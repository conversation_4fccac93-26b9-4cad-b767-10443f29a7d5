"""
Percipio Camera Wrapper - A high-level object-oriented interface for Percipio cameras.

This module provides a simplified, context-manager-based interface for working with
Percipio depth cameras, including image acquisition, point cloud generation, and
camera parameter access.

"""

import os
import sys
import time
from typing import Optional, Union, Any
from dataclasses import dataclass
from pathlib import Path

import cv2
import numpy as np
import open3d as o3d

# Import Percipio SDK
try:
    sys.path.append(os.path.join(os.path.dirname(__file__), 'pcammls_py39'))
    import pcammls
    from pcammls import *
except ImportError as e:
    print(f"Warning: Failed to import Percipio SDK: {e}")
    print("Make sure the pcammls_py39 directory contains the required SDK files.")
    # Define placeholder constants for IDE
    PERCIPIO_STREAM_COLOR = 1
    PERCIPIO_STREAM_DEPTH = 2
    TY_EVENT_DEVICE_OFFLINE = 3
    TY_COMPONENT_RGB_CAM = 4
    TY_BOOL_AUTO_EXPOSURE = 5
    TY_INT_EXPOSURE_TIME = 6

    class PercipioSDK:
        pass

    class image_data:
        def as_nparray(self):
            return np.array([])

    class pointcloud_data_list:
        def as_nparray(self):
            return np.array([])


@dataclass
class CameraInfo:
    """Camera information and calibration data."""
    width: int
    height: int
    intrinsic: Any  # Camera intrinsic matrix
    extrinsic: Any  # Camera extrinsic matrix
    distortion: Any  # Distortion coefficients
    scale_unit: float = 1.0


@dataclass
class CaptureData:
    """Container for captured camera data."""
    color_image: Optional[np.ndarray] = None
    depth_image: Optional[np.ndarray] = None
    aligned_depth_image: Optional[np.ndarray] = None
    point_cloud: Optional[np.ndarray] = None
    aligned_point_cloud: Optional[np.ndarray] = None
    timestamp: float = 0.0


def create_aligned_color_point_cloud(color_image: np.ndarray,
                                    aligned_depth_image: np.ndarray,
                                    color_intrinsic: Any,
                                    depth_scale: float = 1000.0,
                                    use_raw_depth: bool = False) -> Optional[np.ndarray]:
    """
    Create aligned color point cloud with valid points only (no NaN values).

    Args:
        color_image: Undistorted color image (RGB format)
        aligned_depth_image: Depth image aligned to color coordinate system (rendered or raw)
        color_intrinsic: Color camera intrinsic parameters
        depth_scale: Scale factor for depth values (default: 1000.0 for mm to m)
        use_raw_depth: Whether aligned_depth_image contains raw depth values

    Returns:
        Point cloud array with shape (N, 6) containing [x, y, z, r, g, b] or None if error
        Only valid points (no NaN values) are included.
    """
    try:
        # Get image dimensions
        height, width = color_image.shape[:2]
        total_pixels = height * width
        
        # Convert depth image to proper format
        depth_values = aligned_depth_image.astype(np.float32) / depth_scale
        depth_values[depth_values > 3] = 0 
        
        # Extract intrinsic parameters
        intrinsic_matrix = np.array(color_intrinsic).reshape(3, 3)
        fx = intrinsic_matrix[0, 0]
        fy = intrinsic_matrix[1, 1]
        cx = intrinsic_matrix[0, 2]
        cy = intrinsic_matrix[1, 2]
        
        # Create meshgrid of pixel coordinates
        u = np.arange(width)
        v = np.arange(height)
        uu, vv = np.meshgrid(u, v)
        
        # Calculate 3D coordinates for all pixels
        z = depth_values
        x = (uu - cx) * z / fx
        y = (vv - cy) * z / fy
        
        # Reshape to point cloud format
        points = np.stack((x, y, z), axis=-1).reshape(-1, 3)
        
        # Reshape color image to point cloud format and ensure values are in [0, 255]
        colors = color_image.reshape(-1, 3)
        colors = np.clip(colors, 0, 255).astype(np.uint8)
        
        # Combine points and colors
        point_cloud = np.hstack([points, colors.astype(np.float32)])
        
        print(f"Generated {len(point_cloud)} points (matches color image: {total_pixels} pixels)")
        
        return point_cloud


    except Exception as e:
        print(f"Error creating aligned color point cloud: {e}")
        import traceback
        traceback.print_exc()
        return None


class PercipioDeviceEvent(pcammls.DeviceEvent):
    """Device event handler for camera disconnection detection."""

    def __init__(self):
        pcammls.DeviceEvent.__init__(self)
        self.offline = False

    def run(self, handle, eventID):
        _ = handle  # Unused parameter
        if eventID == TY_EVENT_DEVICE_OFFLINE:
            print("=== Event Callback: Device Offline!")
            self.offline = True
        return 0

    def is_offline(self):
        return self.offline


class PercipioCamera:
    """
    High-level interface for Percipio depth cameras.

    This class provides a context manager interface for working with Percipio cameras,
    supporting image acquisition, point cloud generation, and camera parameter access.

    Example:
        with PercipioCamera() as camera:
            data = camera.capture(show=True, save=False)
            if data.color_image is not None:
                print(f"Captured color image: {data.color_image.shape}")
    """

    def __init__(self, device_index: int = 0, color_format_index: int = 0,
                 depth_format_index: int = 1, exposure_time: Optional[int] = None):
        """
        Initialize the camera wrapper.

        Args:
            device_index: Index of the device to use (default: 0 for first device)
            color_format_index: Index of color format to use (default: 2)
            depth_format_index: Index of depth format to use (default: 1)
            exposure_time: Manual exposure time (if None, auto exposure is used)
        """
        self.device_index = device_index
        self.color_format_index = color_format_index
        self.depth_format_index = depth_format_index
        self.exposure_time = exposure_time

        # SDK and device handles
        self.sdk = None
        self.handle = None
        self.event_handler = None

        # Camera information
        self.color_info: Optional[CameraInfo] = None
        self.depth_info: Optional[CameraInfo] = None

        # Stream status
        self.streams_enabled = False
        self.streams_started = False

        # Image buffers
        self._color_buffer = None
        self._depth_buffer = None
        self._depth_render_buffer = None
        self._registration_buffer = None
        self._pointcloud_buffer = None

    def __enter__(self):
        """Context manager entry."""
        self._initialize()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with proper cleanup."""
        _ = exc_type, exc_val, exc_tb  # Unused parameters
        self._cleanup()

    def _initialize(self):
        """Initialize the camera and configure streams."""
        try:
            # Initialize SDK
            self.sdk = PercipioSDK()

            # List and select device
            dev_list = self.sdk.ListDevice()
            if len(dev_list) == 0:
                raise RuntimeError("No Percipio devices found")

            if self.device_index >= len(dev_list):
                raise ValueError(f"Device index {self.device_index} out of range. "
                               f"Found {len(dev_list)} devices.")

            device_info = dev_list[self.device_index]
            print(f"Using device: {device_info.id} - {device_info.iface.id}")

            # Open device
            self.handle = self.sdk.Open(device_info.id)
            if not self.sdk.isValidHandle(self.handle):
                err = self.sdk.TYGetLastErrorCodedescription()
                raise RuntimeError(f"Failed to open device: {err}")

            # Setup event handler
            self.event_handler = PercipioDeviceEvent()
            self.sdk.DeviceRegiststerCallBackEvent(self.event_handler)

            # Configure streams
            self._configure_streams()

            # Set exposure time if specified
            if self.exposure_time is not None:
                self.set_exposure_time(self.exposure_time)


            err = self.sdk.DeviceLoadDefaultParameters(self.handle)
            if err:
                print(f"Warning: Failed to load default parameters: "
                      f"{self.sdk.TYGetLastErrorCodedescription()}")

            # Initialize image buffers
            self._initialize_buffers()

            print("Camera initialized successfully")

        except Exception as e:
            self._cleanup()
            raise RuntimeError(f"Failed to initialize camera: {e}")

    def _configure_streams(self):
        """Configure color and depth streams."""
        # Configure color stream
        color_fmt_list = self.sdk.DeviceStreamFormatDump(self.handle, PERCIPIO_STREAM_COLOR)
        if len(color_fmt_list) > 0:
            if self.color_format_index >= len(color_fmt_list):
                self.color_format_index = 0
                print(f"Warning: Color format index out of range, using index 0")

            selected_fmt = color_fmt_list[self.color_format_index]
            print(f"Color format: {selected_fmt.getDesc()} "
                  f"[{self.sdk.Width(selected_fmt)}x{self.sdk.Height(selected_fmt)}]")

            self.sdk.DeviceStreamFormatConfig(self.handle, PERCIPIO_STREAM_COLOR, selected_fmt)

            # Get color calibration data
            color_calib = self.sdk.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_COLOR)
            self.color_info = CameraInfo(
                width=color_calib.Width(),
                height=color_calib.Height(),
                intrinsic=color_calib.Intrinsic(),
                extrinsic=color_calib.Extrinsic(),
                distortion=color_calib.Distortion()
            )

        # Configure depth stream
        depth_fmt_list = self.sdk.DeviceStreamFormatDump(self.handle, PERCIPIO_STREAM_DEPTH)
        if len(depth_fmt_list) > 0:
            if self.depth_format_index >= len(depth_fmt_list):
                self.depth_format_index = 0
                print(f"Warning: Depth format index out of range, using index 0")

            selected_fmt = depth_fmt_list[self.depth_format_index]
            print(f"Depth format: {selected_fmt.getDesc()} "
                  f"[{self.sdk.Width(selected_fmt)}x{self.sdk.Height(selected_fmt)}]")

            self.sdk.DeviceStreamFormatConfig(self.handle, PERCIPIO_STREAM_DEPTH, selected_fmt)

            # Get depth calibration data and scale unit
            depth_calib = self.sdk.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_DEPTH)
            scale_unit = self.sdk.DeviceReadCalibDepthScaleUnit(self.handle)

            self.depth_info = CameraInfo(
                width=depth_calib.Width(),
                height=depth_calib.Height(),
                intrinsic=depth_calib.Intrinsic(),
                extrinsic=depth_calib.Extrinsic(),
                distortion=depth_calib.Distortion(),
                scale_unit=scale_unit
            )

    def _initialize_buffers(self):
        """Initialize image processing buffers."""
        self._color_buffer = image_data()
        self._color_undistorted_buffer = image_data()
        self._depth_buffer = image_data()
        self._depth_render_buffer = image_data()
        self._registration_buffer = image_data()
        self._pointcloud_buffer = pointcloud_data_list()

    def _cleanup(self):
        """Clean up resources."""
        try:
            if self.streams_started and self.sdk and self.handle:
                self.sdk.DeviceStreamOff(self.handle)
                self.streams_started = False

            if self.handle and self.sdk:
                self.sdk.Close(self.handle)
                self.handle = None

            self.sdk = None
            self.event_handler = None
            self.streams_enabled = False

        except Exception as e:
            print(f"Warning: Error during cleanup: {e}")

    def start_streams(self):
        """Start camera streams."""
        if not self.handle:
            raise RuntimeError("Camera not initialized")

        if self.streams_started:
            return

        # Enable streams
        stream_flags = 0
        if self.color_info:
            stream_flags |= PERCIPIO_STREAM_COLOR
        if self.depth_info:
            stream_flags |= PERCIPIO_STREAM_DEPTH

        if stream_flags == 0:
            raise RuntimeError("No streams available")

        err = self.sdk.DeviceStreamEnable(self.handle, stream_flags)
        if err:
            raise RuntimeError(f"Failed to enable streams: {err}")

        self.streams_enabled = True

        # Start streaming
        self.sdk.DeviceStreamOn(self.handle)
        self.streams_started = True
        print("Camera streams started")

    def stop_streams(self):
        """Stop camera streams."""
        if self.streams_started and self.sdk and self.handle:
            self.sdk.DeviceStreamOff(self.handle)
            self.streams_started = False
            print("Camera streams stopped")

    def is_connected(self) -> bool:
        """Check if camera is still connected."""
        if not self.event_handler:
            return False
        return not self.event_handler.is_offline()

    def capture(self, capture_3d: bool = False, show: bool = False, save_color: bool = False, 
                save_all: bool = False, timeout_ms: int = 2000) -> CaptureData:
        """
        Capture images and point cloud data.

        Args:
            capture_3d: If True, captures all data (color, depth, point clouds).
                       If False, captures only color image and aligned depth image.
            show: Whether to display images using OpenCV
            save_color: Whether to save only color image
            save_all: Whether to save all captured data (images and point clouds)
            timeout_ms: Timeout for frame capture in milliseconds

        Returns:
            CaptureData object containing captured images and point cloud
        """
        if not self.streams_started:
            self.start_streams()

        if not self.is_connected():
            raise RuntimeError("Camera disconnected")

        # Capture frames
        image_list = self.sdk.DeviceStreamRead(self.handle, timeout_ms)
        if len(image_list) == 0:
            raise RuntimeError("No frames captured within timeout")

        # Initialize capture data
        capture_data = CaptureData(timestamp=time.time())

        # Process captured frames
        color_frame = None
        depth_frame = None

        for frame in image_list:
            if frame.streamID == PERCIPIO_STREAM_COLOR:
                color_frame = frame
            elif frame.streamID == PERCIPIO_STREAM_DEPTH:
                depth_frame = frame

        # Process color image
        if color_frame is not None and self.color_info is not None:
            # Decode color image
            self.sdk.DeviceStreamImageDecode(color_frame, self._color_buffer)

            # Apply undistortion to color image
            color_calib = self.sdk.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_COLOR)
            self.sdk.DeviceStreamDoUndistortion(
                color_calib, self._color_buffer, self._color_undistorted_buffer
            )

            # Store undistorted color image
            capture_data.color_image = self._color_undistorted_buffer.as_nparray().copy()

        # Process depth image
        if depth_frame is not None and self.depth_info is not None:
            if capture_3d:
                # Full depth processing: render depth image and generate point cloud
                self.sdk.DeviceStreamDepthRender(depth_frame, self._depth_render_buffer)
                capture_data.depth_image = self._depth_render_buffer.as_nparray().copy()

                # Generate point cloud
                self.sdk.DeviceStreamMapDepthImageToPoint3D(
                    depth_frame,
                    self.sdk.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_DEPTH),
                    self.depth_info.scale_unit,
                    self._pointcloud_buffer
                )
                capture_data.point_cloud = self._pointcloud_buffer.as_nparray().copy()

            # Always process aligned depth if color frame is available
            if color_frame is not None and self.color_info is not None:
                self.sdk.DeviceStreamMapDepthImageToColorCoordinate(
                    self.sdk.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_DEPTH),
                    depth_frame,
                    self.depth_info.scale_unit,
                    self.sdk.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_COLOR),
                    color_frame.width,
                    color_frame.height,
                    self._registration_buffer
                )

                # Get aligned depth for visualization/processing
                registration_array = self._registration_buffer.as_nparray().copy()
                registration_array = np.squeeze(registration_array)
                capture_data.aligned_depth_image = registration_array

                # Generate aligned color point cloud only if capture_3d is True
                if capture_3d and capture_data.color_image is not None:
                    print("color_image is not None, ready to create aligned point cloud")
                    color_calib = self.sdk.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_COLOR)

                    capture_data.aligned_point_cloud = create_aligned_color_point_cloud(
                        capture_data.color_image,
                        capture_data.aligned_depth_image,
                        color_calib.Intrinsic(),
                        depth_scale=1000,
                        use_raw_depth=True
                    )
                    
        # Display images if requested
        if show:
            self._display_images(capture_data)

        # Save data if requested
        if save_color:
            self._save_color_only(capture_data)
        elif save_all:
            self._save_all_data(capture_data)

        return capture_data

    def _display_images(self, data: CaptureData):
        """Display captured images using OpenCV."""
        if data.color_image is not None:
            cv2.imshow("Color Image", data.color_image)

        if data.depth_image is not None:
            cv2.imshow("Depth Image", data.depth_image)

        cv2.waitKey(1)  # Non-blocking wait to update windows

    def _save_color_only(self, data: CaptureData, base_path: str = "Data/capture-2025-08-27_01"):
        """Save only color image to file."""
        timestamp_str = str(int(data.timestamp))
        base_path = Path(base_path)
        base_path.mkdir(exist_ok=True)

        if data.color_image is not None:
            color_path = base_path / f"color_{timestamp_str}.png"
            cv2.imwrite(str(color_path), data.color_image)
            print(f"Saved color image: {color_path}")

    def _save_all_data(self, data: CaptureData, base_path: str = "Data/capture-2025-08-27_01"):
        """Save all captured data to files."""
        timestamp_str = str(int(data.timestamp))
        base_path = Path(base_path)
        base_path.mkdir(exist_ok=True)

        if data.color_image is not None:
            color_path = base_path / f"color_{timestamp_str}.png"
            cv2.imwrite(str(color_path), data.color_image)
            print(f"Saved color image: {color_path}")

        if data.depth_image is not None:
            depth_path = base_path / f"depth_{timestamp_str}.png"
            cv2.imwrite(str(depth_path), data.depth_image)
            print(f"Saved depth image: {depth_path}")

        if data.aligned_depth_image is not None:
            aligned_path = base_path / f"aligned_depth_{timestamp_str}.png"
            cv2.imwrite(str(aligned_path), data.aligned_depth_image)
            print(f"Saved aligned depth image: {aligned_path}")

        if data.point_cloud is not None:
            print(f"point_cloud shape: {data.point_cloud.shape}")
            print(f"point_cloud dtype: {data.point_cloud.dtype}")
            
            points = data.point_cloud.reshape(-1, 3) / 1000
        
            valid_mask = ~np.isnan(points).any(axis=1)
            filtered_points = points[valid_mask]
            print(f"Original points: {points.shape[0]}, After filtering NaN: {filtered_points.shape[0]}")
            
            if filtered_points.shape[0] == 0:
                print("No valid points after filtering NaN values. Skipping PLY save.")
                return
            
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(filtered_points)
            
            pc_path = base_path / f"point_cloud_{timestamp_str}.ply"
            o3d.io.write_point_cloud(str(pc_path), pcd, write_ascii=True)
            print(f"Saved filtered point cloud as ASCII: {pc_path}")

        if data.aligned_point_cloud is not None:
            print(f"aligned_point_cloud shape: {data.aligned_point_cloud.shape}")
            
            points = data.aligned_point_cloud[:, :3]  
            colors = data.aligned_point_cloud[:, 3:6] 
            colors = colors.astype(np.float32) / 255.0
            
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)
            pcd.colors = o3d.utility.Vector3dVector(colors)

            pc_path = base_path / f"aligned_pointcloud_{timestamp_str}.ply"
            o3d.io.write_point_cloud(str(pc_path), pcd)
            print(f"Saved aligned point cloud: {pc_path}")

    def set_exposure_time(self, exposure_time: int) -> bool:
        """
        Set manual exposure time for color camera.
        Auto exposure will be disabled automatically.
        
        Args:
            exposure_time: Exposure time value
            
        Returns:
            True if successful, False otherwise
        """
        if not self.handle or not self.color_info:
            print("Camera not initialized or color stream not available")
            return False
            
        try:
            # Disable auto exposure
            aec_param = self.sdk.DevParamFromBool(False)
            err = self.sdk.DeviceSetParameter(
                self.handle, TY_COMPONENT_RGB_CAM, TY_BOOL_AUTO_EXPOSURE, aec_param
            )
            if err != 0:
                print(f"Warning: Failed to disable auto exposure: error {err}")
                    
            # Set exposure time
            exp_param = self.sdk.DevParamFromInt(exposure_time)
            err = self.sdk.DeviceSetParameter(
                self.handle, TY_COMPONENT_RGB_CAM, TY_INT_EXPOSURE_TIME, exp_param
            )
            if err == 0:
                print(f"Exposure time set to {exposure_time}")
                return True
            else:
                print(f"Failed to set exposure time: error {err}")
                return False
        except Exception as e:
            print(f"Error setting exposure time: {e}")
            return False

    def print_camera_info(self):
        """Print detailed camera information."""
        print("\n=== Camera Information ===")

        if self.color_info:
            print(f"\nColor Camera:")
            print(f"  Resolution: {self.color_info.width}x{self.color_info.height}")
            print(f"  Intrinsic: {self.color_info.intrinsic}")
            print(f"  Extrinsic: {self.color_info.extrinsic}")
            print(f"  Distortion: {self.color_info.distortion}")

        if self.depth_info:
            print(f"\nDepth Camera:")
            print(f"  Resolution: {self.depth_info.width}x{self.depth_info.height}")
            print(f"  Scale Unit: {self.depth_info.scale_unit}")
            print(f"  Intrinsic: {self.depth_info.intrinsic}")
            print(f"  Extrinsic: {self.depth_info.extrinsic}")
            print(f"  Distortion: {self.depth_info.distortion}")


def demo_basic_usage():
    """Demonstrate basic camera usage."""
    print("=== Percipio Camera Demo ===")

    try:
        with PercipioCamera() as camera:
            # Print camera information
            camera.print_camera_info()

            print("\nCapturing frames... Press 'q' to quit, '3' to capture one frame with 3D data")

            while True:
                # Capture data - start with 2D only for faster processing
                data = camera.capture(capture_3d=False, show=True, save_color=False, save_all=False)

                # Print capture info
                if data.color_image is not None:
                    print(f"Color image (undistorted) shape: {data.color_image.shape}")
                if data.depth_image is not None:
                    print(f"Depth image shape: {data.depth_image.shape}")
                if data.aligned_depth_image is not None:
                    print(f"Aligned depth image shape: {data.aligned_depth_image.shape}")
                if data.point_cloud is not None:
                    print(f"Point cloud shape: {data.point_cloud.shape}")
                if data.aligned_point_cloud is not None:
                    print(f"Aligned color point cloud shape: {data.aligned_point_cloud.shape}")
                else:
                    print("aligned point cloud is None (capture_3d=False)")

                # Check for quit or toggle 3D capture
                key = cv2.waitKey(30) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('3'):  # Press '3' to toggle 3D capture for one frame
                    print("Capturing with 3D data...")
                    data_3d = camera.capture(capture_3d=True, show=True, save_color=False, save_all=False)
                    if data_3d.aligned_point_cloud is not None:
                        print(f"3D Aligned color point cloud shape: {data_3d.aligned_point_cloud.shape}")
                    else:
                        print("3D aligned point cloud is None")

    except Exception as e:
        print(f"Error: {e}")
    finally:
        cv2.destroyAllWindows()


def demo_save_data():
    """Demonstrate saving captured data."""
    print("=== Save Data Demo ===")

    try:
        # Example with manual exposure time and save_all
        with PercipioCamera(exposure_time=60000) as camera:
            camera.print_camera_info()
            print("Capturing and saving all data with 3D processing...")

            # Capture and save all data with 3D processing enabled
            _ = camera.capture(capture_3d=True, show=False, save_all=True)

            print("All data saved successfully!")

    except Exception as e:
        print(f"Error: {e}")


def demo_save_color_only():
    """Demonstrate saving only color image."""
    print("=== Save Color Only Demo ===")

    try:
        # Example with manual exposure time and save_color
        with PercipioCamera(exposure_time=5000) as camera:
            camera.print_camera_info()
            print("Capturing and saving color image only (2D mode)...")

            # Capture and save only color image (no 3D processing needed)
            _ = camera.capture(capture_3d=False, show=False, save_color=True)

            print("Color image saved successfully!")

    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    # Run basic demo
    # demo_basic_usage()
    demo_save_data()