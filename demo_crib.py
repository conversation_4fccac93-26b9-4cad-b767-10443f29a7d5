from robo_ctl.ur_robot_controller import URRobotController
import numpy as np

with URRobotController("192.168.25.15") as robot:
    cur_pose = robot.get_tcp_pose()
   # point = np.array([0.7493695494882551, -0.26733617074079474, 0.4452624764026576, -2.221424967904871, 2.2213712950252873, 8.540656958900571e-05])
   # robot.move_linear(point, 0.1, 0.01)
    # pt_5 = np.array([0.7689189204813823, -0.19997825073574516, 0.28373516643973645, -2.221488364887782, 2.221379873168846, -4.052161942145969e-05])
    # robot.move_linear(pt_5, 0.05, 0.05)
    print(f"cur_pose: {cur_pose}")
    robot.disconnect()