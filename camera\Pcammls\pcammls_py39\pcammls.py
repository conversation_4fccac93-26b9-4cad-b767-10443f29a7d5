# This file was automatically generated by SWIG (http://www.swig.org).
# Version 4.0.2
#
# Do not make changes to this file unless you know what you are doing--modify
# the SWIG interface file instead.

from sys import version_info as _swig_python_version_info

if _swig_python_version_info < (2, 7, 0):
    raise RuntimeError("Python 2.7 or later required")

# Import the low-level C/C++ module
if __package__ or "." in __name__:
    from . import _pcammls
else:
    import _pcammls

try:
    import builtins as __builtin__
except ImportError:
    import __builtin__


def _swig_repr(self):
    try:
        strthis = "proxy of " + self.this.__repr__()
    except __builtin__.Exception:
        strthis = ""
    return "<%s.%s; %s >" % (
        self.__class__.__module__,
        self.__class__.__name__,
        strthis,
    )


def _swig_setattr_nondynamic_instance_variable(set):
    def set_instance_attr(self, name, value):
        if name == "thisown":
            self.this.own(value)
        elif name == "this":
            set(self, name, value)
        elif hasattr(self, name) and isinstance(getattr(type(self), name), property):
            set(self, name, value)
        else:
            raise AttributeError("You cannot add instance attributes to %s" % self)

    return set_instance_attr


def _swig_setattr_nondynamic_class_variable(set):
    def set_class_attr(cls, name, value):
        if hasattr(cls, name) and not isinstance(getattr(cls, name), property):
            set(cls, name, value)
        else:
            raise AttributeError("You cannot add class attributes to %s" % cls)

    return set_class_attr


def _swig_add_metaclass(metaclass):
    """Class decorator for adding a metaclass to a SWIG wrapped class - a slimmed down version of six.add_metaclass"""

    def wrapper(cls):
        return metaclass(cls.__name__, cls.__bases__, cls.__dict__.copy())

    return wrapper


class _SwigNonDynamicMeta(type):
    """Meta class to enforce nondynamic attributes (no new attributes) for a class"""

    __setattr__ = _swig_setattr_nondynamic_class_variable(type.__setattr__)


import weakref

TY_STATIC_LIB = _pcammls.TY_STATIC_LIB


class SwigPyIterator(object):
    r"""Proxy of C++ swig::SwigPyIterator class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")

    __repr__ = _swig_repr
    __swig_destroy__ = _pcammls.delete_SwigPyIterator

    def value(self):
        r"""value(self) -> PyObject *"""
        return _pcammls.SwigPyIterator_value(self)

    def incr(self, n=1):
        r"""
        incr(self, n=1) -> SwigPyIterator

        Parameters
        ----------
        n: size_t

        """
        return _pcammls.SwigPyIterator_incr(self, n)

    def decr(self, n=1):
        r"""
        decr(self, n=1) -> SwigPyIterator

        Parameters
        ----------
        n: size_t

        """
        return _pcammls.SwigPyIterator_decr(self, n)

    def distance(self, x):
        r"""
        distance(self, x) -> ptrdiff_t

        Parameters
        ----------
        x: swig::SwigPyIterator const &

        """
        return _pcammls.SwigPyIterator_distance(self, x)

    def equal(self, x):
        r"""
        equal(self, x) -> bool

        Parameters
        ----------
        x: swig::SwigPyIterator const &

        """
        return _pcammls.SwigPyIterator_equal(self, x)

    def copy(self):
        r"""copy(self) -> SwigPyIterator"""
        return _pcammls.SwigPyIterator_copy(self)

    def next(self):
        r"""next(self) -> PyObject *"""
        return _pcammls.SwigPyIterator_next(self)

    def __next__(self):
        r"""__next__(self) -> PyObject *"""
        return _pcammls.SwigPyIterator___next__(self)

    def previous(self):
        r"""previous(self) -> PyObject *"""
        return _pcammls.SwigPyIterator_previous(self)

    def advance(self, n):
        r"""
        advance(self, n) -> SwigPyIterator

        Parameters
        ----------
        n: ptrdiff_t

        """
        return _pcammls.SwigPyIterator_advance(self, n)

    def __eq__(self, x):
        r"""
        __eq__(self, x) -> bool

        Parameters
        ----------
        x: swig::SwigPyIterator const &

        """
        return _pcammls.SwigPyIterator___eq__(self, x)

    def __ne__(self, x):
        r"""
        __ne__(self, x) -> bool

        Parameters
        ----------
        x: swig::SwigPyIterator const &

        """
        return _pcammls.SwigPyIterator___ne__(self, x)

    def __iadd__(self, n):
        r"""
        __iadd__(self, n) -> SwigPyIterator

        Parameters
        ----------
        n: ptrdiff_t

        """
        return _pcammls.SwigPyIterator___iadd__(self, n)

    def __isub__(self, n):
        r"""
        __isub__(self, n) -> SwigPyIterator

        Parameters
        ----------
        n: ptrdiff_t

        """
        return _pcammls.SwigPyIterator___isub__(self, n)

    def __add__(self, n):
        r"""
        __add__(self, n) -> SwigPyIterator

        Parameters
        ----------
        n: ptrdiff_t

        """
        return _pcammls.SwigPyIterator___add__(self, n)

    def __sub__(self, *args):
        r"""
        __sub__(self, n) -> SwigPyIterator

        Parameters
        ----------
        n: ptrdiff_t

        __sub__(self, x) -> ptrdiff_t

        Parameters
        ----------
        x: swig::SwigPyIterator const &

        """
        return _pcammls.SwigPyIterator___sub__(self, *args)

    def __iter__(self):
        return self


# Register SwigPyIterator in _pcammls:
_pcammls.SwigPyIterator_swigregister(SwigPyIterator)


class ty_dev_info_vector(object):
    r"""Proxy of C++ std::vector< TY_DEVICE_BASE_INFO > class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def iterator(self):
        r"""iterator(self) -> SwigPyIterator"""
        return _pcammls.ty_dev_info_vector_iterator(self)

    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        r"""__nonzero__(self) -> bool"""
        return _pcammls.ty_dev_info_vector___nonzero__(self)

    def __bool__(self):
        r"""__bool__(self) -> bool"""
        return _pcammls.ty_dev_info_vector___bool__(self)

    def __len__(self):
        r"""__len__(self) -> std::vector< TY_DEVICE_BASE_INFO >::size_type"""
        return _pcammls.ty_dev_info_vector___len__(self)

    def __getslice__(self, i, j):
        r"""
        __getslice__(self, i, j) -> ty_dev_info_vector

        Parameters
        ----------
        i: std::vector< TY_DEVICE_BASE_INFO >::difference_type
        j: std::vector< TY_DEVICE_BASE_INFO >::difference_type

        """
        return _pcammls.ty_dev_info_vector___getslice__(self, i, j)

    def __setslice__(self, *args):
        r"""
        __setslice__(self, i, j)

        Parameters
        ----------
        i: std::vector< TY_DEVICE_BASE_INFO >::difference_type
        j: std::vector< TY_DEVICE_BASE_INFO >::difference_type

        __setslice__(self, i, j, v)

        Parameters
        ----------
        i: std::vector< TY_DEVICE_BASE_INFO >::difference_type
        j: std::vector< TY_DEVICE_BASE_INFO >::difference_type
        v: std::vector< TY_DEVICE_BASE_INFO,std::allocator< TY_DEVICE_BASE_INFO > > const &

        """
        return _pcammls.ty_dev_info_vector___setslice__(self, *args)

    def __delslice__(self, i, j):
        r"""
        __delslice__(self, i, j)

        Parameters
        ----------
        i: std::vector< TY_DEVICE_BASE_INFO >::difference_type
        j: std::vector< TY_DEVICE_BASE_INFO >::difference_type

        """
        return _pcammls.ty_dev_info_vector___delslice__(self, i, j)

    def __delitem__(self, *args):
        r"""
        __delitem__(self, i)

        Parameters
        ----------
        i: std::vector< TY_DEVICE_BASE_INFO >::difference_type

        __delitem__(self, slice)

        Parameters
        ----------
        slice: PySliceObject *

        """
        return _pcammls.ty_dev_info_vector___delitem__(self, *args)

    def __getitem__(self, *args):
        r"""
        __getitem__(self, slice) -> ty_dev_info_vector

        Parameters
        ----------
        slice: PySliceObject *

        __getitem__(self, i) -> TY_DEVICE_BASE_INFO

        Parameters
        ----------
        i: std::vector< TY_DEVICE_BASE_INFO >::difference_type

        """
        return _pcammls.ty_dev_info_vector___getitem__(self, *args)

    def __setitem__(self, *args):
        r"""
        __setitem__(self, slice, v)

        Parameters
        ----------
        slice: PySliceObject *
        v: std::vector< TY_DEVICE_BASE_INFO,std::allocator< TY_DEVICE_BASE_INFO > > const &

        __setitem__(self, slice)

        Parameters
        ----------
        slice: PySliceObject *

        __setitem__(self, i, x)

        Parameters
        ----------
        i: std::vector< TY_DEVICE_BASE_INFO >::difference_type
        x: std::vector< TY_DEVICE_BASE_INFO >::value_type const &

        """
        return _pcammls.ty_dev_info_vector___setitem__(self, *args)

    def pop(self):
        r"""pop(self) -> TY_DEVICE_BASE_INFO"""
        return _pcammls.ty_dev_info_vector_pop(self)

    def append(self, x):
        r"""
        append(self, x)

        Parameters
        ----------
        x: std::vector< TY_DEVICE_BASE_INFO >::value_type const &

        """
        return _pcammls.ty_dev_info_vector_append(self, x)

    def empty(self):
        r"""empty(self) -> bool"""
        return _pcammls.ty_dev_info_vector_empty(self)

    def size(self):
        r"""size(self) -> std::vector< TY_DEVICE_BASE_INFO >::size_type"""
        return _pcammls.ty_dev_info_vector_size(self)

    def swap(self, v):
        r"""
        swap(self, v)

        Parameters
        ----------
        v: std::vector< TY_DEVICE_BASE_INFO > &

        """
        return _pcammls.ty_dev_info_vector_swap(self, v)

    def begin(self):
        r"""begin(self) -> std::vector< TY_DEVICE_BASE_INFO >::iterator"""
        return _pcammls.ty_dev_info_vector_begin(self)

    def end(self):
        r"""end(self) -> std::vector< TY_DEVICE_BASE_INFO >::iterator"""
        return _pcammls.ty_dev_info_vector_end(self)

    def rbegin(self):
        r"""rbegin(self) -> std::vector< TY_DEVICE_BASE_INFO >::reverse_iterator"""
        return _pcammls.ty_dev_info_vector_rbegin(self)

    def rend(self):
        r"""rend(self) -> std::vector< TY_DEVICE_BASE_INFO >::reverse_iterator"""
        return _pcammls.ty_dev_info_vector_rend(self)

    def clear(self):
        r"""clear(self)"""
        return _pcammls.ty_dev_info_vector_clear(self)

    def get_allocator(self):
        r"""get_allocator(self) -> std::vector< TY_DEVICE_BASE_INFO >::allocator_type"""
        return _pcammls.ty_dev_info_vector_get_allocator(self)

    def pop_back(self):
        r"""pop_back(self)"""
        return _pcammls.ty_dev_info_vector_pop_back(self)

    def erase(self, *args):
        r"""
        erase(self, pos) -> std::vector< TY_DEVICE_BASE_INFO >::iterator

        Parameters
        ----------
        pos: std::vector< TY_DEVICE_BASE_INFO >::iterator

        erase(self, first, last) -> std::vector< TY_DEVICE_BASE_INFO >::iterator

        Parameters
        ----------
        first: std::vector< TY_DEVICE_BASE_INFO >::iterator
        last: std::vector< TY_DEVICE_BASE_INFO >::iterator

        """
        return _pcammls.ty_dev_info_vector_erase(self, *args)

    def __init__(self, *args):
        r"""
        __init__(self) -> ty_dev_info_vector
        __init__(self, other) -> ty_dev_info_vector

        Parameters
        ----------
        other: std::vector< TY_DEVICE_BASE_INFO > const &

        __init__(self, size) -> ty_dev_info_vector

        Parameters
        ----------
        size: std::vector< TY_DEVICE_BASE_INFO >::size_type

        __init__(self, size, value) -> ty_dev_info_vector

        Parameters
        ----------
        size: std::vector< TY_DEVICE_BASE_INFO >::size_type
        value: std::vector< TY_DEVICE_BASE_INFO >::value_type const &

        """
        _pcammls.ty_dev_info_vector_swiginit(
            self, _pcammls.new_ty_dev_info_vector(*args)
        )

    def push_back(self, x):
        r"""
        push_back(self, x)

        Parameters
        ----------
        x: std::vector< TY_DEVICE_BASE_INFO >::value_type const &

        """
        return _pcammls.ty_dev_info_vector_push_back(self, x)

    def front(self):
        r"""front(self) -> TY_DEVICE_BASE_INFO"""
        return _pcammls.ty_dev_info_vector_front(self)

    def back(self):
        r"""back(self) -> TY_DEVICE_BASE_INFO"""
        return _pcammls.ty_dev_info_vector_back(self)

    def assign(self, n, x):
        r"""
        assign(self, n, x)

        Parameters
        ----------
        n: std::vector< TY_DEVICE_BASE_INFO >::size_type
        x: std::vector< TY_DEVICE_BASE_INFO >::value_type const &

        """
        return _pcammls.ty_dev_info_vector_assign(self, n, x)

    def resize(self, *args):
        r"""
        resize(self, new_size)

        Parameters
        ----------
        new_size: std::vector< TY_DEVICE_BASE_INFO >::size_type

        resize(self, new_size, x)

        Parameters
        ----------
        new_size: std::vector< TY_DEVICE_BASE_INFO >::size_type
        x: std::vector< TY_DEVICE_BASE_INFO >::value_type const &

        """
        return _pcammls.ty_dev_info_vector_resize(self, *args)

    def insert(self, *args):
        r"""
        insert(self, pos, x) -> std::vector< TY_DEVICE_BASE_INFO >::iterator

        Parameters
        ----------
        pos: std::vector< TY_DEVICE_BASE_INFO >::iterator
        x: std::vector< TY_DEVICE_BASE_INFO >::value_type const &

        insert(self, pos, n, x)

        Parameters
        ----------
        pos: std::vector< TY_DEVICE_BASE_INFO >::iterator
        n: std::vector< TY_DEVICE_BASE_INFO >::size_type
        x: std::vector< TY_DEVICE_BASE_INFO >::value_type const &

        """
        return _pcammls.ty_dev_info_vector_insert(self, *args)

    def reserve(self, n):
        r"""
        reserve(self, n)

        Parameters
        ----------
        n: std::vector< TY_DEVICE_BASE_INFO >::size_type

        """
        return _pcammls.ty_dev_info_vector_reserve(self, n)

    def capacity(self):
        r"""capacity(self) -> std::vector< TY_DEVICE_BASE_INFO >::size_type"""
        return _pcammls.ty_dev_info_vector_capacity(self)

    __swig_destroy__ = _pcammls.delete_ty_dev_info_vector


# Register ty_dev_info_vector in _pcammls:
_pcammls.ty_dev_info_vector_swigregister(ty_dev_info_vector)


class ty_enum_vector(object):
    r"""Proxy of C++ std::vector< TY_ENUM_ENTRY > class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def iterator(self):
        r"""iterator(self) -> SwigPyIterator"""
        return _pcammls.ty_enum_vector_iterator(self)

    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        r"""__nonzero__(self) -> bool"""
        return _pcammls.ty_enum_vector___nonzero__(self)

    def __bool__(self):
        r"""__bool__(self) -> bool"""
        return _pcammls.ty_enum_vector___bool__(self)

    def __len__(self):
        r"""__len__(self) -> std::vector< TY_ENUM_ENTRY >::size_type"""
        return _pcammls.ty_enum_vector___len__(self)

    def __getslice__(self, i, j):
        r"""
        __getslice__(self, i, j) -> ty_enum_vector

        Parameters
        ----------
        i: std::vector< TY_ENUM_ENTRY >::difference_type
        j: std::vector< TY_ENUM_ENTRY >::difference_type

        """
        return _pcammls.ty_enum_vector___getslice__(self, i, j)

    def __setslice__(self, *args):
        r"""
        __setslice__(self, i, j)

        Parameters
        ----------
        i: std::vector< TY_ENUM_ENTRY >::difference_type
        j: std::vector< TY_ENUM_ENTRY >::difference_type

        __setslice__(self, i, j, v)

        Parameters
        ----------
        i: std::vector< TY_ENUM_ENTRY >::difference_type
        j: std::vector< TY_ENUM_ENTRY >::difference_type
        v: std::vector< TY_ENUM_ENTRY,std::allocator< TY_ENUM_ENTRY > > const &

        """
        return _pcammls.ty_enum_vector___setslice__(self, *args)

    def __delslice__(self, i, j):
        r"""
        __delslice__(self, i, j)

        Parameters
        ----------
        i: std::vector< TY_ENUM_ENTRY >::difference_type
        j: std::vector< TY_ENUM_ENTRY >::difference_type

        """
        return _pcammls.ty_enum_vector___delslice__(self, i, j)

    def __delitem__(self, *args):
        r"""
        __delitem__(self, i)

        Parameters
        ----------
        i: std::vector< TY_ENUM_ENTRY >::difference_type

        __delitem__(self, slice)

        Parameters
        ----------
        slice: PySliceObject *

        """
        return _pcammls.ty_enum_vector___delitem__(self, *args)

    def __getitem__(self, *args):
        r"""
        __getitem__(self, slice) -> ty_enum_vector

        Parameters
        ----------
        slice: PySliceObject *

        __getitem__(self, i) -> TY_ENUM_ENTRY

        Parameters
        ----------
        i: std::vector< TY_ENUM_ENTRY >::difference_type

        """
        return _pcammls.ty_enum_vector___getitem__(self, *args)

    def __setitem__(self, *args):
        r"""
        __setitem__(self, slice, v)

        Parameters
        ----------
        slice: PySliceObject *
        v: std::vector< TY_ENUM_ENTRY,std::allocator< TY_ENUM_ENTRY > > const &

        __setitem__(self, slice)

        Parameters
        ----------
        slice: PySliceObject *

        __setitem__(self, i, x)

        Parameters
        ----------
        i: std::vector< TY_ENUM_ENTRY >::difference_type
        x: std::vector< TY_ENUM_ENTRY >::value_type const &

        """
        return _pcammls.ty_enum_vector___setitem__(self, *args)

    def pop(self):
        r"""pop(self) -> TY_ENUM_ENTRY"""
        return _pcammls.ty_enum_vector_pop(self)

    def append(self, x):
        r"""
        append(self, x)

        Parameters
        ----------
        x: std::vector< TY_ENUM_ENTRY >::value_type const &

        """
        return _pcammls.ty_enum_vector_append(self, x)

    def empty(self):
        r"""empty(self) -> bool"""
        return _pcammls.ty_enum_vector_empty(self)

    def size(self):
        r"""size(self) -> std::vector< TY_ENUM_ENTRY >::size_type"""
        return _pcammls.ty_enum_vector_size(self)

    def swap(self, v):
        r"""
        swap(self, v)

        Parameters
        ----------
        v: std::vector< TY_ENUM_ENTRY > &

        """
        return _pcammls.ty_enum_vector_swap(self, v)

    def begin(self):
        r"""begin(self) -> std::vector< TY_ENUM_ENTRY >::iterator"""
        return _pcammls.ty_enum_vector_begin(self)

    def end(self):
        r"""end(self) -> std::vector< TY_ENUM_ENTRY >::iterator"""
        return _pcammls.ty_enum_vector_end(self)

    def rbegin(self):
        r"""rbegin(self) -> std::vector< TY_ENUM_ENTRY >::reverse_iterator"""
        return _pcammls.ty_enum_vector_rbegin(self)

    def rend(self):
        r"""rend(self) -> std::vector< TY_ENUM_ENTRY >::reverse_iterator"""
        return _pcammls.ty_enum_vector_rend(self)

    def clear(self):
        r"""clear(self)"""
        return _pcammls.ty_enum_vector_clear(self)

    def get_allocator(self):
        r"""get_allocator(self) -> std::vector< TY_ENUM_ENTRY >::allocator_type"""
        return _pcammls.ty_enum_vector_get_allocator(self)

    def pop_back(self):
        r"""pop_back(self)"""
        return _pcammls.ty_enum_vector_pop_back(self)

    def erase(self, *args):
        r"""
        erase(self, pos) -> std::vector< TY_ENUM_ENTRY >::iterator

        Parameters
        ----------
        pos: std::vector< TY_ENUM_ENTRY >::iterator

        erase(self, first, last) -> std::vector< TY_ENUM_ENTRY >::iterator

        Parameters
        ----------
        first: std::vector< TY_ENUM_ENTRY >::iterator
        last: std::vector< TY_ENUM_ENTRY >::iterator

        """
        return _pcammls.ty_enum_vector_erase(self, *args)

    def __init__(self, *args):
        r"""
        __init__(self) -> ty_enum_vector
        __init__(self, other) -> ty_enum_vector

        Parameters
        ----------
        other: std::vector< TY_ENUM_ENTRY > const &

        __init__(self, size) -> ty_enum_vector

        Parameters
        ----------
        size: std::vector< TY_ENUM_ENTRY >::size_type

        __init__(self, size, value) -> ty_enum_vector

        Parameters
        ----------
        size: std::vector< TY_ENUM_ENTRY >::size_type
        value: std::vector< TY_ENUM_ENTRY >::value_type const &

        """
        _pcammls.ty_enum_vector_swiginit(self, _pcammls.new_ty_enum_vector(*args))

    def push_back(self, x):
        r"""
        push_back(self, x)

        Parameters
        ----------
        x: std::vector< TY_ENUM_ENTRY >::value_type const &

        """
        return _pcammls.ty_enum_vector_push_back(self, x)

    def front(self):
        r"""front(self) -> TY_ENUM_ENTRY"""
        return _pcammls.ty_enum_vector_front(self)

    def back(self):
        r"""back(self) -> TY_ENUM_ENTRY"""
        return _pcammls.ty_enum_vector_back(self)

    def assign(self, n, x):
        r"""
        assign(self, n, x)

        Parameters
        ----------
        n: std::vector< TY_ENUM_ENTRY >::size_type
        x: std::vector< TY_ENUM_ENTRY >::value_type const &

        """
        return _pcammls.ty_enum_vector_assign(self, n, x)

    def resize(self, *args):
        r"""
        resize(self, new_size)

        Parameters
        ----------
        new_size: std::vector< TY_ENUM_ENTRY >::size_type

        resize(self, new_size, x)

        Parameters
        ----------
        new_size: std::vector< TY_ENUM_ENTRY >::size_type
        x: std::vector< TY_ENUM_ENTRY >::value_type const &

        """
        return _pcammls.ty_enum_vector_resize(self, *args)

    def insert(self, *args):
        r"""
        insert(self, pos, x) -> std::vector< TY_ENUM_ENTRY >::iterator

        Parameters
        ----------
        pos: std::vector< TY_ENUM_ENTRY >::iterator
        x: std::vector< TY_ENUM_ENTRY >::value_type const &

        insert(self, pos, n, x)

        Parameters
        ----------
        pos: std::vector< TY_ENUM_ENTRY >::iterator
        n: std::vector< TY_ENUM_ENTRY >::size_type
        x: std::vector< TY_ENUM_ENTRY >::value_type const &

        """
        return _pcammls.ty_enum_vector_insert(self, *args)

    def reserve(self, n):
        r"""
        reserve(self, n)

        Parameters
        ----------
        n: std::vector< TY_ENUM_ENTRY >::size_type

        """
        return _pcammls.ty_enum_vector_reserve(self, n)

    def capacity(self):
        r"""capacity(self) -> std::vector< TY_ENUM_ENTRY >::size_type"""
        return _pcammls.ty_enum_vector_capacity(self)

    __swig_destroy__ = _pcammls.delete_ty_enum_vector


# Register ty_enum_vector in _pcammls:
_pcammls.ty_enum_vector_swigregister(ty_enum_vector)


class ty_image_data_vector(object):
    r"""Proxy of C++ std::vector< image_data > class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def iterator(self):
        r"""iterator(self) -> SwigPyIterator"""
        return _pcammls.ty_image_data_vector_iterator(self)

    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        r"""__nonzero__(self) -> bool"""
        return _pcammls.ty_image_data_vector___nonzero__(self)

    def __bool__(self):
        r"""__bool__(self) -> bool"""
        return _pcammls.ty_image_data_vector___bool__(self)

    def __len__(self):
        r"""__len__(self) -> std::vector< image_data >::size_type"""
        return _pcammls.ty_image_data_vector___len__(self)

    def __getslice__(self, i, j):
        r"""
        __getslice__(self, i, j) -> ty_image_data_vector

        Parameters
        ----------
        i: std::vector< image_data >::difference_type
        j: std::vector< image_data >::difference_type

        """
        return _pcammls.ty_image_data_vector___getslice__(self, i, j)

    def __setslice__(self, *args):
        r"""
        __setslice__(self, i, j)

        Parameters
        ----------
        i: std::vector< image_data >::difference_type
        j: std::vector< image_data >::difference_type

        __setslice__(self, i, j, v)

        Parameters
        ----------
        i: std::vector< image_data >::difference_type
        j: std::vector< image_data >::difference_type
        v: std::vector< image_data,std::allocator< image_data > > const &

        """
        return _pcammls.ty_image_data_vector___setslice__(self, *args)

    def __delslice__(self, i, j):
        r"""
        __delslice__(self, i, j)

        Parameters
        ----------
        i: std::vector< image_data >::difference_type
        j: std::vector< image_data >::difference_type

        """
        return _pcammls.ty_image_data_vector___delslice__(self, i, j)

    def __delitem__(self, *args):
        r"""
        __delitem__(self, i)

        Parameters
        ----------
        i: std::vector< image_data >::difference_type

        __delitem__(self, slice)

        Parameters
        ----------
        slice: PySliceObject *

        """
        return _pcammls.ty_image_data_vector___delitem__(self, *args)

    def __getitem__(self, *args):
        r"""
        __getitem__(self, slice) -> ty_image_data_vector

        Parameters
        ----------
        slice: PySliceObject *

        __getitem__(self, i) -> image_data

        Parameters
        ----------
        i: std::vector< image_data >::difference_type

        """
        return _pcammls.ty_image_data_vector___getitem__(self, *args)

    def __setitem__(self, *args):
        r"""
        __setitem__(self, slice, v)

        Parameters
        ----------
        slice: PySliceObject *
        v: std::vector< image_data,std::allocator< image_data > > const &

        __setitem__(self, slice)

        Parameters
        ----------
        slice: PySliceObject *

        __setitem__(self, i, x)

        Parameters
        ----------
        i: std::vector< image_data >::difference_type
        x: std::vector< image_data >::value_type const &

        """
        return _pcammls.ty_image_data_vector___setitem__(self, *args)

    def pop(self):
        r"""pop(self) -> image_data"""
        return _pcammls.ty_image_data_vector_pop(self)

    def append(self, x):
        r"""
        append(self, x)

        Parameters
        ----------
        x: std::vector< image_data >::value_type const &

        """
        return _pcammls.ty_image_data_vector_append(self, x)

    def empty(self):
        r"""empty(self) -> bool"""
        return _pcammls.ty_image_data_vector_empty(self)

    def size(self):
        r"""size(self) -> std::vector< image_data >::size_type"""
        return _pcammls.ty_image_data_vector_size(self)

    def swap(self, v):
        r"""
        swap(self, v)

        Parameters
        ----------
        v: std::vector< image_data > &

        """
        return _pcammls.ty_image_data_vector_swap(self, v)

    def begin(self):
        r"""begin(self) -> std::vector< image_data >::iterator"""
        return _pcammls.ty_image_data_vector_begin(self)

    def end(self):
        r"""end(self) -> std::vector< image_data >::iterator"""
        return _pcammls.ty_image_data_vector_end(self)

    def rbegin(self):
        r"""rbegin(self) -> std::vector< image_data >::reverse_iterator"""
        return _pcammls.ty_image_data_vector_rbegin(self)

    def rend(self):
        r"""rend(self) -> std::vector< image_data >::reverse_iterator"""
        return _pcammls.ty_image_data_vector_rend(self)

    def clear(self):
        r"""clear(self)"""
        return _pcammls.ty_image_data_vector_clear(self)

    def get_allocator(self):
        r"""get_allocator(self) -> std::vector< image_data >::allocator_type"""
        return _pcammls.ty_image_data_vector_get_allocator(self)

    def pop_back(self):
        r"""pop_back(self)"""
        return _pcammls.ty_image_data_vector_pop_back(self)

    def erase(self, *args):
        r"""
        erase(self, pos) -> std::vector< image_data >::iterator

        Parameters
        ----------
        pos: std::vector< image_data >::iterator

        erase(self, first, last) -> std::vector< image_data >::iterator

        Parameters
        ----------
        first: std::vector< image_data >::iterator
        last: std::vector< image_data >::iterator

        """
        return _pcammls.ty_image_data_vector_erase(self, *args)

    def __init__(self, *args):
        r"""
        __init__(self) -> ty_image_data_vector
        __init__(self, other) -> ty_image_data_vector

        Parameters
        ----------
        other: std::vector< image_data > const &

        __init__(self, size) -> ty_image_data_vector

        Parameters
        ----------
        size: std::vector< image_data >::size_type

        __init__(self, size, value) -> ty_image_data_vector

        Parameters
        ----------
        size: std::vector< image_data >::size_type
        value: std::vector< image_data >::value_type const &

        """
        _pcammls.ty_image_data_vector_swiginit(
            self, _pcammls.new_ty_image_data_vector(*args)
        )

    def push_back(self, x):
        r"""
        push_back(self, x)

        Parameters
        ----------
        x: std::vector< image_data >::value_type const &

        """
        return _pcammls.ty_image_data_vector_push_back(self, x)

    def front(self):
        r"""front(self) -> image_data"""
        return _pcammls.ty_image_data_vector_front(self)

    def back(self):
        r"""back(self) -> image_data"""
        return _pcammls.ty_image_data_vector_back(self)

    def assign(self, n, x):
        r"""
        assign(self, n, x)

        Parameters
        ----------
        n: std::vector< image_data >::size_type
        x: std::vector< image_data >::value_type const &

        """
        return _pcammls.ty_image_data_vector_assign(self, n, x)

    def resize(self, *args):
        r"""
        resize(self, new_size)

        Parameters
        ----------
        new_size: std::vector< image_data >::size_type

        resize(self, new_size, x)

        Parameters
        ----------
        new_size: std::vector< image_data >::size_type
        x: std::vector< image_data >::value_type const &

        """
        return _pcammls.ty_image_data_vector_resize(self, *args)

    def insert(self, *args):
        r"""
        insert(self, pos, x) -> std::vector< image_data >::iterator

        Parameters
        ----------
        pos: std::vector< image_data >::iterator
        x: std::vector< image_data >::value_type const &

        insert(self, pos, n, x)

        Parameters
        ----------
        pos: std::vector< image_data >::iterator
        n: std::vector< image_data >::size_type
        x: std::vector< image_data >::value_type const &

        """
        return _pcammls.ty_image_data_vector_insert(self, *args)

    def reserve(self, n):
        r"""
        reserve(self, n)

        Parameters
        ----------
        n: std::vector< image_data >::size_type

        """
        return _pcammls.ty_image_data_vector_reserve(self, n)

    def capacity(self):
        r"""capacity(self) -> std::vector< image_data >::size_type"""
        return _pcammls.ty_image_data_vector_capacity(self)

    __swig_destroy__ = _pcammls.delete_ty_image_data_vector


# Register ty_image_data_vector in _pcammls:
_pcammls.ty_image_data_vector_swigregister(ty_image_data_vector)


class ty_float_vector(object):
    r"""Proxy of C++ std::vector< float > class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def iterator(self):
        r"""iterator(self) -> SwigPyIterator"""
        return _pcammls.ty_float_vector_iterator(self)

    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        r"""__nonzero__(self) -> bool"""
        return _pcammls.ty_float_vector___nonzero__(self)

    def __bool__(self):
        r"""__bool__(self) -> bool"""
        return _pcammls.ty_float_vector___bool__(self)

    def __len__(self):
        r"""__len__(self) -> std::vector< float >::size_type"""
        return _pcammls.ty_float_vector___len__(self)

    def __getslice__(self, i, j):
        r"""
        __getslice__(self, i, j) -> ty_float_vector

        Parameters
        ----------
        i: std::vector< float >::difference_type
        j: std::vector< float >::difference_type

        """
        return _pcammls.ty_float_vector___getslice__(self, i, j)

    def __setslice__(self, *args):
        r"""
        __setslice__(self, i, j)

        Parameters
        ----------
        i: std::vector< float >::difference_type
        j: std::vector< float >::difference_type

        __setslice__(self, i, j, v)

        Parameters
        ----------
        i: std::vector< float >::difference_type
        j: std::vector< float >::difference_type
        v: std::vector< float,std::allocator< float > > const &

        """
        return _pcammls.ty_float_vector___setslice__(self, *args)

    def __delslice__(self, i, j):
        r"""
        __delslice__(self, i, j)

        Parameters
        ----------
        i: std::vector< float >::difference_type
        j: std::vector< float >::difference_type

        """
        return _pcammls.ty_float_vector___delslice__(self, i, j)

    def __delitem__(self, *args):
        r"""
        __delitem__(self, i)

        Parameters
        ----------
        i: std::vector< float >::difference_type

        __delitem__(self, slice)

        Parameters
        ----------
        slice: PySliceObject *

        """
        return _pcammls.ty_float_vector___delitem__(self, *args)

    def __getitem__(self, *args):
        r"""
        __getitem__(self, slice) -> ty_float_vector

        Parameters
        ----------
        slice: PySliceObject *

        __getitem__(self, i) -> std::vector< float >::value_type const &

        Parameters
        ----------
        i: std::vector< float >::difference_type

        """
        return _pcammls.ty_float_vector___getitem__(self, *args)

    def __setitem__(self, *args):
        r"""
        __setitem__(self, slice, v)

        Parameters
        ----------
        slice: PySliceObject *
        v: std::vector< float,std::allocator< float > > const &

        __setitem__(self, slice)

        Parameters
        ----------
        slice: PySliceObject *

        __setitem__(self, i, x)

        Parameters
        ----------
        i: std::vector< float >::difference_type
        x: std::vector< float >::value_type const &

        """
        return _pcammls.ty_float_vector___setitem__(self, *args)

    def pop(self):
        r"""pop(self) -> std::vector< float >::value_type"""
        return _pcammls.ty_float_vector_pop(self)

    def append(self, x):
        r"""
        append(self, x)

        Parameters
        ----------
        x: std::vector< float >::value_type const &

        """
        return _pcammls.ty_float_vector_append(self, x)

    def empty(self):
        r"""empty(self) -> bool"""
        return _pcammls.ty_float_vector_empty(self)

    def size(self):
        r"""size(self) -> std::vector< float >::size_type"""
        return _pcammls.ty_float_vector_size(self)

    def swap(self, v):
        r"""
        swap(self, v)

        Parameters
        ----------
        v: std::vector< float > &

        """
        return _pcammls.ty_float_vector_swap(self, v)

    def begin(self):
        r"""begin(self) -> std::vector< float >::iterator"""
        return _pcammls.ty_float_vector_begin(self)

    def end(self):
        r"""end(self) -> std::vector< float >::iterator"""
        return _pcammls.ty_float_vector_end(self)

    def rbegin(self):
        r"""rbegin(self) -> std::vector< float >::reverse_iterator"""
        return _pcammls.ty_float_vector_rbegin(self)

    def rend(self):
        r"""rend(self) -> std::vector< float >::reverse_iterator"""
        return _pcammls.ty_float_vector_rend(self)

    def clear(self):
        r"""clear(self)"""
        return _pcammls.ty_float_vector_clear(self)

    def get_allocator(self):
        r"""get_allocator(self) -> std::vector< float >::allocator_type"""
        return _pcammls.ty_float_vector_get_allocator(self)

    def pop_back(self):
        r"""pop_back(self)"""
        return _pcammls.ty_float_vector_pop_back(self)

    def erase(self, *args):
        r"""
        erase(self, pos) -> std::vector< float >::iterator

        Parameters
        ----------
        pos: std::vector< float >::iterator

        erase(self, first, last) -> std::vector< float >::iterator

        Parameters
        ----------
        first: std::vector< float >::iterator
        last: std::vector< float >::iterator

        """
        return _pcammls.ty_float_vector_erase(self, *args)

    def __init__(self, *args):
        r"""
        __init__(self) -> ty_float_vector
        __init__(self, other) -> ty_float_vector

        Parameters
        ----------
        other: std::vector< float > const &

        __init__(self, size) -> ty_float_vector

        Parameters
        ----------
        size: std::vector< float >::size_type

        __init__(self, size, value) -> ty_float_vector

        Parameters
        ----------
        size: std::vector< float >::size_type
        value: std::vector< float >::value_type const &

        """
        _pcammls.ty_float_vector_swiginit(self, _pcammls.new_ty_float_vector(*args))

    def push_back(self, x):
        r"""
        push_back(self, x)

        Parameters
        ----------
        x: std::vector< float >::value_type const &

        """
        return _pcammls.ty_float_vector_push_back(self, x)

    def front(self):
        r"""front(self) -> std::vector< float >::value_type const &"""
        return _pcammls.ty_float_vector_front(self)

    def back(self):
        r"""back(self) -> std::vector< float >::value_type const &"""
        return _pcammls.ty_float_vector_back(self)

    def assign(self, n, x):
        r"""
        assign(self, n, x)

        Parameters
        ----------
        n: std::vector< float >::size_type
        x: std::vector< float >::value_type const &

        """
        return _pcammls.ty_float_vector_assign(self, n, x)

    def resize(self, *args):
        r"""
        resize(self, new_size)

        Parameters
        ----------
        new_size: std::vector< float >::size_type

        resize(self, new_size, x)

        Parameters
        ----------
        new_size: std::vector< float >::size_type
        x: std::vector< float >::value_type const &

        """
        return _pcammls.ty_float_vector_resize(self, *args)

    def insert(self, *args):
        r"""
        insert(self, pos, x) -> std::vector< float >::iterator

        Parameters
        ----------
        pos: std::vector< float >::iterator
        x: std::vector< float >::value_type const &

        insert(self, pos, n, x)

        Parameters
        ----------
        pos: std::vector< float >::iterator
        n: std::vector< float >::size_type
        x: std::vector< float >::value_type const &

        """
        return _pcammls.ty_float_vector_insert(self, *args)

    def reserve(self, n):
        r"""
        reserve(self, n)

        Parameters
        ----------
        n: std::vector< float >::size_type

        """
        return _pcammls.ty_float_vector_reserve(self, n)

    def capacity(self):
        r"""capacity(self) -> std::vector< float >::size_type"""
        return _pcammls.ty_float_vector_capacity(self)

    __swig_destroy__ = _pcammls.delete_ty_float_vector


# Register ty_float_vector in _pcammls:
_pcammls.ty_float_vector_swigregister(ty_float_vector)


class ty_uint8_vector(object):
    r"""Proxy of C++ std::vector< unsigned char > class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def iterator(self):
        r"""iterator(self) -> SwigPyIterator"""
        return _pcammls.ty_uint8_vector_iterator(self)

    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        r"""__nonzero__(self) -> bool"""
        return _pcammls.ty_uint8_vector___nonzero__(self)

    def __bool__(self):
        r"""__bool__(self) -> bool"""
        return _pcammls.ty_uint8_vector___bool__(self)

    def __len__(self):
        r"""__len__(self) -> std::vector< unsigned char >::size_type"""
        return _pcammls.ty_uint8_vector___len__(self)

    def __getslice__(self, i, j):
        r"""
        __getslice__(self, i, j) -> ty_uint8_vector

        Parameters
        ----------
        i: std::vector< unsigned char >::difference_type
        j: std::vector< unsigned char >::difference_type

        """
        return _pcammls.ty_uint8_vector___getslice__(self, i, j)

    def __setslice__(self, *args):
        r"""
        __setslice__(self, i, j)

        Parameters
        ----------
        i: std::vector< unsigned char >::difference_type
        j: std::vector< unsigned char >::difference_type

        __setslice__(self, i, j, v)

        Parameters
        ----------
        i: std::vector< unsigned char >::difference_type
        j: std::vector< unsigned char >::difference_type
        v: std::vector< unsigned char,std::allocator< unsigned char > > const &

        """
        return _pcammls.ty_uint8_vector___setslice__(self, *args)

    def __delslice__(self, i, j):
        r"""
        __delslice__(self, i, j)

        Parameters
        ----------
        i: std::vector< unsigned char >::difference_type
        j: std::vector< unsigned char >::difference_type

        """
        return _pcammls.ty_uint8_vector___delslice__(self, i, j)

    def __delitem__(self, *args):
        r"""
        __delitem__(self, i)

        Parameters
        ----------
        i: std::vector< unsigned char >::difference_type

        __delitem__(self, slice)

        Parameters
        ----------
        slice: PySliceObject *

        """
        return _pcammls.ty_uint8_vector___delitem__(self, *args)

    def __getitem__(self, *args):
        r"""
        __getitem__(self, slice) -> ty_uint8_vector

        Parameters
        ----------
        slice: PySliceObject *

        __getitem__(self, i) -> std::vector< unsigned char >::value_type const &

        Parameters
        ----------
        i: std::vector< unsigned char >::difference_type

        """
        return _pcammls.ty_uint8_vector___getitem__(self, *args)

    def __setitem__(self, *args):
        r"""
        __setitem__(self, slice, v)

        Parameters
        ----------
        slice: PySliceObject *
        v: std::vector< unsigned char,std::allocator< unsigned char > > const &

        __setitem__(self, slice)

        Parameters
        ----------
        slice: PySliceObject *

        __setitem__(self, i, x)

        Parameters
        ----------
        i: std::vector< unsigned char >::difference_type
        x: std::vector< unsigned char >::value_type const &

        """
        return _pcammls.ty_uint8_vector___setitem__(self, *args)

    def pop(self):
        r"""pop(self) -> std::vector< unsigned char >::value_type"""
        return _pcammls.ty_uint8_vector_pop(self)

    def append(self, x):
        r"""
        append(self, x)

        Parameters
        ----------
        x: std::vector< unsigned char >::value_type const &

        """
        return _pcammls.ty_uint8_vector_append(self, x)

    def empty(self):
        r"""empty(self) -> bool"""
        return _pcammls.ty_uint8_vector_empty(self)

    def size(self):
        r"""size(self) -> std::vector< unsigned char >::size_type"""
        return _pcammls.ty_uint8_vector_size(self)

    def swap(self, v):
        r"""
        swap(self, v)

        Parameters
        ----------
        v: std::vector< unsigned char > &

        """
        return _pcammls.ty_uint8_vector_swap(self, v)

    def begin(self):
        r"""begin(self) -> std::vector< unsigned char >::iterator"""
        return _pcammls.ty_uint8_vector_begin(self)

    def end(self):
        r"""end(self) -> std::vector< unsigned char >::iterator"""
        return _pcammls.ty_uint8_vector_end(self)

    def rbegin(self):
        r"""rbegin(self) -> std::vector< unsigned char >::reverse_iterator"""
        return _pcammls.ty_uint8_vector_rbegin(self)

    def rend(self):
        r"""rend(self) -> std::vector< unsigned char >::reverse_iterator"""
        return _pcammls.ty_uint8_vector_rend(self)

    def clear(self):
        r"""clear(self)"""
        return _pcammls.ty_uint8_vector_clear(self)

    def get_allocator(self):
        r"""get_allocator(self) -> std::vector< unsigned char >::allocator_type"""
        return _pcammls.ty_uint8_vector_get_allocator(self)

    def pop_back(self):
        r"""pop_back(self)"""
        return _pcammls.ty_uint8_vector_pop_back(self)

    def erase(self, *args):
        r"""
        erase(self, pos) -> std::vector< unsigned char >::iterator

        Parameters
        ----------
        pos: std::vector< unsigned char >::iterator

        erase(self, first, last) -> std::vector< unsigned char >::iterator

        Parameters
        ----------
        first: std::vector< unsigned char >::iterator
        last: std::vector< unsigned char >::iterator

        """
        return _pcammls.ty_uint8_vector_erase(self, *args)

    def __init__(self, *args):
        r"""
        __init__(self) -> ty_uint8_vector
        __init__(self, other) -> ty_uint8_vector

        Parameters
        ----------
        other: std::vector< unsigned char > const &

        __init__(self, size) -> ty_uint8_vector

        Parameters
        ----------
        size: std::vector< unsigned char >::size_type

        __init__(self, size, value) -> ty_uint8_vector

        Parameters
        ----------
        size: std::vector< unsigned char >::size_type
        value: std::vector< unsigned char >::value_type const &

        """
        _pcammls.ty_uint8_vector_swiginit(self, _pcammls.new_ty_uint8_vector(*args))

    def push_back(self, x):
        r"""
        push_back(self, x)

        Parameters
        ----------
        x: std::vector< unsigned char >::value_type const &

        """
        return _pcammls.ty_uint8_vector_push_back(self, x)

    def front(self):
        r"""front(self) -> std::vector< unsigned char >::value_type const &"""
        return _pcammls.ty_uint8_vector_front(self)

    def back(self):
        r"""back(self) -> std::vector< unsigned char >::value_type const &"""
        return _pcammls.ty_uint8_vector_back(self)

    def assign(self, n, x):
        r"""
        assign(self, n, x)

        Parameters
        ----------
        n: std::vector< unsigned char >::size_type
        x: std::vector< unsigned char >::value_type const &

        """
        return _pcammls.ty_uint8_vector_assign(self, n, x)

    def resize(self, *args):
        r"""
        resize(self, new_size)

        Parameters
        ----------
        new_size: std::vector< unsigned char >::size_type

        resize(self, new_size, x)

        Parameters
        ----------
        new_size: std::vector< unsigned char >::size_type
        x: std::vector< unsigned char >::value_type const &

        """
        return _pcammls.ty_uint8_vector_resize(self, *args)

    def insert(self, *args):
        r"""
        insert(self, pos, x) -> std::vector< unsigned char >::iterator

        Parameters
        ----------
        pos: std::vector< unsigned char >::iterator
        x: std::vector< unsigned char >::value_type const &

        insert(self, pos, n, x)

        Parameters
        ----------
        pos: std::vector< unsigned char >::iterator
        n: std::vector< unsigned char >::size_type
        x: std::vector< unsigned char >::value_type const &

        """
        return _pcammls.ty_uint8_vector_insert(self, *args)

    def reserve(self, n):
        r"""
        reserve(self, n)

        Parameters
        ----------
        n: std::vector< unsigned char >::size_type

        """
        return _pcammls.ty_uint8_vector_reserve(self, n)

    def capacity(self):
        r"""capacity(self) -> std::vector< unsigned char >::size_type"""
        return _pcammls.ty_uint8_vector_capacity(self)

    __swig_destroy__ = _pcammls.delete_ty_uint8_vector


# Register ty_uint8_vector in _pcammls:
_pcammls.ty_uint8_vector_swigregister(ty_uint8_vector)


class ty_int32_vector(object):
    r"""Proxy of C++ std::vector< int > class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def iterator(self):
        r"""iterator(self) -> SwigPyIterator"""
        return _pcammls.ty_int32_vector_iterator(self)

    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        r"""__nonzero__(self) -> bool"""
        return _pcammls.ty_int32_vector___nonzero__(self)

    def __bool__(self):
        r"""__bool__(self) -> bool"""
        return _pcammls.ty_int32_vector___bool__(self)

    def __len__(self):
        r"""__len__(self) -> std::vector< int >::size_type"""
        return _pcammls.ty_int32_vector___len__(self)

    def __getslice__(self, i, j):
        r"""
        __getslice__(self, i, j) -> ty_int32_vector

        Parameters
        ----------
        i: std::vector< int >::difference_type
        j: std::vector< int >::difference_type

        """
        return _pcammls.ty_int32_vector___getslice__(self, i, j)

    def __setslice__(self, *args):
        r"""
        __setslice__(self, i, j)

        Parameters
        ----------
        i: std::vector< int >::difference_type
        j: std::vector< int >::difference_type

        __setslice__(self, i, j, v)

        Parameters
        ----------
        i: std::vector< int >::difference_type
        j: std::vector< int >::difference_type
        v: std::vector< int,std::allocator< int > > const &

        """
        return _pcammls.ty_int32_vector___setslice__(self, *args)

    def __delslice__(self, i, j):
        r"""
        __delslice__(self, i, j)

        Parameters
        ----------
        i: std::vector< int >::difference_type
        j: std::vector< int >::difference_type

        """
        return _pcammls.ty_int32_vector___delslice__(self, i, j)

    def __delitem__(self, *args):
        r"""
        __delitem__(self, i)

        Parameters
        ----------
        i: std::vector< int >::difference_type

        __delitem__(self, slice)

        Parameters
        ----------
        slice: PySliceObject *

        """
        return _pcammls.ty_int32_vector___delitem__(self, *args)

    def __getitem__(self, *args):
        r"""
        __getitem__(self, slice) -> ty_int32_vector

        Parameters
        ----------
        slice: PySliceObject *

        __getitem__(self, i) -> std::vector< int >::value_type const &

        Parameters
        ----------
        i: std::vector< int >::difference_type

        """
        return _pcammls.ty_int32_vector___getitem__(self, *args)

    def __setitem__(self, *args):
        r"""
        __setitem__(self, slice, v)

        Parameters
        ----------
        slice: PySliceObject *
        v: std::vector< int,std::allocator< int > > const &

        __setitem__(self, slice)

        Parameters
        ----------
        slice: PySliceObject *

        __setitem__(self, i, x)

        Parameters
        ----------
        i: std::vector< int >::difference_type
        x: std::vector< int >::value_type const &

        """
        return _pcammls.ty_int32_vector___setitem__(self, *args)

    def pop(self):
        r"""pop(self) -> std::vector< int >::value_type"""
        return _pcammls.ty_int32_vector_pop(self)

    def append(self, x):
        r"""
        append(self, x)

        Parameters
        ----------
        x: std::vector< int >::value_type const &

        """
        return _pcammls.ty_int32_vector_append(self, x)

    def empty(self):
        r"""empty(self) -> bool"""
        return _pcammls.ty_int32_vector_empty(self)

    def size(self):
        r"""size(self) -> std::vector< int >::size_type"""
        return _pcammls.ty_int32_vector_size(self)

    def swap(self, v):
        r"""
        swap(self, v)

        Parameters
        ----------
        v: std::vector< int > &

        """
        return _pcammls.ty_int32_vector_swap(self, v)

    def begin(self):
        r"""begin(self) -> std::vector< int >::iterator"""
        return _pcammls.ty_int32_vector_begin(self)

    def end(self):
        r"""end(self) -> std::vector< int >::iterator"""
        return _pcammls.ty_int32_vector_end(self)

    def rbegin(self):
        r"""rbegin(self) -> std::vector< int >::reverse_iterator"""
        return _pcammls.ty_int32_vector_rbegin(self)

    def rend(self):
        r"""rend(self) -> std::vector< int >::reverse_iterator"""
        return _pcammls.ty_int32_vector_rend(self)

    def clear(self):
        r"""clear(self)"""
        return _pcammls.ty_int32_vector_clear(self)

    def get_allocator(self):
        r"""get_allocator(self) -> std::vector< int >::allocator_type"""
        return _pcammls.ty_int32_vector_get_allocator(self)

    def pop_back(self):
        r"""pop_back(self)"""
        return _pcammls.ty_int32_vector_pop_back(self)

    def erase(self, *args):
        r"""
        erase(self, pos) -> std::vector< int >::iterator

        Parameters
        ----------
        pos: std::vector< int >::iterator

        erase(self, first, last) -> std::vector< int >::iterator

        Parameters
        ----------
        first: std::vector< int >::iterator
        last: std::vector< int >::iterator

        """
        return _pcammls.ty_int32_vector_erase(self, *args)

    def __init__(self, *args):
        r"""
        __init__(self) -> ty_int32_vector
        __init__(self, other) -> ty_int32_vector

        Parameters
        ----------
        other: std::vector< int > const &

        __init__(self, size) -> ty_int32_vector

        Parameters
        ----------
        size: std::vector< int >::size_type

        __init__(self, size, value) -> ty_int32_vector

        Parameters
        ----------
        size: std::vector< int >::size_type
        value: std::vector< int >::value_type const &

        """
        _pcammls.ty_int32_vector_swiginit(self, _pcammls.new_ty_int32_vector(*args))

    def push_back(self, x):
        r"""
        push_back(self, x)

        Parameters
        ----------
        x: std::vector< int >::value_type const &

        """
        return _pcammls.ty_int32_vector_push_back(self, x)

    def front(self):
        r"""front(self) -> std::vector< int >::value_type const &"""
        return _pcammls.ty_int32_vector_front(self)

    def back(self):
        r"""back(self) -> std::vector< int >::value_type const &"""
        return _pcammls.ty_int32_vector_back(self)

    def assign(self, n, x):
        r"""
        assign(self, n, x)

        Parameters
        ----------
        n: std::vector< int >::size_type
        x: std::vector< int >::value_type const &

        """
        return _pcammls.ty_int32_vector_assign(self, n, x)

    def resize(self, *args):
        r"""
        resize(self, new_size)

        Parameters
        ----------
        new_size: std::vector< int >::size_type

        resize(self, new_size, x)

        Parameters
        ----------
        new_size: std::vector< int >::size_type
        x: std::vector< int >::value_type const &

        """
        return _pcammls.ty_int32_vector_resize(self, *args)

    def insert(self, *args):
        r"""
        insert(self, pos, x) -> std::vector< int >::iterator

        Parameters
        ----------
        pos: std::vector< int >::iterator
        x: std::vector< int >::value_type const &

        insert(self, pos, n, x)

        Parameters
        ----------
        pos: std::vector< int >::iterator
        n: std::vector< int >::size_type
        x: std::vector< int >::value_type const &

        """
        return _pcammls.ty_int32_vector_insert(self, *args)

    def reserve(self, n):
        r"""
        reserve(self, n)

        Parameters
        ----------
        n: std::vector< int >::size_type

        """
        return _pcammls.ty_int32_vector_reserve(self, n)

    def capacity(self):
        r"""capacity(self) -> std::vector< int >::size_type"""
        return _pcammls.ty_int32_vector_capacity(self)

    __swig_destroy__ = _pcammls.delete_ty_int32_vector


# Register ty_int32_vector in _pcammls:
_pcammls.ty_int32_vector_swigregister(ty_int32_vector)


class TY_ISP_FEATURE_INFO_ARRAY(object):
    r"""Proxy of C++ TY_ISP_FEATURE_INFO_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_ISP_FEATURE_INFO_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_ISP_FEATURE_INFO_ARRAY_swiginit(
            self, _pcammls.new_TY_ISP_FEATURE_INFO_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_ISP_FEATURE_INFO_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_ISP_FEATURE_INFO

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_ISP_FEATURE_INFO_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_ISP_FEATURE_INFO

        """
        return _pcammls.TY_ISP_FEATURE_INFO_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_ISP_FEATURE_INFO"""
        return _pcammls.TY_ISP_FEATURE_INFO_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_ISP_FEATURE_INFO_ARRAY

        Parameters
        ----------
        t: TY_ISP_FEATURE_INFO *

        """
        return _pcammls.TY_ISP_FEATURE_INFO_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_ISP_FEATURE_INFO_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_ISP_FEATURE_INFO_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_ISP_FEATURE_INFO_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_ISP_FEATURE_INFO_ARRAY *

        """
        return _pcammls.TY_ISP_FEATURE_INFO_ARRAY_ReleasePtr(p)


# Register TY_ISP_FEATURE_INFO_ARRAY in _pcammls:
_pcammls.TY_ISP_FEATURE_INFO_ARRAY_swigregister(TY_ISP_FEATURE_INFO_ARRAY)


def TY_ISP_FEATURE_INFO_ARRAY_frompointer(t):
    r"""
    TY_ISP_FEATURE_INFO_ARRAY_frompointer(t) -> TY_ISP_FEATURE_INFO_ARRAY

    Parameters
    ----------
    t: TY_ISP_FEATURE_INFO *

    """
    return _pcammls.TY_ISP_FEATURE_INFO_ARRAY_frompointer(t)


def TY_ISP_FEATURE_INFO_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_ISP_FEATURE_INFO_ARRAY_FromVoidPtr(t, size) -> TY_ISP_FEATURE_INFO_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_ISP_FEATURE_INFO_ARRAY_FromVoidPtr(t, size)


def TY_ISP_FEATURE_INFO_ARRAY_ReleasePtr(p):
    r"""
    TY_ISP_FEATURE_INFO_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_ISP_FEATURE_INFO_ARRAY *

    """
    return _pcammls.TY_ISP_FEATURE_INFO_ARRAY_ReleasePtr(p)


def PY_TYISPSetDeviceHandle(isp_handle, dev_handle):
    r"""
    PY_TYISPSetDeviceHandle(isp_handle, dev_handle) -> TY_STATUS

    Parameters
    ----------
    isp_handle: TY_ISP_HANDLE
    dev_handle: TY_DEV_HANDLE

    """
    return _pcammls.PY_TYISPSetDeviceHandle(isp_handle, dev_handle)


class float_ARRAY(object):
    r"""Proxy of C++ float_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    @staticmethod
    def from_nparray1d(NP_ARRAY_PTR):
        r"""
        from_nparray1d(NP_ARRAY_PTR) -> float_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: float *

        """
        return _pcammls.float_ARRAY_from_nparray1d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray2d(NP_ARRAY_PTR):
        r"""
        from_nparray2d(NP_ARRAY_PTR) -> float_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: float *

        """
        return _pcammls.float_ARRAY_from_nparray2d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray3d(NP_ARRAY_PTR):
        r"""
        from_nparray3d(NP_ARRAY_PTR) -> float_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: float *

        """
        return _pcammls.float_ARRAY_from_nparray3d(NP_ARRAY_PTR)

    @staticmethod
    def Release(ptr):
        r"""
        Release(ptr)

        Parameters
        ----------
        ptr: float_ARRAY *

        """
        return _pcammls.float_ARRAY_Release(ptr)

    @staticmethod
    def ptr_as_nparray1d(ptr, row):
        r"""
        ptr_as_nparray1d(ptr, row)

        Parameters
        ----------
        ptr: void *
        row: int

        """
        return _pcammls.float_ARRAY_ptr_as_nparray1d(ptr, row)

    @staticmethod
    def ptr_as_nparray2d(ptr, row, col):
        r"""
        ptr_as_nparray2d(ptr, row, col)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int

        """
        return _pcammls.float_ARRAY_ptr_as_nparray2d(ptr, row, col)

    @staticmethod
    def ptr_as_nparray3d(ptr, row, col, psz):
        r"""
        ptr_as_nparray3d(ptr, row, col, psz)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int
        psz: int

        """
        return _pcammls.float_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    def as_nparray1d(self, row):
        r"""
        as_nparray1d(self, row)

        Parameters
        ----------
        row: int

        """
        return _pcammls.float_ARRAY_as_nparray1d(self, row)

    def as_nparray2d(self, row, col):
        r"""
        as_nparray2d(self, row, col)

        Parameters
        ----------
        row: int
        col: int

        """
        return _pcammls.float_ARRAY_as_nparray2d(self, row, col)

    def as_nparray3d(self, row, col, psz):
        r"""
        as_nparray3d(self, row, col, psz)

        Parameters
        ----------
        row: int
        col: int
        psz: int

        """
        return _pcammls.float_ARRAY_as_nparray3d(self, row, col, psz)

    @staticmethod
    def from_nparray(np_array):
        dim = np_array.ndim
        if dim == 3:
            return float_ARRAY.from_nparray3d(np_array)
        elif dim == 2:
            return float_ARRAY.from_nparray2d(np_array)
        elif dim == 1:
            return float_ARRAY.from_nparray1d(np_array)
        else:
            raise Exception("not support format")

    @staticmethod
    def release(ptr):
        float_ARRAY.Release(ptr)

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> float_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.float_ARRAY_swiginit(self, _pcammls.new_float_ARRAY(nelements))

    __swig_destroy__ = _pcammls.delete_float_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> float

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.float_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: float

        """
        return _pcammls.float_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> float *"""
        return _pcammls.float_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> float_ARRAY

        Parameters
        ----------
        t: float *

        """
        return _pcammls.float_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> float_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.float_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.float_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: float_ARRAY *

        """
        return _pcammls.float_ARRAY_ReleasePtr(p)


# Register float_ARRAY in _pcammls:
_pcammls.float_ARRAY_swigregister(float_ARRAY)


def float_ARRAY_from_nparray1d(NP_ARRAY_PTR):
    r"""
    float_ARRAY_from_nparray1d(NP_ARRAY_PTR) -> float_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: float *

    """
    return _pcammls.float_ARRAY_from_nparray1d(NP_ARRAY_PTR)


def float_ARRAY_from_nparray2d(NP_ARRAY_PTR):
    r"""
    float_ARRAY_from_nparray2d(NP_ARRAY_PTR) -> float_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: float *

    """
    return _pcammls.float_ARRAY_from_nparray2d(NP_ARRAY_PTR)


def float_ARRAY_from_nparray3d(NP_ARRAY_PTR):
    r"""
    float_ARRAY_from_nparray3d(NP_ARRAY_PTR) -> float_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: float *

    """
    return _pcammls.float_ARRAY_from_nparray3d(NP_ARRAY_PTR)


def float_ARRAY_Release(ptr):
    r"""
    float_ARRAY_Release(ptr)

    Parameters
    ----------
    ptr: float_ARRAY *

    """
    return _pcammls.float_ARRAY_Release(ptr)


def float_ARRAY_ptr_as_nparray1d(ptr, row):
    r"""
    float_ARRAY_ptr_as_nparray1d(ptr, row)

    Parameters
    ----------
    ptr: void *
    row: int

    """
    return _pcammls.float_ARRAY_ptr_as_nparray1d(ptr, row)


def float_ARRAY_ptr_as_nparray2d(ptr, row, col):
    r"""
    float_ARRAY_ptr_as_nparray2d(ptr, row, col)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int

    """
    return _pcammls.float_ARRAY_ptr_as_nparray2d(ptr, row, col)


def float_ARRAY_ptr_as_nparray3d(ptr, row, col, psz):
    r"""
    float_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int
    psz: int

    """
    return _pcammls.float_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)


def float_ARRAY_frompointer(t):
    r"""
    float_ARRAY_frompointer(t) -> float_ARRAY

    Parameters
    ----------
    t: float *

    """
    return _pcammls.float_ARRAY_frompointer(t)


def float_ARRAY_FromVoidPtr(t, size):
    r"""
    float_ARRAY_FromVoidPtr(t, size) -> float_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.float_ARRAY_FromVoidPtr(t, size)


def float_ARRAY_ReleasePtr(p):
    r"""
    float_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: float_ARRAY *

    """
    return _pcammls.float_ARRAY_ReleasePtr(p)


class int8_t_ARRAY(object):
    r"""Proxy of C++ int8_t_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    @staticmethod
    def from_nparray1d(NP_ARRAY_PTR):
        r"""
        from_nparray1d(NP_ARRAY_PTR) -> int8_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: int8_t *

        """
        return _pcammls.int8_t_ARRAY_from_nparray1d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray2d(NP_ARRAY_PTR):
        r"""
        from_nparray2d(NP_ARRAY_PTR) -> int8_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: int8_t *

        """
        return _pcammls.int8_t_ARRAY_from_nparray2d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray3d(NP_ARRAY_PTR):
        r"""
        from_nparray3d(NP_ARRAY_PTR) -> int8_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: int8_t *

        """
        return _pcammls.int8_t_ARRAY_from_nparray3d(NP_ARRAY_PTR)

    @staticmethod
    def Release(ptr):
        r"""
        Release(ptr)

        Parameters
        ----------
        ptr: int8_t_ARRAY *

        """
        return _pcammls.int8_t_ARRAY_Release(ptr)

    @staticmethod
    def ptr_as_nparray1d(ptr, row):
        r"""
        ptr_as_nparray1d(ptr, row)

        Parameters
        ----------
        ptr: void *
        row: int

        """
        return _pcammls.int8_t_ARRAY_ptr_as_nparray1d(ptr, row)

    @staticmethod
    def ptr_as_nparray2d(ptr, row, col):
        r"""
        ptr_as_nparray2d(ptr, row, col)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int

        """
        return _pcammls.int8_t_ARRAY_ptr_as_nparray2d(ptr, row, col)

    @staticmethod
    def ptr_as_nparray3d(ptr, row, col, psz):
        r"""
        ptr_as_nparray3d(ptr, row, col, psz)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int
        psz: int

        """
        return _pcammls.int8_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    def as_nparray1d(self, row):
        r"""
        as_nparray1d(self, row)

        Parameters
        ----------
        row: int

        """
        return _pcammls.int8_t_ARRAY_as_nparray1d(self, row)

    def as_nparray2d(self, row, col):
        r"""
        as_nparray2d(self, row, col)

        Parameters
        ----------
        row: int
        col: int

        """
        return _pcammls.int8_t_ARRAY_as_nparray2d(self, row, col)

    def as_nparray3d(self, row, col, psz):
        r"""
        as_nparray3d(self, row, col, psz)

        Parameters
        ----------
        row: int
        col: int
        psz: int

        """
        return _pcammls.int8_t_ARRAY_as_nparray3d(self, row, col, psz)

    @staticmethod
    def from_nparray(np_array):
        dim = np_array.ndim
        if dim == 3:
            return int8_t_ARRAY.from_nparray3d(np_array)
        elif dim == 2:
            return int8_t_ARRAY.from_nparray2d(np_array)
        elif dim == 1:
            return int8_t_ARRAY.from_nparray1d(np_array)
        else:
            raise Exception("not support format")

    @staticmethod
    def release(ptr):
        int8_t_ARRAY.Release(ptr)

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> int8_t_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.int8_t_ARRAY_swiginit(self, _pcammls.new_int8_t_ARRAY(nelements))

    __swig_destroy__ = _pcammls.delete_int8_t_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> int8_t

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.int8_t_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: int8_t

        """
        return _pcammls.int8_t_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> int8_t *"""
        return _pcammls.int8_t_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> int8_t_ARRAY

        Parameters
        ----------
        t: int8_t *

        """
        return _pcammls.int8_t_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> int8_t_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.int8_t_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.int8_t_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: int8_t_ARRAY *

        """
        return _pcammls.int8_t_ARRAY_ReleasePtr(p)


# Register int8_t_ARRAY in _pcammls:
_pcammls.int8_t_ARRAY_swigregister(int8_t_ARRAY)


def int8_t_ARRAY_from_nparray1d(NP_ARRAY_PTR):
    r"""
    int8_t_ARRAY_from_nparray1d(NP_ARRAY_PTR) -> int8_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: int8_t *

    """
    return _pcammls.int8_t_ARRAY_from_nparray1d(NP_ARRAY_PTR)


def int8_t_ARRAY_from_nparray2d(NP_ARRAY_PTR):
    r"""
    int8_t_ARRAY_from_nparray2d(NP_ARRAY_PTR) -> int8_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: int8_t *

    """
    return _pcammls.int8_t_ARRAY_from_nparray2d(NP_ARRAY_PTR)


def int8_t_ARRAY_from_nparray3d(NP_ARRAY_PTR):
    r"""
    int8_t_ARRAY_from_nparray3d(NP_ARRAY_PTR) -> int8_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: int8_t *

    """
    return _pcammls.int8_t_ARRAY_from_nparray3d(NP_ARRAY_PTR)


def int8_t_ARRAY_Release(ptr):
    r"""
    int8_t_ARRAY_Release(ptr)

    Parameters
    ----------
    ptr: int8_t_ARRAY *

    """
    return _pcammls.int8_t_ARRAY_Release(ptr)


def int8_t_ARRAY_ptr_as_nparray1d(ptr, row):
    r"""
    int8_t_ARRAY_ptr_as_nparray1d(ptr, row)

    Parameters
    ----------
    ptr: void *
    row: int

    """
    return _pcammls.int8_t_ARRAY_ptr_as_nparray1d(ptr, row)


def int8_t_ARRAY_ptr_as_nparray2d(ptr, row, col):
    r"""
    int8_t_ARRAY_ptr_as_nparray2d(ptr, row, col)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int

    """
    return _pcammls.int8_t_ARRAY_ptr_as_nparray2d(ptr, row, col)


def int8_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz):
    r"""
    int8_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int
    psz: int

    """
    return _pcammls.int8_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)


def int8_t_ARRAY_frompointer(t):
    r"""
    int8_t_ARRAY_frompointer(t) -> int8_t_ARRAY

    Parameters
    ----------
    t: int8_t *

    """
    return _pcammls.int8_t_ARRAY_frompointer(t)


def int8_t_ARRAY_FromVoidPtr(t, size):
    r"""
    int8_t_ARRAY_FromVoidPtr(t, size) -> int8_t_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.int8_t_ARRAY_FromVoidPtr(t, size)


def int8_t_ARRAY_ReleasePtr(p):
    r"""
    int8_t_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: int8_t_ARRAY *

    """
    return _pcammls.int8_t_ARRAY_ReleasePtr(p)


class uint8_t_ARRAY(object):
    r"""Proxy of C++ uint8_t_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    @staticmethod
    def from_nparray1d(NP_ARRAY_PTR):
        r"""
        from_nparray1d(NP_ARRAY_PTR) -> uint8_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: uint8_t *

        """
        return _pcammls.uint8_t_ARRAY_from_nparray1d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray2d(NP_ARRAY_PTR):
        r"""
        from_nparray2d(NP_ARRAY_PTR) -> uint8_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: uint8_t *

        """
        return _pcammls.uint8_t_ARRAY_from_nparray2d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray3d(NP_ARRAY_PTR):
        r"""
        from_nparray3d(NP_ARRAY_PTR) -> uint8_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: uint8_t *

        """
        return _pcammls.uint8_t_ARRAY_from_nparray3d(NP_ARRAY_PTR)

    @staticmethod
    def Release(ptr):
        r"""
        Release(ptr)

        Parameters
        ----------
        ptr: uint8_t_ARRAY *

        """
        return _pcammls.uint8_t_ARRAY_Release(ptr)

    @staticmethod
    def ptr_as_nparray1d(ptr, row):
        r"""
        ptr_as_nparray1d(ptr, row)

        Parameters
        ----------
        ptr: void *
        row: int

        """
        return _pcammls.uint8_t_ARRAY_ptr_as_nparray1d(ptr, row)

    @staticmethod
    def ptr_as_nparray2d(ptr, row, col):
        r"""
        ptr_as_nparray2d(ptr, row, col)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int

        """
        return _pcammls.uint8_t_ARRAY_ptr_as_nparray2d(ptr, row, col)

    @staticmethod
    def ptr_as_nparray3d(ptr, row, col, psz):
        r"""
        ptr_as_nparray3d(ptr, row, col, psz)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int
        psz: int

        """
        return _pcammls.uint8_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    def as_nparray1d(self, row):
        r"""
        as_nparray1d(self, row)

        Parameters
        ----------
        row: int

        """
        return _pcammls.uint8_t_ARRAY_as_nparray1d(self, row)

    def as_nparray2d(self, row, col):
        r"""
        as_nparray2d(self, row, col)

        Parameters
        ----------
        row: int
        col: int

        """
        return _pcammls.uint8_t_ARRAY_as_nparray2d(self, row, col)

    def as_nparray3d(self, row, col, psz):
        r"""
        as_nparray3d(self, row, col, psz)

        Parameters
        ----------
        row: int
        col: int
        psz: int

        """
        return _pcammls.uint8_t_ARRAY_as_nparray3d(self, row, col, psz)

    @staticmethod
    def from_nparray(np_array):
        dim = np_array.ndim
        if dim == 3:
            return uint8_t_ARRAY.from_nparray3d(np_array)
        elif dim == 2:
            return uint8_t_ARRAY.from_nparray2d(np_array)
        elif dim == 1:
            return uint8_t_ARRAY.from_nparray1d(np_array)
        else:
            raise Exception("not support format")

    @staticmethod
    def release(ptr):
        uint8_t_ARRAY.Release(ptr)

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> uint8_t_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.uint8_t_ARRAY_swiginit(self, _pcammls.new_uint8_t_ARRAY(nelements))

    __swig_destroy__ = _pcammls.delete_uint8_t_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> uint8_t

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.uint8_t_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: uint8_t

        """
        return _pcammls.uint8_t_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> uint8_t *"""
        return _pcammls.uint8_t_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> uint8_t_ARRAY

        Parameters
        ----------
        t: uint8_t *

        """
        return _pcammls.uint8_t_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> uint8_t_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.uint8_t_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.uint8_t_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: uint8_t_ARRAY *

        """
        return _pcammls.uint8_t_ARRAY_ReleasePtr(p)


# Register uint8_t_ARRAY in _pcammls:
_pcammls.uint8_t_ARRAY_swigregister(uint8_t_ARRAY)


def uint8_t_ARRAY_from_nparray1d(NP_ARRAY_PTR):
    r"""
    uint8_t_ARRAY_from_nparray1d(NP_ARRAY_PTR) -> uint8_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: uint8_t *

    """
    return _pcammls.uint8_t_ARRAY_from_nparray1d(NP_ARRAY_PTR)


def uint8_t_ARRAY_from_nparray2d(NP_ARRAY_PTR):
    r"""
    uint8_t_ARRAY_from_nparray2d(NP_ARRAY_PTR) -> uint8_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: uint8_t *

    """
    return _pcammls.uint8_t_ARRAY_from_nparray2d(NP_ARRAY_PTR)


def uint8_t_ARRAY_from_nparray3d(NP_ARRAY_PTR):
    r"""
    uint8_t_ARRAY_from_nparray3d(NP_ARRAY_PTR) -> uint8_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: uint8_t *

    """
    return _pcammls.uint8_t_ARRAY_from_nparray3d(NP_ARRAY_PTR)


def uint8_t_ARRAY_Release(ptr):
    r"""
    uint8_t_ARRAY_Release(ptr)

    Parameters
    ----------
    ptr: uint8_t_ARRAY *

    """
    return _pcammls.uint8_t_ARRAY_Release(ptr)


def uint8_t_ARRAY_ptr_as_nparray1d(ptr, row):
    r"""
    uint8_t_ARRAY_ptr_as_nparray1d(ptr, row)

    Parameters
    ----------
    ptr: void *
    row: int

    """
    return _pcammls.uint8_t_ARRAY_ptr_as_nparray1d(ptr, row)


def uint8_t_ARRAY_ptr_as_nparray2d(ptr, row, col):
    r"""
    uint8_t_ARRAY_ptr_as_nparray2d(ptr, row, col)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int

    """
    return _pcammls.uint8_t_ARRAY_ptr_as_nparray2d(ptr, row, col)


def uint8_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz):
    r"""
    uint8_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int
    psz: int

    """
    return _pcammls.uint8_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)


def uint8_t_ARRAY_frompointer(t):
    r"""
    uint8_t_ARRAY_frompointer(t) -> uint8_t_ARRAY

    Parameters
    ----------
    t: uint8_t *

    """
    return _pcammls.uint8_t_ARRAY_frompointer(t)


def uint8_t_ARRAY_FromVoidPtr(t, size):
    r"""
    uint8_t_ARRAY_FromVoidPtr(t, size) -> uint8_t_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.uint8_t_ARRAY_FromVoidPtr(t, size)


def uint8_t_ARRAY_ReleasePtr(p):
    r"""
    uint8_t_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: uint8_t_ARRAY *

    """
    return _pcammls.uint8_t_ARRAY_ReleasePtr(p)


class int16_t_ARRAY(object):
    r"""Proxy of C++ int16_t_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    @staticmethod
    def from_nparray1d(NP_ARRAY_PTR):
        r"""
        from_nparray1d(NP_ARRAY_PTR) -> int16_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: int16_t *

        """
        return _pcammls.int16_t_ARRAY_from_nparray1d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray2d(NP_ARRAY_PTR):
        r"""
        from_nparray2d(NP_ARRAY_PTR) -> int16_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: int16_t *

        """
        return _pcammls.int16_t_ARRAY_from_nparray2d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray3d(NP_ARRAY_PTR):
        r"""
        from_nparray3d(NP_ARRAY_PTR) -> int16_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: int16_t *

        """
        return _pcammls.int16_t_ARRAY_from_nparray3d(NP_ARRAY_PTR)

    @staticmethod
    def Release(ptr):
        r"""
        Release(ptr)

        Parameters
        ----------
        ptr: int16_t_ARRAY *

        """
        return _pcammls.int16_t_ARRAY_Release(ptr)

    @staticmethod
    def ptr_as_nparray1d(ptr, row):
        r"""
        ptr_as_nparray1d(ptr, row)

        Parameters
        ----------
        ptr: void *
        row: int

        """
        return _pcammls.int16_t_ARRAY_ptr_as_nparray1d(ptr, row)

    @staticmethod
    def ptr_as_nparray2d(ptr, row, col):
        r"""
        ptr_as_nparray2d(ptr, row, col)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int

        """
        return _pcammls.int16_t_ARRAY_ptr_as_nparray2d(ptr, row, col)

    @staticmethod
    def ptr_as_nparray3d(ptr, row, col, psz):
        r"""
        ptr_as_nparray3d(ptr, row, col, psz)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int
        psz: int

        """
        return _pcammls.int16_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    def as_nparray1d(self, row):
        r"""
        as_nparray1d(self, row)

        Parameters
        ----------
        row: int

        """
        return _pcammls.int16_t_ARRAY_as_nparray1d(self, row)

    def as_nparray2d(self, row, col):
        r"""
        as_nparray2d(self, row, col)

        Parameters
        ----------
        row: int
        col: int

        """
        return _pcammls.int16_t_ARRAY_as_nparray2d(self, row, col)

    def as_nparray3d(self, row, col, psz):
        r"""
        as_nparray3d(self, row, col, psz)

        Parameters
        ----------
        row: int
        col: int
        psz: int

        """
        return _pcammls.int16_t_ARRAY_as_nparray3d(self, row, col, psz)

    @staticmethod
    def from_nparray(np_array):
        dim = np_array.ndim
        if dim == 3:
            return int16_t_ARRAY.from_nparray3d(np_array)
        elif dim == 2:
            return int16_t_ARRAY.from_nparray2d(np_array)
        elif dim == 1:
            return int16_t_ARRAY.from_nparray1d(np_array)
        else:
            raise Exception("not support format")

    @staticmethod
    def release(ptr):
        int16_t_ARRAY.Release(ptr)

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> int16_t_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.int16_t_ARRAY_swiginit(self, _pcammls.new_int16_t_ARRAY(nelements))

    __swig_destroy__ = _pcammls.delete_int16_t_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> int16_t

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.int16_t_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: int16_t

        """
        return _pcammls.int16_t_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> int16_t *"""
        return _pcammls.int16_t_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> int16_t_ARRAY

        Parameters
        ----------
        t: int16_t *

        """
        return _pcammls.int16_t_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> int16_t_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.int16_t_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.int16_t_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: int16_t_ARRAY *

        """
        return _pcammls.int16_t_ARRAY_ReleasePtr(p)


# Register int16_t_ARRAY in _pcammls:
_pcammls.int16_t_ARRAY_swigregister(int16_t_ARRAY)


def int16_t_ARRAY_from_nparray1d(NP_ARRAY_PTR):
    r"""
    int16_t_ARRAY_from_nparray1d(NP_ARRAY_PTR) -> int16_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: int16_t *

    """
    return _pcammls.int16_t_ARRAY_from_nparray1d(NP_ARRAY_PTR)


def int16_t_ARRAY_from_nparray2d(NP_ARRAY_PTR):
    r"""
    int16_t_ARRAY_from_nparray2d(NP_ARRAY_PTR) -> int16_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: int16_t *

    """
    return _pcammls.int16_t_ARRAY_from_nparray2d(NP_ARRAY_PTR)


def int16_t_ARRAY_from_nparray3d(NP_ARRAY_PTR):
    r"""
    int16_t_ARRAY_from_nparray3d(NP_ARRAY_PTR) -> int16_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: int16_t *

    """
    return _pcammls.int16_t_ARRAY_from_nparray3d(NP_ARRAY_PTR)


def int16_t_ARRAY_Release(ptr):
    r"""
    int16_t_ARRAY_Release(ptr)

    Parameters
    ----------
    ptr: int16_t_ARRAY *

    """
    return _pcammls.int16_t_ARRAY_Release(ptr)


def int16_t_ARRAY_ptr_as_nparray1d(ptr, row):
    r"""
    int16_t_ARRAY_ptr_as_nparray1d(ptr, row)

    Parameters
    ----------
    ptr: void *
    row: int

    """
    return _pcammls.int16_t_ARRAY_ptr_as_nparray1d(ptr, row)


def int16_t_ARRAY_ptr_as_nparray2d(ptr, row, col):
    r"""
    int16_t_ARRAY_ptr_as_nparray2d(ptr, row, col)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int

    """
    return _pcammls.int16_t_ARRAY_ptr_as_nparray2d(ptr, row, col)


def int16_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz):
    r"""
    int16_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int
    psz: int

    """
    return _pcammls.int16_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)


def int16_t_ARRAY_frompointer(t):
    r"""
    int16_t_ARRAY_frompointer(t) -> int16_t_ARRAY

    Parameters
    ----------
    t: int16_t *

    """
    return _pcammls.int16_t_ARRAY_frompointer(t)


def int16_t_ARRAY_FromVoidPtr(t, size):
    r"""
    int16_t_ARRAY_FromVoidPtr(t, size) -> int16_t_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.int16_t_ARRAY_FromVoidPtr(t, size)


def int16_t_ARRAY_ReleasePtr(p):
    r"""
    int16_t_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: int16_t_ARRAY *

    """
    return _pcammls.int16_t_ARRAY_ReleasePtr(p)


class uint16_t_ARRAY(object):
    r"""Proxy of C++ uint16_t_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    @staticmethod
    def from_nparray1d(NP_ARRAY_PTR):
        r"""
        from_nparray1d(NP_ARRAY_PTR) -> uint16_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: uint16_t *

        """
        return _pcammls.uint16_t_ARRAY_from_nparray1d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray2d(NP_ARRAY_PTR):
        r"""
        from_nparray2d(NP_ARRAY_PTR) -> uint16_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: uint16_t *

        """
        return _pcammls.uint16_t_ARRAY_from_nparray2d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray3d(NP_ARRAY_PTR):
        r"""
        from_nparray3d(NP_ARRAY_PTR) -> uint16_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: uint16_t *

        """
        return _pcammls.uint16_t_ARRAY_from_nparray3d(NP_ARRAY_PTR)

    @staticmethod
    def Release(ptr):
        r"""
        Release(ptr)

        Parameters
        ----------
        ptr: uint16_t_ARRAY *

        """
        return _pcammls.uint16_t_ARRAY_Release(ptr)

    @staticmethod
    def ptr_as_nparray1d(ptr, row):
        r"""
        ptr_as_nparray1d(ptr, row)

        Parameters
        ----------
        ptr: void *
        row: int

        """
        return _pcammls.uint16_t_ARRAY_ptr_as_nparray1d(ptr, row)

    @staticmethod
    def ptr_as_nparray2d(ptr, row, col):
        r"""
        ptr_as_nparray2d(ptr, row, col)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int

        """
        return _pcammls.uint16_t_ARRAY_ptr_as_nparray2d(ptr, row, col)

    @staticmethod
    def ptr_as_nparray3d(ptr, row, col, psz):
        r"""
        ptr_as_nparray3d(ptr, row, col, psz)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int
        psz: int

        """
        return _pcammls.uint16_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    def as_nparray1d(self, row):
        r"""
        as_nparray1d(self, row)

        Parameters
        ----------
        row: int

        """
        return _pcammls.uint16_t_ARRAY_as_nparray1d(self, row)

    def as_nparray2d(self, row, col):
        r"""
        as_nparray2d(self, row, col)

        Parameters
        ----------
        row: int
        col: int

        """
        return _pcammls.uint16_t_ARRAY_as_nparray2d(self, row, col)

    def as_nparray3d(self, row, col, psz):
        r"""
        as_nparray3d(self, row, col, psz)

        Parameters
        ----------
        row: int
        col: int
        psz: int

        """
        return _pcammls.uint16_t_ARRAY_as_nparray3d(self, row, col, psz)

    @staticmethod
    def from_nparray(np_array):
        dim = np_array.ndim
        if dim == 3:
            return uint16_t_ARRAY.from_nparray3d(np_array)
        elif dim == 2:
            return uint16_t_ARRAY.from_nparray2d(np_array)
        elif dim == 1:
            return uint16_t_ARRAY.from_nparray1d(np_array)
        else:
            raise Exception("not support format")

    @staticmethod
    def release(ptr):
        uint16_t_ARRAY.Release(ptr)

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> uint16_t_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.uint16_t_ARRAY_swiginit(self, _pcammls.new_uint16_t_ARRAY(nelements))

    __swig_destroy__ = _pcammls.delete_uint16_t_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> uint16_t

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.uint16_t_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: uint16_t

        """
        return _pcammls.uint16_t_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> uint16_t *"""
        return _pcammls.uint16_t_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> uint16_t_ARRAY

        Parameters
        ----------
        t: uint16_t *

        """
        return _pcammls.uint16_t_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> uint16_t_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.uint16_t_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.uint16_t_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: uint16_t_ARRAY *

        """
        return _pcammls.uint16_t_ARRAY_ReleasePtr(p)


# Register uint16_t_ARRAY in _pcammls:
_pcammls.uint16_t_ARRAY_swigregister(uint16_t_ARRAY)


def uint16_t_ARRAY_from_nparray1d(NP_ARRAY_PTR):
    r"""
    uint16_t_ARRAY_from_nparray1d(NP_ARRAY_PTR) -> uint16_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: uint16_t *

    """
    return _pcammls.uint16_t_ARRAY_from_nparray1d(NP_ARRAY_PTR)


def uint16_t_ARRAY_from_nparray2d(NP_ARRAY_PTR):
    r"""
    uint16_t_ARRAY_from_nparray2d(NP_ARRAY_PTR) -> uint16_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: uint16_t *

    """
    return _pcammls.uint16_t_ARRAY_from_nparray2d(NP_ARRAY_PTR)


def uint16_t_ARRAY_from_nparray3d(NP_ARRAY_PTR):
    r"""
    uint16_t_ARRAY_from_nparray3d(NP_ARRAY_PTR) -> uint16_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: uint16_t *

    """
    return _pcammls.uint16_t_ARRAY_from_nparray3d(NP_ARRAY_PTR)


def uint16_t_ARRAY_Release(ptr):
    r"""
    uint16_t_ARRAY_Release(ptr)

    Parameters
    ----------
    ptr: uint16_t_ARRAY *

    """
    return _pcammls.uint16_t_ARRAY_Release(ptr)


def uint16_t_ARRAY_ptr_as_nparray1d(ptr, row):
    r"""
    uint16_t_ARRAY_ptr_as_nparray1d(ptr, row)

    Parameters
    ----------
    ptr: void *
    row: int

    """
    return _pcammls.uint16_t_ARRAY_ptr_as_nparray1d(ptr, row)


def uint16_t_ARRAY_ptr_as_nparray2d(ptr, row, col):
    r"""
    uint16_t_ARRAY_ptr_as_nparray2d(ptr, row, col)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int

    """
    return _pcammls.uint16_t_ARRAY_ptr_as_nparray2d(ptr, row, col)


def uint16_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz):
    r"""
    uint16_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int
    psz: int

    """
    return _pcammls.uint16_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)


def uint16_t_ARRAY_frompointer(t):
    r"""
    uint16_t_ARRAY_frompointer(t) -> uint16_t_ARRAY

    Parameters
    ----------
    t: uint16_t *

    """
    return _pcammls.uint16_t_ARRAY_frompointer(t)


def uint16_t_ARRAY_FromVoidPtr(t, size):
    r"""
    uint16_t_ARRAY_FromVoidPtr(t, size) -> uint16_t_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.uint16_t_ARRAY_FromVoidPtr(t, size)


def uint16_t_ARRAY_ReleasePtr(p):
    r"""
    uint16_t_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: uint16_t_ARRAY *

    """
    return _pcammls.uint16_t_ARRAY_ReleasePtr(p)


class uint32_t_ARRAY(object):
    r"""Proxy of C++ uint32_t_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    @staticmethod
    def from_nparray1d(NP_ARRAY_PTR):
        r"""
        from_nparray1d(NP_ARRAY_PTR) -> uint32_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: uint32_t *

        """
        return _pcammls.uint32_t_ARRAY_from_nparray1d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray2d(NP_ARRAY_PTR):
        r"""
        from_nparray2d(NP_ARRAY_PTR) -> uint32_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: uint32_t *

        """
        return _pcammls.uint32_t_ARRAY_from_nparray2d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray3d(NP_ARRAY_PTR):
        r"""
        from_nparray3d(NP_ARRAY_PTR) -> uint32_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: uint32_t *

        """
        return _pcammls.uint32_t_ARRAY_from_nparray3d(NP_ARRAY_PTR)

    @staticmethod
    def Release(ptr):
        r"""
        Release(ptr)

        Parameters
        ----------
        ptr: uint32_t_ARRAY *

        """
        return _pcammls.uint32_t_ARRAY_Release(ptr)

    @staticmethod
    def ptr_as_nparray1d(ptr, row):
        r"""
        ptr_as_nparray1d(ptr, row)

        Parameters
        ----------
        ptr: void *
        row: int

        """
        return _pcammls.uint32_t_ARRAY_ptr_as_nparray1d(ptr, row)

    @staticmethod
    def ptr_as_nparray2d(ptr, row, col):
        r"""
        ptr_as_nparray2d(ptr, row, col)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int

        """
        return _pcammls.uint32_t_ARRAY_ptr_as_nparray2d(ptr, row, col)

    @staticmethod
    def ptr_as_nparray3d(ptr, row, col, psz):
        r"""
        ptr_as_nparray3d(ptr, row, col, psz)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int
        psz: int

        """
        return _pcammls.uint32_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    def as_nparray1d(self, row):
        r"""
        as_nparray1d(self, row)

        Parameters
        ----------
        row: int

        """
        return _pcammls.uint32_t_ARRAY_as_nparray1d(self, row)

    def as_nparray2d(self, row, col):
        r"""
        as_nparray2d(self, row, col)

        Parameters
        ----------
        row: int
        col: int

        """
        return _pcammls.uint32_t_ARRAY_as_nparray2d(self, row, col)

    def as_nparray3d(self, row, col, psz):
        r"""
        as_nparray3d(self, row, col, psz)

        Parameters
        ----------
        row: int
        col: int
        psz: int

        """
        return _pcammls.uint32_t_ARRAY_as_nparray3d(self, row, col, psz)

    @staticmethod
    def from_nparray(np_array):
        dim = np_array.ndim
        if dim == 3:
            return uint32_t_ARRAY.from_nparray3d(np_array)
        elif dim == 2:
            return uint32_t_ARRAY.from_nparray2d(np_array)
        elif dim == 1:
            return uint32_t_ARRAY.from_nparray1d(np_array)
        else:
            raise Exception("not support format")

    @staticmethod
    def release(ptr):
        uint32_t_ARRAY.Release(ptr)

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> uint32_t_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.uint32_t_ARRAY_swiginit(self, _pcammls.new_uint32_t_ARRAY(nelements))

    __swig_destroy__ = _pcammls.delete_uint32_t_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> uint32_t

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.uint32_t_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: uint32_t

        """
        return _pcammls.uint32_t_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> uint32_t *"""
        return _pcammls.uint32_t_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> uint32_t_ARRAY

        Parameters
        ----------
        t: uint32_t *

        """
        return _pcammls.uint32_t_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> uint32_t_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.uint32_t_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.uint32_t_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: uint32_t_ARRAY *

        """
        return _pcammls.uint32_t_ARRAY_ReleasePtr(p)


# Register uint32_t_ARRAY in _pcammls:
_pcammls.uint32_t_ARRAY_swigregister(uint32_t_ARRAY)


def uint32_t_ARRAY_from_nparray1d(NP_ARRAY_PTR):
    r"""
    uint32_t_ARRAY_from_nparray1d(NP_ARRAY_PTR) -> uint32_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: uint32_t *

    """
    return _pcammls.uint32_t_ARRAY_from_nparray1d(NP_ARRAY_PTR)


def uint32_t_ARRAY_from_nparray2d(NP_ARRAY_PTR):
    r"""
    uint32_t_ARRAY_from_nparray2d(NP_ARRAY_PTR) -> uint32_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: uint32_t *

    """
    return _pcammls.uint32_t_ARRAY_from_nparray2d(NP_ARRAY_PTR)


def uint32_t_ARRAY_from_nparray3d(NP_ARRAY_PTR):
    r"""
    uint32_t_ARRAY_from_nparray3d(NP_ARRAY_PTR) -> uint32_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: uint32_t *

    """
    return _pcammls.uint32_t_ARRAY_from_nparray3d(NP_ARRAY_PTR)


def uint32_t_ARRAY_Release(ptr):
    r"""
    uint32_t_ARRAY_Release(ptr)

    Parameters
    ----------
    ptr: uint32_t_ARRAY *

    """
    return _pcammls.uint32_t_ARRAY_Release(ptr)


def uint32_t_ARRAY_ptr_as_nparray1d(ptr, row):
    r"""
    uint32_t_ARRAY_ptr_as_nparray1d(ptr, row)

    Parameters
    ----------
    ptr: void *
    row: int

    """
    return _pcammls.uint32_t_ARRAY_ptr_as_nparray1d(ptr, row)


def uint32_t_ARRAY_ptr_as_nparray2d(ptr, row, col):
    r"""
    uint32_t_ARRAY_ptr_as_nparray2d(ptr, row, col)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int

    """
    return _pcammls.uint32_t_ARRAY_ptr_as_nparray2d(ptr, row, col)


def uint32_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz):
    r"""
    uint32_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int
    psz: int

    """
    return _pcammls.uint32_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)


def uint32_t_ARRAY_frompointer(t):
    r"""
    uint32_t_ARRAY_frompointer(t) -> uint32_t_ARRAY

    Parameters
    ----------
    t: uint32_t *

    """
    return _pcammls.uint32_t_ARRAY_frompointer(t)


def uint32_t_ARRAY_FromVoidPtr(t, size):
    r"""
    uint32_t_ARRAY_FromVoidPtr(t, size) -> uint32_t_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.uint32_t_ARRAY_FromVoidPtr(t, size)


def uint32_t_ARRAY_ReleasePtr(p):
    r"""
    uint32_t_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: uint32_t_ARRAY *

    """
    return _pcammls.uint32_t_ARRAY_ReleasePtr(p)


class int32_t_ARRAY(object):
    r"""Proxy of C++ int32_t_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    @staticmethod
    def from_nparray1d(NP_ARRAY_PTR):
        r"""
        from_nparray1d(NP_ARRAY_PTR) -> int32_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: int32_t *

        """
        return _pcammls.int32_t_ARRAY_from_nparray1d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray2d(NP_ARRAY_PTR):
        r"""
        from_nparray2d(NP_ARRAY_PTR) -> int32_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: int32_t *

        """
        return _pcammls.int32_t_ARRAY_from_nparray2d(NP_ARRAY_PTR)

    @staticmethod
    def from_nparray3d(NP_ARRAY_PTR):
        r"""
        from_nparray3d(NP_ARRAY_PTR) -> int32_t_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: int32_t *

        """
        return _pcammls.int32_t_ARRAY_from_nparray3d(NP_ARRAY_PTR)

    @staticmethod
    def Release(ptr):
        r"""
        Release(ptr)

        Parameters
        ----------
        ptr: int32_t_ARRAY *

        """
        return _pcammls.int32_t_ARRAY_Release(ptr)

    @staticmethod
    def ptr_as_nparray1d(ptr, row):
        r"""
        ptr_as_nparray1d(ptr, row)

        Parameters
        ----------
        ptr: void *
        row: int

        """
        return _pcammls.int32_t_ARRAY_ptr_as_nparray1d(ptr, row)

    @staticmethod
    def ptr_as_nparray2d(ptr, row, col):
        r"""
        ptr_as_nparray2d(ptr, row, col)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int

        """
        return _pcammls.int32_t_ARRAY_ptr_as_nparray2d(ptr, row, col)

    @staticmethod
    def ptr_as_nparray3d(ptr, row, col, psz):
        r"""
        ptr_as_nparray3d(ptr, row, col, psz)

        Parameters
        ----------
        ptr: void *
        row: int
        col: int
        psz: int

        """
        return _pcammls.int32_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    def as_nparray1d(self, row):
        r"""
        as_nparray1d(self, row)

        Parameters
        ----------
        row: int

        """
        return _pcammls.int32_t_ARRAY_as_nparray1d(self, row)

    def as_nparray2d(self, row, col):
        r"""
        as_nparray2d(self, row, col)

        Parameters
        ----------
        row: int
        col: int

        """
        return _pcammls.int32_t_ARRAY_as_nparray2d(self, row, col)

    def as_nparray3d(self, row, col, psz):
        r"""
        as_nparray3d(self, row, col, psz)

        Parameters
        ----------
        row: int
        col: int
        psz: int

        """
        return _pcammls.int32_t_ARRAY_as_nparray3d(self, row, col, psz)

    @staticmethod
    def from_nparray(np_array):
        dim = np_array.ndim
        if dim == 3:
            return int32_t_ARRAY.from_nparray3d(np_array)
        elif dim == 2:
            return int32_t_ARRAY.from_nparray2d(np_array)
        elif dim == 1:
            return int32_t_ARRAY.from_nparray1d(np_array)
        else:
            raise Exception("not support format")

    @staticmethod
    def release(ptr):
        int32_t_ARRAY.Release(ptr)

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> int32_t_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.int32_t_ARRAY_swiginit(self, _pcammls.new_int32_t_ARRAY(nelements))

    __swig_destroy__ = _pcammls.delete_int32_t_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> int32_t

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.int32_t_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: int32_t

        """
        return _pcammls.int32_t_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> int32_t *"""
        return _pcammls.int32_t_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> int32_t_ARRAY

        Parameters
        ----------
        t: int32_t *

        """
        return _pcammls.int32_t_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> int32_t_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.int32_t_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.int32_t_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: int32_t_ARRAY *

        """
        return _pcammls.int32_t_ARRAY_ReleasePtr(p)


# Register int32_t_ARRAY in _pcammls:
_pcammls.int32_t_ARRAY_swigregister(int32_t_ARRAY)


def int32_t_ARRAY_from_nparray1d(NP_ARRAY_PTR):
    r"""
    int32_t_ARRAY_from_nparray1d(NP_ARRAY_PTR) -> int32_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: int32_t *

    """
    return _pcammls.int32_t_ARRAY_from_nparray1d(NP_ARRAY_PTR)


def int32_t_ARRAY_from_nparray2d(NP_ARRAY_PTR):
    r"""
    int32_t_ARRAY_from_nparray2d(NP_ARRAY_PTR) -> int32_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: int32_t *

    """
    return _pcammls.int32_t_ARRAY_from_nparray2d(NP_ARRAY_PTR)


def int32_t_ARRAY_from_nparray3d(NP_ARRAY_PTR):
    r"""
    int32_t_ARRAY_from_nparray3d(NP_ARRAY_PTR) -> int32_t_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: int32_t *

    """
    return _pcammls.int32_t_ARRAY_from_nparray3d(NP_ARRAY_PTR)


def int32_t_ARRAY_Release(ptr):
    r"""
    int32_t_ARRAY_Release(ptr)

    Parameters
    ----------
    ptr: int32_t_ARRAY *

    """
    return _pcammls.int32_t_ARRAY_Release(ptr)


def int32_t_ARRAY_ptr_as_nparray1d(ptr, row):
    r"""
    int32_t_ARRAY_ptr_as_nparray1d(ptr, row)

    Parameters
    ----------
    ptr: void *
    row: int

    """
    return _pcammls.int32_t_ARRAY_ptr_as_nparray1d(ptr, row)


def int32_t_ARRAY_ptr_as_nparray2d(ptr, row, col):
    r"""
    int32_t_ARRAY_ptr_as_nparray2d(ptr, row, col)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int

    """
    return _pcammls.int32_t_ARRAY_ptr_as_nparray2d(ptr, row, col)


def int32_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz):
    r"""
    int32_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)

    Parameters
    ----------
    ptr: void *
    row: int
    col: int
    psz: int

    """
    return _pcammls.int32_t_ARRAY_ptr_as_nparray3d(ptr, row, col, psz)


def int32_t_ARRAY_frompointer(t):
    r"""
    int32_t_ARRAY_frompointer(t) -> int32_t_ARRAY

    Parameters
    ----------
    t: int32_t *

    """
    return _pcammls.int32_t_ARRAY_frompointer(t)


def int32_t_ARRAY_FromVoidPtr(t, size):
    r"""
    int32_t_ARRAY_FromVoidPtr(t, size) -> int32_t_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.int32_t_ARRAY_FromVoidPtr(t, size)


def int32_t_ARRAY_ReleasePtr(p):
    r"""
    int32_t_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: int32_t_ARRAY *

    """
    return _pcammls.int32_t_ARRAY_ReleasePtr(p)


class char_ARRAY(object):
    r"""Proxy of C++ char_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> char_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.char_ARRAY_swiginit(self, _pcammls.new_char_ARRAY(nelements))

    __swig_destroy__ = _pcammls.delete_char_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> char

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.char_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: char

        """
        return _pcammls.char_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> char *"""
        return _pcammls.char_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> char_ARRAY

        Parameters
        ----------
        t: char *

        """
        return _pcammls.char_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> char_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.char_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.char_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: char_ARRAY *

        """
        return _pcammls.char_ARRAY_ReleasePtr(p)


# Register char_ARRAY in _pcammls:
_pcammls.char_ARRAY_swigregister(char_ARRAY)


def char_ARRAY_frompointer(t):
    r"""
    char_ARRAY_frompointer(t) -> char_ARRAY

    Parameters
    ----------
    t: char *

    """
    return _pcammls.char_ARRAY_frompointer(t)


def char_ARRAY_FromVoidPtr(t, size):
    r"""
    char_ARRAY_FromVoidPtr(t, size) -> char_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.char_ARRAY_FromVoidPtr(t, size)


def char_ARRAY_ReleasePtr(p):
    r"""
    char_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: char_ARRAY *

    """
    return _pcammls.char_ARRAY_ReleasePtr(p)


class TY_VECT_3F_ARRAY(object):
    r"""Proxy of C++ TY_VECT_3F_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def as_nparray(self, arr_size):
        return float_ARRAY.ptr_as_nparray2d(self.VoidPtr(), arr_size, 3)

    @staticmethod
    def from_nparray(NP_ARRAY_PTR):
        r"""
        from_nparray(NP_ARRAY_PTR) -> TY_VECT_3F_ARRAY

        Parameters
        ----------
        NP_ARRAY_PTR: double *

        """
        return _pcammls.TY_VECT_3F_ARRAY_from_nparray(NP_ARRAY_PTR)

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_VECT_3F_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_VECT_3F_ARRAY_swiginit(
            self, _pcammls.new_TY_VECT_3F_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_VECT_3F_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_VECT_3F

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_VECT_3F_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_VECT_3F

        """
        return _pcammls.TY_VECT_3F_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_VECT_3F"""
        return _pcammls.TY_VECT_3F_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_VECT_3F_ARRAY

        Parameters
        ----------
        t: TY_VECT_3F *

        """
        return _pcammls.TY_VECT_3F_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_VECT_3F_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_VECT_3F_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_VECT_3F_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_VECT_3F_ARRAY *

        """
        return _pcammls.TY_VECT_3F_ARRAY_ReleasePtr(p)


# Register TY_VECT_3F_ARRAY in _pcammls:
_pcammls.TY_VECT_3F_ARRAY_swigregister(TY_VECT_3F_ARRAY)


def TY_VECT_3F_ARRAY_from_nparray(NP_ARRAY_PTR):
    r"""
    TY_VECT_3F_ARRAY_from_nparray(NP_ARRAY_PTR) -> TY_VECT_3F_ARRAY

    Parameters
    ----------
    NP_ARRAY_PTR: double *

    """
    return _pcammls.TY_VECT_3F_ARRAY_from_nparray(NP_ARRAY_PTR)


def TY_VECT_3F_ARRAY_frompointer(t):
    r"""
    TY_VECT_3F_ARRAY_frompointer(t) -> TY_VECT_3F_ARRAY

    Parameters
    ----------
    t: TY_VECT_3F *

    """
    return _pcammls.TY_VECT_3F_ARRAY_frompointer(t)


def TY_VECT_3F_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_VECT_3F_ARRAY_FromVoidPtr(t, size) -> TY_VECT_3F_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_VECT_3F_ARRAY_FromVoidPtr(t, size)


def TY_VECT_3F_ARRAY_ReleasePtr(p):
    r"""
    TY_VECT_3F_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_VECT_3F_ARRAY *

    """
    return _pcammls.TY_VECT_3F_ARRAY_ReleasePtr(p)


class TY_PIXEL_DESC_ARRAY(object):
    r"""Proxy of C++ TY_PIXEL_DESC_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_PIXEL_DESC_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_PIXEL_DESC_ARRAY_swiginit(
            self, _pcammls.new_TY_PIXEL_DESC_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_PIXEL_DESC_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_PIXEL_DESC

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_PIXEL_DESC_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_PIXEL_DESC

        """
        return _pcammls.TY_PIXEL_DESC_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_PIXEL_DESC"""
        return _pcammls.TY_PIXEL_DESC_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_PIXEL_DESC_ARRAY

        Parameters
        ----------
        t: TY_PIXEL_DESC *

        """
        return _pcammls.TY_PIXEL_DESC_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_PIXEL_DESC_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_PIXEL_DESC_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_PIXEL_DESC_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_PIXEL_DESC_ARRAY *

        """
        return _pcammls.TY_PIXEL_DESC_ARRAY_ReleasePtr(p)


# Register TY_PIXEL_DESC_ARRAY in _pcammls:
_pcammls.TY_PIXEL_DESC_ARRAY_swigregister(TY_PIXEL_DESC_ARRAY)


def TY_PIXEL_DESC_ARRAY_frompointer(t):
    r"""
    TY_PIXEL_DESC_ARRAY_frompointer(t) -> TY_PIXEL_DESC_ARRAY

    Parameters
    ----------
    t: TY_PIXEL_DESC *

    """
    return _pcammls.TY_PIXEL_DESC_ARRAY_frompointer(t)


def TY_PIXEL_DESC_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_PIXEL_DESC_ARRAY_FromVoidPtr(t, size) -> TY_PIXEL_DESC_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_PIXEL_DESC_ARRAY_FromVoidPtr(t, size)


def TY_PIXEL_DESC_ARRAY_ReleasePtr(p):
    r"""
    TY_PIXEL_DESC_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_PIXEL_DESC_ARRAY *

    """
    return _pcammls.TY_PIXEL_DESC_ARRAY_ReleasePtr(p)


class TY_PIXEL_COLOR_DESC_ARRAY(object):
    r"""Proxy of C++ TY_PIXEL_COLOR_DESC_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_PIXEL_COLOR_DESC_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_PIXEL_COLOR_DESC_ARRAY_swiginit(
            self, _pcammls.new_TY_PIXEL_COLOR_DESC_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_PIXEL_COLOR_DESC_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_PIXEL_COLOR_DESC

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_PIXEL_COLOR_DESC_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_PIXEL_COLOR_DESC

        """
        return _pcammls.TY_PIXEL_COLOR_DESC_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_PIXEL_COLOR_DESC"""
        return _pcammls.TY_PIXEL_COLOR_DESC_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_PIXEL_COLOR_DESC_ARRAY

        Parameters
        ----------
        t: TY_PIXEL_COLOR_DESC *

        """
        return _pcammls.TY_PIXEL_COLOR_DESC_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_PIXEL_COLOR_DESC_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_PIXEL_COLOR_DESC_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_PIXEL_COLOR_DESC_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_PIXEL_COLOR_DESC_ARRAY *

        """
        return _pcammls.TY_PIXEL_COLOR_DESC_ARRAY_ReleasePtr(p)


# Register TY_PIXEL_COLOR_DESC_ARRAY in _pcammls:
_pcammls.TY_PIXEL_COLOR_DESC_ARRAY_swigregister(TY_PIXEL_COLOR_DESC_ARRAY)


def TY_PIXEL_COLOR_DESC_ARRAY_frompointer(t):
    r"""
    TY_PIXEL_COLOR_DESC_ARRAY_frompointer(t) -> TY_PIXEL_COLOR_DESC_ARRAY

    Parameters
    ----------
    t: TY_PIXEL_COLOR_DESC *

    """
    return _pcammls.TY_PIXEL_COLOR_DESC_ARRAY_frompointer(t)


def TY_PIXEL_COLOR_DESC_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_PIXEL_COLOR_DESC_ARRAY_FromVoidPtr(t, size) -> TY_PIXEL_COLOR_DESC_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_PIXEL_COLOR_DESC_ARRAY_FromVoidPtr(t, size)


def TY_PIXEL_COLOR_DESC_ARRAY_ReleasePtr(p):
    r"""
    TY_PIXEL_COLOR_DESC_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_PIXEL_COLOR_DESC_ARRAY *

    """
    return _pcammls.TY_PIXEL_COLOR_DESC_ARRAY_ReleasePtr(p)


class TY_INTERFACE_INFO_ARRAY(object):
    r"""Proxy of C++ TY_INTERFACE_INFO_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_INTERFACE_INFO_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_INTERFACE_INFO_ARRAY_swiginit(
            self, _pcammls.new_TY_INTERFACE_INFO_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_INTERFACE_INFO_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_INTERFACE_INFO

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_INTERFACE_INFO_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_INTERFACE_INFO

        """
        return _pcammls.TY_INTERFACE_INFO_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_INTERFACE_INFO"""
        return _pcammls.TY_INTERFACE_INFO_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_INTERFACE_INFO_ARRAY

        Parameters
        ----------
        t: TY_INTERFACE_INFO *

        """
        return _pcammls.TY_INTERFACE_INFO_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_INTERFACE_INFO_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_INTERFACE_INFO_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_INTERFACE_INFO_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_INTERFACE_INFO_ARRAY *

        """
        return _pcammls.TY_INTERFACE_INFO_ARRAY_ReleasePtr(p)


# Register TY_INTERFACE_INFO_ARRAY in _pcammls:
_pcammls.TY_INTERFACE_INFO_ARRAY_swigregister(TY_INTERFACE_INFO_ARRAY)


def TY_INTERFACE_INFO_ARRAY_frompointer(t):
    r"""
    TY_INTERFACE_INFO_ARRAY_frompointer(t) -> TY_INTERFACE_INFO_ARRAY

    Parameters
    ----------
    t: TY_INTERFACE_INFO *

    """
    return _pcammls.TY_INTERFACE_INFO_ARRAY_frompointer(t)


def TY_INTERFACE_INFO_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_INTERFACE_INFO_ARRAY_FromVoidPtr(t, size) -> TY_INTERFACE_INFO_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_INTERFACE_INFO_ARRAY_FromVoidPtr(t, size)


def TY_INTERFACE_INFO_ARRAY_ReleasePtr(p):
    r"""
    TY_INTERFACE_INFO_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_INTERFACE_INFO_ARRAY *

    """
    return _pcammls.TY_INTERFACE_INFO_ARRAY_ReleasePtr(p)


class TY_DEVICE_BASE_INFO_ARRAY(object):
    r"""Proxy of C++ TY_DEVICE_BASE_INFO_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_DEVICE_BASE_INFO_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_DEVICE_BASE_INFO_ARRAY_swiginit(
            self, _pcammls.new_TY_DEVICE_BASE_INFO_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_DEVICE_BASE_INFO_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_DEVICE_BASE_INFO

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_DEVICE_BASE_INFO_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_DEVICE_BASE_INFO

        """
        return _pcammls.TY_DEVICE_BASE_INFO_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_DEVICE_BASE_INFO"""
        return _pcammls.TY_DEVICE_BASE_INFO_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_DEVICE_BASE_INFO_ARRAY

        Parameters
        ----------
        t: TY_DEVICE_BASE_INFO *

        """
        return _pcammls.TY_DEVICE_BASE_INFO_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_DEVICE_BASE_INFO_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_DEVICE_BASE_INFO_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_DEVICE_BASE_INFO_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_DEVICE_BASE_INFO_ARRAY *

        """
        return _pcammls.TY_DEVICE_BASE_INFO_ARRAY_ReleasePtr(p)


# Register TY_DEVICE_BASE_INFO_ARRAY in _pcammls:
_pcammls.TY_DEVICE_BASE_INFO_ARRAY_swigregister(TY_DEVICE_BASE_INFO_ARRAY)


def TY_DEVICE_BASE_INFO_ARRAY_frompointer(t):
    r"""
    TY_DEVICE_BASE_INFO_ARRAY_frompointer(t) -> TY_DEVICE_BASE_INFO_ARRAY

    Parameters
    ----------
    t: TY_DEVICE_BASE_INFO *

    """
    return _pcammls.TY_DEVICE_BASE_INFO_ARRAY_frompointer(t)


def TY_DEVICE_BASE_INFO_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_DEVICE_BASE_INFO_ARRAY_FromVoidPtr(t, size) -> TY_DEVICE_BASE_INFO_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_DEVICE_BASE_INFO_ARRAY_FromVoidPtr(t, size)


def TY_DEVICE_BASE_INFO_ARRAY_ReleasePtr(p):
    r"""
    TY_DEVICE_BASE_INFO_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_DEVICE_BASE_INFO_ARRAY *

    """
    return _pcammls.TY_DEVICE_BASE_INFO_ARRAY_ReleasePtr(p)


class TY_FEATURE_INFO_ARRAY(object):
    r"""Proxy of C++ TY_FEATURE_INFO_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_FEATURE_INFO_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_FEATURE_INFO_ARRAY_swiginit(
            self, _pcammls.new_TY_FEATURE_INFO_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_FEATURE_INFO_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_FEATURE_INFO

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_FEATURE_INFO_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_FEATURE_INFO

        """
        return _pcammls.TY_FEATURE_INFO_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_FEATURE_INFO"""
        return _pcammls.TY_FEATURE_INFO_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_FEATURE_INFO_ARRAY

        Parameters
        ----------
        t: TY_FEATURE_INFO *

        """
        return _pcammls.TY_FEATURE_INFO_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_FEATURE_INFO_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_FEATURE_INFO_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_FEATURE_INFO_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_FEATURE_INFO_ARRAY *

        """
        return _pcammls.TY_FEATURE_INFO_ARRAY_ReleasePtr(p)


# Register TY_FEATURE_INFO_ARRAY in _pcammls:
_pcammls.TY_FEATURE_INFO_ARRAY_swigregister(TY_FEATURE_INFO_ARRAY)


def TY_FEATURE_INFO_ARRAY_frompointer(t):
    r"""
    TY_FEATURE_INFO_ARRAY_frompointer(t) -> TY_FEATURE_INFO_ARRAY

    Parameters
    ----------
    t: TY_FEATURE_INFO *

    """
    return _pcammls.TY_FEATURE_INFO_ARRAY_frompointer(t)


def TY_FEATURE_INFO_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_FEATURE_INFO_ARRAY_FromVoidPtr(t, size) -> TY_FEATURE_INFO_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_FEATURE_INFO_ARRAY_FromVoidPtr(t, size)


def TY_FEATURE_INFO_ARRAY_ReleasePtr(p):
    r"""
    TY_FEATURE_INFO_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_FEATURE_INFO_ARRAY *

    """
    return _pcammls.TY_FEATURE_INFO_ARRAY_ReleasePtr(p)


class TY_ENUM_ENTRY_ARRAY(object):
    r"""Proxy of C++ TY_ENUM_ENTRY_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_ENUM_ENTRY_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_ENUM_ENTRY_ARRAY_swiginit(
            self, _pcammls.new_TY_ENUM_ENTRY_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_ENUM_ENTRY_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_ENUM_ENTRY

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_ENUM_ENTRY_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_ENUM_ENTRY

        """
        return _pcammls.TY_ENUM_ENTRY_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_ENUM_ENTRY"""
        return _pcammls.TY_ENUM_ENTRY_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_ENUM_ENTRY_ARRAY

        Parameters
        ----------
        t: TY_ENUM_ENTRY *

        """
        return _pcammls.TY_ENUM_ENTRY_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_ENUM_ENTRY_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_ENUM_ENTRY_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_ENUM_ENTRY_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_ENUM_ENTRY_ARRAY *

        """
        return _pcammls.TY_ENUM_ENTRY_ARRAY_ReleasePtr(p)


# Register TY_ENUM_ENTRY_ARRAY in _pcammls:
_pcammls.TY_ENUM_ENTRY_ARRAY_swigregister(TY_ENUM_ENTRY_ARRAY)


def TY_ENUM_ENTRY_ARRAY_frompointer(t):
    r"""
    TY_ENUM_ENTRY_ARRAY_frompointer(t) -> TY_ENUM_ENTRY_ARRAY

    Parameters
    ----------
    t: TY_ENUM_ENTRY *

    """
    return _pcammls.TY_ENUM_ENTRY_ARRAY_frompointer(t)


def TY_ENUM_ENTRY_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_ENUM_ENTRY_ARRAY_FromVoidPtr(t, size) -> TY_ENUM_ENTRY_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_ENUM_ENTRY_ARRAY_FromVoidPtr(t, size)


def TY_ENUM_ENTRY_ARRAY_ReleasePtr(p):
    r"""
    TY_ENUM_ENTRY_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_ENUM_ENTRY_ARRAY *

    """
    return _pcammls.TY_ENUM_ENTRY_ARRAY_ReleasePtr(p)


class TY_IMAGE_DATA_ARRAY(object):
    r"""Proxy of C++ TY_IMAGE_DATA_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_IMAGE_DATA_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_IMAGE_DATA_ARRAY_swiginit(
            self, _pcammls.new_TY_IMAGE_DATA_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_IMAGE_DATA_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_IMAGE_DATA

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_IMAGE_DATA_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_IMAGE_DATA

        """
        return _pcammls.TY_IMAGE_DATA_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_IMAGE_DATA"""
        return _pcammls.TY_IMAGE_DATA_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_IMAGE_DATA_ARRAY

        Parameters
        ----------
        t: TY_IMAGE_DATA *

        """
        return _pcammls.TY_IMAGE_DATA_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_IMAGE_DATA_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_IMAGE_DATA_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_IMAGE_DATA_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_IMAGE_DATA_ARRAY *

        """
        return _pcammls.TY_IMAGE_DATA_ARRAY_ReleasePtr(p)


# Register TY_IMAGE_DATA_ARRAY in _pcammls:
_pcammls.TY_IMAGE_DATA_ARRAY_swigregister(TY_IMAGE_DATA_ARRAY)


def TY_IMAGE_DATA_ARRAY_frompointer(t):
    r"""
    TY_IMAGE_DATA_ARRAY_frompointer(t) -> TY_IMAGE_DATA_ARRAY

    Parameters
    ----------
    t: TY_IMAGE_DATA *

    """
    return _pcammls.TY_IMAGE_DATA_ARRAY_frompointer(t)


def TY_IMAGE_DATA_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_IMAGE_DATA_ARRAY_FromVoidPtr(t, size) -> TY_IMAGE_DATA_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_IMAGE_DATA_ARRAY_FromVoidPtr(t, size)


def TY_IMAGE_DATA_ARRAY_ReleasePtr(p):
    r"""
    TY_IMAGE_DATA_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_IMAGE_DATA_ARRAY *

    """
    return _pcammls.TY_IMAGE_DATA_ARRAY_ReleasePtr(p)


class TY_FRAME_DATA_ARRAY(object):
    r"""Proxy of C++ TY_FRAME_DATA_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_FRAME_DATA_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_FRAME_DATA_ARRAY_swiginit(
            self, _pcammls.new_TY_FRAME_DATA_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_FRAME_DATA_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_FRAME_DATA

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_FRAME_DATA_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_FRAME_DATA

        """
        return _pcammls.TY_FRAME_DATA_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_FRAME_DATA"""
        return _pcammls.TY_FRAME_DATA_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_FRAME_DATA_ARRAY

        Parameters
        ----------
        t: TY_FRAME_DATA *

        """
        return _pcammls.TY_FRAME_DATA_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_FRAME_DATA_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_FRAME_DATA_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_FRAME_DATA_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_FRAME_DATA_ARRAY *

        """
        return _pcammls.TY_FRAME_DATA_ARRAY_ReleasePtr(p)


# Register TY_FRAME_DATA_ARRAY in _pcammls:
_pcammls.TY_FRAME_DATA_ARRAY_swigregister(TY_FRAME_DATA_ARRAY)


def TY_FRAME_DATA_ARRAY_frompointer(t):
    r"""
    TY_FRAME_DATA_ARRAY_frompointer(t) -> TY_FRAME_DATA_ARRAY

    Parameters
    ----------
    t: TY_FRAME_DATA *

    """
    return _pcammls.TY_FRAME_DATA_ARRAY_frompointer(t)


def TY_FRAME_DATA_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_FRAME_DATA_ARRAY_FromVoidPtr(t, size) -> TY_FRAME_DATA_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_FRAME_DATA_ARRAY_FromVoidPtr(t, size)


def TY_FRAME_DATA_ARRAY_ReleasePtr(p):
    r"""
    TY_FRAME_DATA_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_FRAME_DATA_ARRAY *

    """
    return _pcammls.TY_FRAME_DATA_ARRAY_ReleasePtr(p)


class TY_CAMERA_INTRINSIC_ARRAY(object):
    r"""Proxy of C++ TY_CAMERA_INTRINSIC_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_CAMERA_INTRINSIC_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_CAMERA_INTRINSIC_ARRAY_swiginit(
            self, _pcammls.new_TY_CAMERA_INTRINSIC_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_CAMERA_INTRINSIC_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_CAMERA_INTRINSIC

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_CAMERA_INTRINSIC_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_CAMERA_INTRINSIC

        """
        return _pcammls.TY_CAMERA_INTRINSIC_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_CAMERA_INTRINSIC"""
        return _pcammls.TY_CAMERA_INTRINSIC_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_CAMERA_INTRINSIC_ARRAY

        Parameters
        ----------
        t: TY_CAMERA_INTRINSIC *

        """
        return _pcammls.TY_CAMERA_INTRINSIC_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_CAMERA_INTRINSIC_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_CAMERA_INTRINSIC_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_CAMERA_INTRINSIC_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_CAMERA_INTRINSIC_ARRAY *

        """
        return _pcammls.TY_CAMERA_INTRINSIC_ARRAY_ReleasePtr(p)


# Register TY_CAMERA_INTRINSIC_ARRAY in _pcammls:
_pcammls.TY_CAMERA_INTRINSIC_ARRAY_swigregister(TY_CAMERA_INTRINSIC_ARRAY)


def TY_CAMERA_INTRINSIC_ARRAY_frompointer(t):
    r"""
    TY_CAMERA_INTRINSIC_ARRAY_frompointer(t) -> TY_CAMERA_INTRINSIC_ARRAY

    Parameters
    ----------
    t: TY_CAMERA_INTRINSIC *

    """
    return _pcammls.TY_CAMERA_INTRINSIC_ARRAY_frompointer(t)


def TY_CAMERA_INTRINSIC_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_CAMERA_INTRINSIC_ARRAY_FromVoidPtr(t, size) -> TY_CAMERA_INTRINSIC_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_CAMERA_INTRINSIC_ARRAY_FromVoidPtr(t, size)


def TY_CAMERA_INTRINSIC_ARRAY_ReleasePtr(p):
    r"""
    TY_CAMERA_INTRINSIC_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_CAMERA_INTRINSIC_ARRAY *

    """
    return _pcammls.TY_CAMERA_INTRINSIC_ARRAY_ReleasePtr(p)


class TY_CAMERA_ROTATION_ARRAY(object):
    r"""Proxy of C++ TY_CAMERA_ROTATION_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_CAMERA_ROTATION_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_CAMERA_ROTATION_ARRAY_swiginit(
            self, _pcammls.new_TY_CAMERA_ROTATION_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_CAMERA_ROTATION_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_CAMERA_ROTATION

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_CAMERA_ROTATION_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_CAMERA_ROTATION

        """
        return _pcammls.TY_CAMERA_ROTATION_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_CAMERA_ROTATION"""
        return _pcammls.TY_CAMERA_ROTATION_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_CAMERA_ROTATION_ARRAY

        Parameters
        ----------
        t: TY_CAMERA_ROTATION *

        """
        return _pcammls.TY_CAMERA_ROTATION_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_CAMERA_ROTATION_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_CAMERA_ROTATION_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_CAMERA_ROTATION_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_CAMERA_ROTATION_ARRAY *

        """
        return _pcammls.TY_CAMERA_ROTATION_ARRAY_ReleasePtr(p)


# Register TY_CAMERA_ROTATION_ARRAY in _pcammls:
_pcammls.TY_CAMERA_ROTATION_ARRAY_swigregister(TY_CAMERA_ROTATION_ARRAY)


def TY_CAMERA_ROTATION_ARRAY_frompointer(t):
    r"""
    TY_CAMERA_ROTATION_ARRAY_frompointer(t) -> TY_CAMERA_ROTATION_ARRAY

    Parameters
    ----------
    t: TY_CAMERA_ROTATION *

    """
    return _pcammls.TY_CAMERA_ROTATION_ARRAY_frompointer(t)


def TY_CAMERA_ROTATION_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_CAMERA_ROTATION_ARRAY_FromVoidPtr(t, size) -> TY_CAMERA_ROTATION_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_CAMERA_ROTATION_ARRAY_FromVoidPtr(t, size)


def TY_CAMERA_ROTATION_ARRAY_ReleasePtr(p):
    r"""
    TY_CAMERA_ROTATION_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_CAMERA_ROTATION_ARRAY *

    """
    return _pcammls.TY_CAMERA_ROTATION_ARRAY_ReleasePtr(p)


class TY_CAMERA_EXTRINSIC_ARRAY(object):
    r"""Proxy of C++ TY_CAMERA_EXTRINSIC_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_CAMERA_EXTRINSIC_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_CAMERA_EXTRINSIC_ARRAY_swiginit(
            self, _pcammls.new_TY_CAMERA_EXTRINSIC_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_CAMERA_EXTRINSIC_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_CAMERA_EXTRINSIC

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_CAMERA_EXTRINSIC_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_CAMERA_EXTRINSIC

        """
        return _pcammls.TY_CAMERA_EXTRINSIC_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_CAMERA_EXTRINSIC"""
        return _pcammls.TY_CAMERA_EXTRINSIC_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_CAMERA_EXTRINSIC_ARRAY

        Parameters
        ----------
        t: TY_CAMERA_EXTRINSIC *

        """
        return _pcammls.TY_CAMERA_EXTRINSIC_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_CAMERA_EXTRINSIC_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_CAMERA_EXTRINSIC_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_CAMERA_EXTRINSIC_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_CAMERA_EXTRINSIC_ARRAY *

        """
        return _pcammls.TY_CAMERA_EXTRINSIC_ARRAY_ReleasePtr(p)


# Register TY_CAMERA_EXTRINSIC_ARRAY in _pcammls:
_pcammls.TY_CAMERA_EXTRINSIC_ARRAY_swigregister(TY_CAMERA_EXTRINSIC_ARRAY)


def TY_CAMERA_EXTRINSIC_ARRAY_frompointer(t):
    r"""
    TY_CAMERA_EXTRINSIC_ARRAY_frompointer(t) -> TY_CAMERA_EXTRINSIC_ARRAY

    Parameters
    ----------
    t: TY_CAMERA_EXTRINSIC *

    """
    return _pcammls.TY_CAMERA_EXTRINSIC_ARRAY_frompointer(t)


def TY_CAMERA_EXTRINSIC_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_CAMERA_EXTRINSIC_ARRAY_FromVoidPtr(t, size) -> TY_CAMERA_EXTRINSIC_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_CAMERA_EXTRINSIC_ARRAY_FromVoidPtr(t, size)


def TY_CAMERA_EXTRINSIC_ARRAY_ReleasePtr(p):
    r"""
    TY_CAMERA_EXTRINSIC_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_CAMERA_EXTRINSIC_ARRAY *

    """
    return _pcammls.TY_CAMERA_EXTRINSIC_ARRAY_ReleasePtr(p)


class TY_CAMERA_DISTORTION_ARRAY(object):
    r"""Proxy of C++ TY_CAMERA_DISTORTION_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_CAMERA_DISTORTION_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_CAMERA_DISTORTION_ARRAY_swiginit(
            self, _pcammls.new_TY_CAMERA_DISTORTION_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_CAMERA_DISTORTION_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_CAMERA_DISTORTION

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_CAMERA_DISTORTION_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_CAMERA_DISTORTION

        """
        return _pcammls.TY_CAMERA_DISTORTION_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_CAMERA_DISTORTION"""
        return _pcammls.TY_CAMERA_DISTORTION_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_CAMERA_DISTORTION_ARRAY

        Parameters
        ----------
        t: TY_CAMERA_DISTORTION *

        """
        return _pcammls.TY_CAMERA_DISTORTION_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_CAMERA_DISTORTION_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_CAMERA_DISTORTION_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_CAMERA_DISTORTION_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_CAMERA_DISTORTION_ARRAY *

        """
        return _pcammls.TY_CAMERA_DISTORTION_ARRAY_ReleasePtr(p)


# Register TY_CAMERA_DISTORTION_ARRAY in _pcammls:
_pcammls.TY_CAMERA_DISTORTION_ARRAY_swigregister(TY_CAMERA_DISTORTION_ARRAY)


def TY_CAMERA_DISTORTION_ARRAY_frompointer(t):
    r"""
    TY_CAMERA_DISTORTION_ARRAY_frompointer(t) -> TY_CAMERA_DISTORTION_ARRAY

    Parameters
    ----------
    t: TY_CAMERA_DISTORTION *

    """
    return _pcammls.TY_CAMERA_DISTORTION_ARRAY_frompointer(t)


def TY_CAMERA_DISTORTION_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_CAMERA_DISTORTION_ARRAY_FromVoidPtr(t, size) -> TY_CAMERA_DISTORTION_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_CAMERA_DISTORTION_ARRAY_FromVoidPtr(t, size)


def TY_CAMERA_DISTORTION_ARRAY_ReleasePtr(p):
    r"""
    TY_CAMERA_DISTORTION_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_CAMERA_DISTORTION_ARRAY *

    """
    return _pcammls.TY_CAMERA_DISTORTION_ARRAY_ReleasePtr(p)


class TY_CAMERA_CALIB_INFO_ARRAY(object):
    r"""Proxy of C++ TY_CAMERA_CALIB_INFO_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> TY_CAMERA_CALIB_INFO_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.TY_CAMERA_CALIB_INFO_ARRAY_swiginit(
            self, _pcammls.new_TY_CAMERA_CALIB_INFO_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_TY_CAMERA_CALIB_INFO_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> TY_CAMERA_CALIB_INFO

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.TY_CAMERA_CALIB_INFO_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: TY_CAMERA_CALIB_INFO

        """
        return _pcammls.TY_CAMERA_CALIB_INFO_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> TY_CAMERA_CALIB_INFO"""
        return _pcammls.TY_CAMERA_CALIB_INFO_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> TY_CAMERA_CALIB_INFO_ARRAY

        Parameters
        ----------
        t: TY_CAMERA_CALIB_INFO *

        """
        return _pcammls.TY_CAMERA_CALIB_INFO_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> TY_CAMERA_CALIB_INFO_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.TY_CAMERA_CALIB_INFO_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.TY_CAMERA_CALIB_INFO_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: TY_CAMERA_CALIB_INFO_ARRAY *

        """
        return _pcammls.TY_CAMERA_CALIB_INFO_ARRAY_ReleasePtr(p)


# Register TY_CAMERA_CALIB_INFO_ARRAY in _pcammls:
_pcammls.TY_CAMERA_CALIB_INFO_ARRAY_swigregister(TY_CAMERA_CALIB_INFO_ARRAY)


def TY_CAMERA_CALIB_INFO_ARRAY_frompointer(t):
    r"""
    TY_CAMERA_CALIB_INFO_ARRAY_frompointer(t) -> TY_CAMERA_CALIB_INFO_ARRAY

    Parameters
    ----------
    t: TY_CAMERA_CALIB_INFO *

    """
    return _pcammls.TY_CAMERA_CALIB_INFO_ARRAY_frompointer(t)


def TY_CAMERA_CALIB_INFO_ARRAY_FromVoidPtr(t, size):
    r"""
    TY_CAMERA_CALIB_INFO_ARRAY_FromVoidPtr(t, size) -> TY_CAMERA_CALIB_INFO_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.TY_CAMERA_CALIB_INFO_ARRAY_FromVoidPtr(t, size)


def TY_CAMERA_CALIB_INFO_ARRAY_ReleasePtr(p):
    r"""
    TY_CAMERA_CALIB_INFO_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: TY_CAMERA_CALIB_INFO_ARRAY *

    """
    return _pcammls.TY_CAMERA_CALIB_INFO_ARRAY_ReleasePtr(p)


class pointcloud_data_ARRAY(object):
    r"""Proxy of C++ pointcloud_data_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> pointcloud_data_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.pointcloud_data_ARRAY_swiginit(
            self, _pcammls.new_pointcloud_data_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_pointcloud_data_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> pointcloud_data

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.pointcloud_data_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: pointcloud_data

        """
        return _pcammls.pointcloud_data_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> pointcloud_data"""
        return _pcammls.pointcloud_data_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> pointcloud_data_ARRAY

        Parameters
        ----------
        t: pointcloud_data *

        """
        return _pcammls.pointcloud_data_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> pointcloud_data_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.pointcloud_data_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.pointcloud_data_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: pointcloud_data_ARRAY *

        """
        return _pcammls.pointcloud_data_ARRAY_ReleasePtr(p)


# Register pointcloud_data_ARRAY in _pcammls:
_pcammls.pointcloud_data_ARRAY_swigregister(pointcloud_data_ARRAY)


def pointcloud_data_ARRAY_frompointer(t):
    r"""
    pointcloud_data_ARRAY_frompointer(t) -> pointcloud_data_ARRAY

    Parameters
    ----------
    t: pointcloud_data *

    """
    return _pcammls.pointcloud_data_ARRAY_frompointer(t)


def pointcloud_data_ARRAY_FromVoidPtr(t, size):
    r"""
    pointcloud_data_ARRAY_FromVoidPtr(t, size) -> pointcloud_data_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.pointcloud_data_ARRAY_FromVoidPtr(t, size)


def pointcloud_data_ARRAY_ReleasePtr(p):
    r"""
    pointcloud_data_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: pointcloud_data_ARRAY *

    """
    return _pcammls.pointcloud_data_ARRAY_ReleasePtr(p)


class pointcloud_data_list_ARRAY(object):
    r"""Proxy of C++ pointcloud_data_list_ARRAY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, nelements):
        r"""
        __init__(self, nelements) -> pointcloud_data_list_ARRAY

        Parameters
        ----------
        nelements: size_t

        """
        _pcammls.pointcloud_data_list_ARRAY_swiginit(
            self, _pcammls.new_pointcloud_data_list_ARRAY(nelements)
        )

    __swig_destroy__ = _pcammls.delete_pointcloud_data_list_ARRAY

    def __getitem__(self, index):
        r"""
        __getitem__(self, index) -> pointcloud_data_list

        Parameters
        ----------
        index: size_t

        """
        return _pcammls.pointcloud_data_list_ARRAY___getitem__(self, index)

    def __setitem__(self, index, value):
        r"""
        __setitem__(self, index, value)

        Parameters
        ----------
        index: size_t
        value: pointcloud_data_list

        """
        return _pcammls.pointcloud_data_list_ARRAY___setitem__(self, index, value)

    def cast(self):
        r"""cast(self) -> pointcloud_data_list"""
        return _pcammls.pointcloud_data_list_ARRAY_cast(self)

    @staticmethod
    def frompointer(t):
        r"""
        frompointer(t) -> pointcloud_data_list_ARRAY

        Parameters
        ----------
        t: pointcloud_data_list *

        """
        return _pcammls.pointcloud_data_list_ARRAY_frompointer(t)

    @staticmethod
    def FromVoidPtr(t, size):
        r"""
        FromVoidPtr(t, size) -> pointcloud_data_list_ARRAY

        Parameters
        ----------
        t: void *
        size: int

        """
        return _pcammls.pointcloud_data_list_ARRAY_FromVoidPtr(t, size)

    def VoidPtr(self):
        r"""VoidPtr(self) -> void *"""
        return _pcammls.pointcloud_data_list_ARRAY_VoidPtr(self)

    @staticmethod
    def ReleasePtr(p):
        r"""
        ReleasePtr(p)

        Parameters
        ----------
        p: pointcloud_data_list_ARRAY *

        """
        return _pcammls.pointcloud_data_list_ARRAY_ReleasePtr(p)


# Register pointcloud_data_list_ARRAY in _pcammls:
_pcammls.pointcloud_data_list_ARRAY_swigregister(pointcloud_data_list_ARRAY)


def pointcloud_data_list_ARRAY_frompointer(t):
    r"""
    pointcloud_data_list_ARRAY_frompointer(t) -> pointcloud_data_list_ARRAY

    Parameters
    ----------
    t: pointcloud_data_list *

    """
    return _pcammls.pointcloud_data_list_ARRAY_frompointer(t)


def pointcloud_data_list_ARRAY_FromVoidPtr(t, size):
    r"""
    pointcloud_data_list_ARRAY_FromVoidPtr(t, size) -> pointcloud_data_list_ARRAY

    Parameters
    ----------
    t: void *
    size: int

    """
    return _pcammls.pointcloud_data_list_ARRAY_FromVoidPtr(t, size)


def pointcloud_data_list_ARRAY_ReleasePtr(p):
    r"""
    pointcloud_data_list_ARRAY_ReleasePtr(p)

    Parameters
    ----------
    p: pointcloud_data_list_ARRAY *

    """
    return _pcammls.pointcloud_data_list_ARRAY_ReleasePtr(p)


class EventCallback(object):
    r"""Proxy of C++ EventCallback class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    __swig_destroy__ = _pcammls.delete_EventCallback

    def run(self, info):
        r"""
        run(self, info)

        Parameters
        ----------
        info: TY_EVENT_INFO *

        """
        return _pcammls.EventCallback_run(self, info)

    def set_to(self, handle):
        r"""
        set_to(self, handle) -> TY_STATUS

        Parameters
        ----------
        handle: TY_DEV_HANDLE

        """
        return _pcammls.EventCallback_set_to(self, handle)

    def __init__(self):
        r"""
        __init__(self) -> EventCallback

        Parameters
        ----------
        self: PyObject *

        """
        if self.__class__ == EventCallback:
            _self = None
        else:
            _self = self
        _pcammls.EventCallback_swiginit(
            self,
            _pcammls.new_EventCallback(
                _self,
            ),
        )

    def __disown__(self):
        self.this.disown()
        _pcammls.disown_EventCallback(self)
        return weakref.proxy(self)


# Register EventCallback in _pcammls:
_pcammls.EventCallback_swigregister(EventCallback)


def CPointerSize():
    r"""CPointerSize() -> int"""
    return _pcammls.CPointerSize()


__bool_true_false_are_defined = _pcammls.__bool_true_false_are_defined

TY_STATUS_OK = _pcammls.TY_STATUS_OK

TY_STATUS_ERROR = _pcammls.TY_STATUS_ERROR

TY_STATUS_NOT_INITED = _pcammls.TY_STATUS_NOT_INITED

TY_STATUS_NOT_IMPLEMENTED = _pcammls.TY_STATUS_NOT_IMPLEMENTED

TY_STATUS_NOT_PERMITTED = _pcammls.TY_STATUS_NOT_PERMITTED

TY_STATUS_DEVICE_ERROR = _pcammls.TY_STATUS_DEVICE_ERROR

TY_STATUS_INVALID_PARAMETER = _pcammls.TY_STATUS_INVALID_PARAMETER

TY_STATUS_INVALID_HANDLE = _pcammls.TY_STATUS_INVALID_HANDLE

TY_STATUS_INVALID_COMPONENT = _pcammls.TY_STATUS_INVALID_COMPONENT

TY_STATUS_INVALID_FEATURE = _pcammls.TY_STATUS_INVALID_FEATURE

TY_STATUS_WRONG_TYPE = _pcammls.TY_STATUS_WRONG_TYPE

TY_STATUS_WRONG_SIZE = _pcammls.TY_STATUS_WRONG_SIZE

TY_STATUS_OUT_OF_MEMORY = _pcammls.TY_STATUS_OUT_OF_MEMORY

TY_STATUS_OUT_OF_RANGE = _pcammls.TY_STATUS_OUT_OF_RANGE

TY_STATUS_TIMEOUT = _pcammls.TY_STATUS_TIMEOUT

TY_STATUS_WRONG_MODE = _pcammls.TY_STATUS_WRONG_MODE

TY_STATUS_BUSY = _pcammls.TY_STATUS_BUSY

TY_STATUS_IDLE = _pcammls.TY_STATUS_IDLE

TY_STATUS_NO_DATA = _pcammls.TY_STATUS_NO_DATA

TY_STATUS_NO_BUFFER = _pcammls.TY_STATUS_NO_BUFFER

TY_STATUS_NULL_POINTER = _pcammls.TY_STATUS_NULL_POINTER

TY_STATUS_READONLY_FEATURE = _pcammls.TY_STATUS_READONLY_FEATURE

TY_STATUS_INVALID_DESCRIPTOR = _pcammls.TY_STATUS_INVALID_DESCRIPTOR

TY_STATUS_INVALID_INTERFACE = _pcammls.TY_STATUS_INVALID_INTERFACE

TY_STATUS_FIRMWARE_ERROR = _pcammls.TY_STATUS_FIRMWARE_ERROR

TY_STATUS_DEV_EPERM = _pcammls.TY_STATUS_DEV_EPERM

TY_STATUS_DEV_EIO = _pcammls.TY_STATUS_DEV_EIO

TY_STATUS_DEV_ENOMEM = _pcammls.TY_STATUS_DEV_ENOMEM

TY_STATUS_DEV_EBUSY = _pcammls.TY_STATUS_DEV_EBUSY

TY_STATUS_DEV_EINVAL = _pcammls.TY_STATUS_DEV_EINVAL

TY_FW_ERRORCODE_CAM0_NOT_DETECTED = _pcammls.TY_FW_ERRORCODE_CAM0_NOT_DETECTED

TY_FW_ERRORCODE_CAM1_NOT_DETECTED = _pcammls.TY_FW_ERRORCODE_CAM1_NOT_DETECTED

TY_FW_ERRORCODE_CAM2_NOT_DETECTED = _pcammls.TY_FW_ERRORCODE_CAM2_NOT_DETECTED

TY_FW_ERRORCODE_POE_NOT_INIT = _pcammls.TY_FW_ERRORCODE_POE_NOT_INIT

TY_FW_ERRORCODE_RECMAP_NOT_CORRECT = _pcammls.TY_FW_ERRORCODE_RECMAP_NOT_CORRECT

TY_FW_ERRORCODE_LOOKUPTABLE_NOT_CORRECT = (
    _pcammls.TY_FW_ERRORCODE_LOOKUPTABLE_NOT_CORRECT
)

TY_FW_ERRORCODE_DRV8899_NOT_INIT = _pcammls.TY_FW_ERRORCODE_DRV8899_NOT_INIT

TY_FW_ERRORCODE_FOC_START_ERR = _pcammls.TY_FW_ERRORCODE_FOC_START_ERR

TY_FW_ERRORCODE_CONFIG_NOT_FOUND = _pcammls.TY_FW_ERRORCODE_CONFIG_NOT_FOUND

TY_FW_ERRORCODE_CONFIG_NOT_CORRECT = _pcammls.TY_FW_ERRORCODE_CONFIG_NOT_CORRECT

TY_FW_ERRORCODE_XML_NOT_FOUND = _pcammls.TY_FW_ERRORCODE_XML_NOT_FOUND

TY_FW_ERRORCODE_XML_NOT_CORRECT = _pcammls.TY_FW_ERRORCODE_XML_NOT_CORRECT

TY_FW_ERRORCODE_XML_OVERRIDE_FAILED = _pcammls.TY_FW_ERRORCODE_XML_OVERRIDE_FAILED

TY_FW_ERRORCODE_CAM_INIT_FAILED = _pcammls.TY_FW_ERRORCODE_CAM_INIT_FAILED

TY_FW_ERRORCODE_LASER_INIT_FAILED = _pcammls.TY_FW_ERRORCODE_LASER_INIT_FAILED

TY_EVENT_DEVICE_OFFLINE = _pcammls.TY_EVENT_DEVICE_OFFLINE

TY_EVENT_LICENSE_ERROR = _pcammls.TY_EVENT_LICENSE_ERROR

TY_EVENT_FW_INIT_ERROR = _pcammls.TY_EVENT_FW_INIT_ERROR

TY_COMPONENT_DEVICE = _pcammls.TY_COMPONENT_DEVICE
r""" Abstract component stands for whole device, always enabled"""
TY_COMPONENT_DEPTH_CAM = _pcammls.TY_COMPONENT_DEPTH_CAM
r""" Depth camera"""
TY_COMPONENT_IR_CAM_LEFT = _pcammls.TY_COMPONENT_IR_CAM_LEFT
r""" Left IR camera"""
TY_COMPONENT_IR_CAM_RIGHT = _pcammls.TY_COMPONENT_IR_CAM_RIGHT
r""" Right IR camera"""
TY_COMPONENT_RGB_CAM_LEFT = _pcammls.TY_COMPONENT_RGB_CAM_LEFT
r""" Left RGB camera"""
TY_COMPONENT_RGB_CAM_RIGHT = _pcammls.TY_COMPONENT_RGB_CAM_RIGHT
r""" Right RGB camera"""
TY_COMPONENT_LASER = _pcammls.TY_COMPONENT_LASER
r""" Laser"""
TY_COMPONENT_IMU = _pcammls.TY_COMPONENT_IMU
r""" Inertial Measurement Unit"""
TY_COMPONENT_BRIGHT_HISTO = _pcammls.TY_COMPONENT_BRIGHT_HISTO
r""" virtual component for brightness histogram of ir"""
TY_COMPONENT_STORAGE = _pcammls.TY_COMPONENT_STORAGE
r""" virtual component for device storage"""
TY_COMPONENT_RGB_CAM = _pcammls.TY_COMPONENT_RGB_CAM
r""" Some device has only one RGB camera, map it to left"""
TY_FEATURE_INT = _pcammls.TY_FEATURE_INT

TY_FEATURE_FLOAT = _pcammls.TY_FEATURE_FLOAT

TY_FEATURE_ENUM = _pcammls.TY_FEATURE_ENUM

TY_FEATURE_BOOL = _pcammls.TY_FEATURE_BOOL

TY_FEATURE_STRING = _pcammls.TY_FEATURE_STRING

TY_FEATURE_BYTEARRAY = _pcammls.TY_FEATURE_BYTEARRAY

TY_FEATURE_STRUCT = _pcammls.TY_FEATURE_STRUCT

TY_STRUCT_CAM_INTRINSIC = _pcammls.TY_STRUCT_CAM_INTRINSIC
r""" see TY_CAMERA_INTRINSIC"""
TY_STRUCT_EXTRINSIC_TO_DEPTH = _pcammls.TY_STRUCT_EXTRINSIC_TO_DEPTH
r""" extrinsic between  depth cam and current component , see TY_CAMERA_EXTRINSIC"""
TY_STRUCT_EXTRINSIC_TO_IR_LEFT = _pcammls.TY_STRUCT_EXTRINSIC_TO_IR_LEFT
r""" extrinsic between  left IR and current compoent, see TY_CAMERA_EXTRINSIC"""
TY_STRUCT_CAM_RECTIFIED_ROTATION = _pcammls.TY_STRUCT_CAM_RECTIFIED_ROTATION
r""" see TY_CAMERA_ROTATION"""
TY_STRUCT_CAM_DISTORTION = _pcammls.TY_STRUCT_CAM_DISTORTION
r""" see TY_CAMERA_DISTORTION"""
TY_STRUCT_CAM_CALIB_DATA = _pcammls.TY_STRUCT_CAM_CALIB_DATA
r""" see TY_CAMERA_CALIB_INFO"""
TY_STRUCT_CAM_RECTIFIED_INTRI = _pcammls.TY_STRUCT_CAM_RECTIFIED_INTRI
r""" the rectified intrinsic. see TY_CAMERA_INTRINSIC"""
TY_BYTEARRAY_CUSTOM_BLOCK = _pcammls.TY_BYTEARRAY_CUSTOM_BLOCK
r""" used for reading/writing custom block"""
TY_BYTEARRAY_ISP_BLOCK = _pcammls.TY_BYTEARRAY_ISP_BLOCK
r""" used for reading/writing fpn block"""
TY_INT_PERSISTENT_IP = _pcammls.TY_INT_PERSISTENT_IP

TY_INT_PERSISTENT_SUBMASK = _pcammls.TY_INT_PERSISTENT_SUBMASK

TY_INT_PERSISTENT_GATEWAY = _pcammls.TY_INT_PERSISTENT_GATEWAY

TY_BOOL_GVSP_RESEND = _pcammls.TY_BOOL_GVSP_RESEND

TY_INT_PACKET_DELAY = _pcammls.TY_INT_PACKET_DELAY
r""" microseconds"""
TY_INT_ACCEPTABLE_PERCENT = _pcammls.TY_INT_ACCEPTABLE_PERCENT

TY_INT_NTP_SERVER_IP = _pcammls.TY_INT_NTP_SERVER_IP
r""" Ntp server IP"""
TY_INT_PACKET_SIZE = _pcammls.TY_INT_PACKET_SIZE

TY_INT_LINK_CMD_TIMEOUT = _pcammls.TY_INT_LINK_CMD_TIMEOUT
r""" milliseconds"""
TY_STRUCT_CAM_STATISTICS = _pcammls.TY_STRUCT_CAM_STATISTICS
r""" statistical information, see TY_CAMERA_STATISTICS"""
TY_INT_WIDTH_MAX = _pcammls.TY_INT_WIDTH_MAX

TY_INT_HEIGHT_MAX = _pcammls.TY_INT_HEIGHT_MAX

TY_INT_OFFSET_X = _pcammls.TY_INT_OFFSET_X

TY_INT_OFFSET_Y = _pcammls.TY_INT_OFFSET_Y

TY_INT_WIDTH = _pcammls.TY_INT_WIDTH
r""" Image width"""
TY_INT_HEIGHT = _pcammls.TY_INT_HEIGHT
r""" Image height"""
TY_ENUM_IMAGE_MODE = _pcammls.TY_ENUM_IMAGE_MODE
r""" Resolution-PixelFromat mode, see TY_IMAGE_MODE_LIST"""
TY_FLOAT_SCALE_UNIT = _pcammls.TY_FLOAT_SCALE_UNIT
r"""
    scale unit
    depth image is uint16 pixel format with default millimeter unit ,for some device  can output Sub-millimeter accuracy data
    the acutal depth (mm)= PixelValue * ScaleUnit
    """
TY_ENUM_TRIGGER_POL = _pcammls.TY_ENUM_TRIGGER_POL
r""" Trigger POL, see TY_TRIGGER_POL_LIST"""
TY_INT_FRAME_PER_TRIGGER = _pcammls.TY_INT_FRAME_PER_TRIGGER
r""" Number of frames captured per trigger"""
TY_STRUCT_TRIGGER_PARAM = _pcammls.TY_STRUCT_TRIGGER_PARAM
r""" param of trigger, see TY_TRIGGER_PARAM"""
TY_STRUCT_TRIGGER_PARAM_EX = _pcammls.TY_STRUCT_TRIGGER_PARAM_EX
r""" param of trigger, see TY_TRIGGER_PARAM_EX"""
TY_STRUCT_TRIGGER_TIMER_LIST = _pcammls.TY_STRUCT_TRIGGER_TIMER_LIST
r""" param of trigger mode 20, see TY_TRIGGER_TIMER_LIST"""
TY_STRUCT_TRIGGER_TIMER_PERIOD = _pcammls.TY_STRUCT_TRIGGER_TIMER_PERIOD
r""" param of trigger mode 21, see TY_TRIGGER_TIMER_PERIOD"""
TY_BOOL_KEEP_ALIVE_ONOFF = _pcammls.TY_BOOL_KEEP_ALIVE_ONOFF
r""" Keep Alive switch"""
TY_INT_KEEP_ALIVE_TIMEOUT = _pcammls.TY_INT_KEEP_ALIVE_TIMEOUT
r""" Keep Alive timeout"""
TY_BOOL_CMOS_SYNC = _pcammls.TY_BOOL_CMOS_SYNC
r""" Cmos sync switch"""
TY_INT_TRIGGER_DELAY_US = _pcammls.TY_INT_TRIGGER_DELAY_US
r""" Trigger delay time, in microseconds"""
TY_BOOL_TRIGGER_OUT_IO = _pcammls.TY_BOOL_TRIGGER_OUT_IO
r""" Trigger out IO"""
TY_INT_TRIGGER_DURATION_US = _pcammls.TY_INT_TRIGGER_DURATION_US
r""" Trigger duration time, in microseconds"""
TY_ENUM_STREAM_ASYNC = _pcammls.TY_ENUM_STREAM_ASYNC
r""" stream async switch, see TY_STREAM_ASYNC_MODE"""
TY_INT_CAPTURE_TIME_US = _pcammls.TY_INT_CAPTURE_TIME_US
r""" capture time in multi-ir"""
TY_ENUM_TIME_SYNC_TYPE = _pcammls.TY_ENUM_TIME_SYNC_TYPE
r""" see TY_TIME_SYNC_TYPE"""
TY_BOOL_TIME_SYNC_READY = _pcammls.TY_BOOL_TIME_SYNC_READY
r""" time sync done status"""
TY_BOOL_IR_FLASHLIGHT = _pcammls.TY_BOOL_IR_FLASHLIGHT
r""" Enable switch for floodlight used in ir component"""
TY_INT_IR_FLASHLIGHT_INTENSITY = _pcammls.TY_INT_IR_FLASHLIGHT_INTENSITY
r""" ir component flashlight intensity level"""
TY_STRUCT_DO0_WORKMODE = _pcammls.TY_STRUCT_DO0_WORKMODE
r""" DO_0 workmode, see TY_DO_WORKMODE"""
TY_STRUCT_DI0_WORKMODE = _pcammls.TY_STRUCT_DI0_WORKMODE
r""" DI_0 workmode, see TY_DI_WORKMODE"""
TY_STRUCT_DO1_WORKMODE = _pcammls.TY_STRUCT_DO1_WORKMODE
r""" DO_1 workmode, see TY_DO_WORKMODE"""
TY_STRUCT_DI1_WORKMODE = _pcammls.TY_STRUCT_DI1_WORKMODE
r""" DI_1 workmode, see TY_DI_WORKMODE"""
TY_STRUCT_DO2_WORKMODE = _pcammls.TY_STRUCT_DO2_WORKMODE
r""" DO_2 workmode, see TY_DO_WORKMODE"""
TY_STRUCT_DI2_WORKMODE = _pcammls.TY_STRUCT_DI2_WORKMODE
r""" DI_2 workmode, see TY_DI_WORKMODE"""
TY_BOOL_RGB_FLASHLIGHT = _pcammls.TY_BOOL_RGB_FLASHLIGHT
r""" Enable switch for floodlight used in rgb component"""
TY_INT_RGB_FLASHLIGHT_INTENSITY = _pcammls.TY_INT_RGB_FLASHLIGHT_INTENSITY
r""" rgb component flashlight intensity level"""
TY_ENUM_CONFIG_MODE = _pcammls.TY_ENUM_CONFIG_MODE

TY_ENUM_TEMPERATURE_ID = _pcammls.TY_ENUM_TEMPERATURE_ID

TY_STRUCT_TEMPERATURE = _pcammls.TY_STRUCT_TEMPERATURE

TY_BOOL_AUTO_EXPOSURE = _pcammls.TY_BOOL_AUTO_EXPOSURE
r""" Auto exposure switch"""
TY_INT_EXPOSURE_TIME = _pcammls.TY_INT_EXPOSURE_TIME
r""" Exposure time"""
TY_BOOL_AUTO_GAIN = _pcammls.TY_BOOL_AUTO_GAIN
r""" Auto gain switch"""
TY_INT_GAIN = _pcammls.TY_INT_GAIN
r""" Sensor Gain"""
TY_BOOL_AUTO_AWB = _pcammls.TY_BOOL_AUTO_AWB
r""" Auto white balance"""
TY_STRUCT_AEC_ROI = _pcammls.TY_STRUCT_AEC_ROI
r""" region of aec statistics, see TY_AEC_ROI_PARAM"""
TY_INT_TOF_HDR_RATIO = _pcammls.TY_INT_TOF_HDR_RATIO
r""" tof sensor hdr ratio for depth"""
TY_INT_TOF_JITTER_THRESHOLD = _pcammls.TY_INT_TOF_JITTER_THRESHOLD
r""" tof jitter threshold for depth"""
TY_FLOAT_EXPOSURE_TIME_US = _pcammls.TY_FLOAT_EXPOSURE_TIME_US
r""" the exposure time, unit: us"""
TY_INT_LASER_POWER = _pcammls.TY_INT_LASER_POWER
r""" Laser power level"""
TY_BOOL_LASER_AUTO_CTRL = _pcammls.TY_BOOL_LASER_AUTO_CTRL
r""" Laser auto ctrl"""
TY_STRUCT_LASER_PATTERN = _pcammls.TY_STRUCT_LASER_PATTERN

TY_INT_LASER_CAM_TRIG_POS = _pcammls.TY_INT_LASER_CAM_TRIG_POS

TY_INT_LASER_CAM_TRIG_LEN = _pcammls.TY_INT_LASER_CAM_TRIG_LEN

TY_INT_LASER_LUT_TRIG_POS = _pcammls.TY_INT_LASER_LUT_TRIG_POS

TY_INT_LASER_LUT_NUM = _pcammls.TY_INT_LASER_LUT_NUM

TY_INT_LASER_PATTERN_OFFSET = _pcammls.TY_INT_LASER_PATTERN_OFFSET

TY_INT_LASER_MIRROR_NUM = _pcammls.TY_INT_LASER_MIRROR_NUM

TY_INT_LASER_MIRROR_SEL = _pcammls.TY_INT_LASER_MIRROR_SEL

TY_INT_LASER_LUT_IDX = _pcammls.TY_INT_LASER_LUT_IDX

TY_INT_LASER_FACET_IDX = _pcammls.TY_INT_LASER_FACET_IDX

TY_INT_LASER_FACET_POS = _pcammls.TY_INT_LASER_FACET_POS

TY_INT_LASER_MODE = _pcammls.TY_INT_LASER_MODE

TY_INT_CONST_DRV_DUTY = _pcammls.TY_INT_CONST_DRV_DUTY

TY_STRUCT_LASER_ENABLE_BY_IDX = _pcammls.TY_STRUCT_LASER_ENABLE_BY_IDX
r""" Laser enable by device index"""
TY_STRUCT_LASER_POWER_BY_IDX = _pcammls.TY_STRUCT_LASER_POWER_BY_IDX
r""" Laser power by device index"""
TY_STRUCT_FLOOD_ENABLE_BY_IDX = _pcammls.TY_STRUCT_FLOOD_ENABLE_BY_IDX
r""" Flood enable by device index"""
TY_STRUCT_FLOOD_POWER_BY_IDX = _pcammls.TY_STRUCT_FLOOD_POWER_BY_IDX
r""" Flood power by device index"""
TY_BOOL_UNDISTORTION = _pcammls.TY_BOOL_UNDISTORTION
r""" Output undistorted image"""
TY_BOOL_BRIGHTNESS_HISTOGRAM = _pcammls.TY_BOOL_BRIGHTNESS_HISTOGRAM
r""" Output bright histogram"""
TY_BOOL_DEPTH_POSTPROC = _pcammls.TY_BOOL_DEPTH_POSTPROC
r""" Do depth image postproc"""
TY_INT_R_GAIN = _pcammls.TY_INT_R_GAIN
r""" Gain of R channel"""
TY_INT_G_GAIN = _pcammls.TY_INT_G_GAIN
r""" Gain of G channel"""
TY_INT_B_GAIN = _pcammls.TY_INT_B_GAIN
r""" Gain of B channel"""
TY_INT_ANALOG_GAIN = _pcammls.TY_INT_ANALOG_GAIN
r""" Analog gain"""
TY_BOOL_HDR = _pcammls.TY_BOOL_HDR
r""" HDR func enable/disable"""
TY_BYTEARRAY_HDR_PARAMETER = _pcammls.TY_BYTEARRAY_HDR_PARAMETER
r""" HDR parameters"""
TY_INT_AE_TARGET_Y = _pcammls.TY_INT_AE_TARGET_Y

TY_BOOL_IMU_DATA_ONOFF = _pcammls.TY_BOOL_IMU_DATA_ONOFF
r"""AE target y IMU Data Onoff"""
TY_STRUCT_IMU_ACC_BIAS = _pcammls.TY_STRUCT_IMU_ACC_BIAS
r""" IMU acc bias matrix, see TY_ACC_BIAS"""
TY_STRUCT_IMU_ACC_MISALIGNMENT = _pcammls.TY_STRUCT_IMU_ACC_MISALIGNMENT
r""" IMU acc misalignment matrix, see TY_ACC_MISALIGNMENT"""
TY_STRUCT_IMU_ACC_SCALE = _pcammls.TY_STRUCT_IMU_ACC_SCALE
r""" IMU acc scale matrix, see TY_ACC_SCALE"""
TY_STRUCT_IMU_GYRO_BIAS = _pcammls.TY_STRUCT_IMU_GYRO_BIAS
r""" IMU gyro bias matrix, see TY_GYRO_BIAS"""
TY_STRUCT_IMU_GYRO_MISALIGNMENT = _pcammls.TY_STRUCT_IMU_GYRO_MISALIGNMENT
r""" IMU gyro misalignment matrix, see TY_GYRO_MISALIGNMENT"""
TY_STRUCT_IMU_GYRO_SCALE = _pcammls.TY_STRUCT_IMU_GYRO_SCALE
r""" IMU gyro scale matrix, see TY_GYRO_SCALE"""
TY_STRUCT_IMU_CAM_TO_IMU = _pcammls.TY_STRUCT_IMU_CAM_TO_IMU
r""" IMU camera to imu matrix, see TY_CAMERA_TO_IMU"""
TY_ENUM_IMU_FPS = _pcammls.TY_ENUM_IMU_FPS
r""" IMU fps, see TY_IMU_FPS_LIST"""
TY_INT_SGBM_IMAGE_NUM = _pcammls.TY_INT_SGBM_IMAGE_NUM
r""" SGBM image channel num"""
TY_INT_SGBM_DISPARITY_NUM = _pcammls.TY_INT_SGBM_DISPARITY_NUM
r""" SGBM disparity num"""
TY_INT_SGBM_DISPARITY_OFFSET = _pcammls.TY_INT_SGBM_DISPARITY_OFFSET
r""" SGBM disparity offset"""
TY_INT_SGBM_MATCH_WIN_HEIGHT = _pcammls.TY_INT_SGBM_MATCH_WIN_HEIGHT
r""" SGBM match window height"""
TY_INT_SGBM_SEMI_PARAM_P1 = _pcammls.TY_INT_SGBM_SEMI_PARAM_P1
r""" SGBM semi global param p1"""
TY_INT_SGBM_SEMI_PARAM_P2 = _pcammls.TY_INT_SGBM_SEMI_PARAM_P2
r""" SGBM semi global param p2"""
TY_INT_SGBM_UNIQUE_FACTOR = _pcammls.TY_INT_SGBM_UNIQUE_FACTOR
r""" SGBM uniqueness factor param"""
TY_INT_SGBM_UNIQUE_ABSDIFF = _pcammls.TY_INT_SGBM_UNIQUE_ABSDIFF
r""" SGBM uniqueness min absolute diff"""
TY_INT_SGBM_UNIQUE_MAX_COST = _pcammls.TY_INT_SGBM_UNIQUE_MAX_COST
r""" SGBM uniqueness max cost param"""
TY_BOOL_SGBM_HFILTER_HALF_WIN = _pcammls.TY_BOOL_SGBM_HFILTER_HALF_WIN
r""" SGBM enable half window size"""
TY_INT_SGBM_MATCH_WIN_WIDTH = _pcammls.TY_INT_SGBM_MATCH_WIN_WIDTH
r""" SGBM match window width"""
TY_BOOL_SGBM_MEDFILTER = _pcammls.TY_BOOL_SGBM_MEDFILTER
r""" SGBM enable median filter"""
TY_BOOL_SGBM_LRC = _pcammls.TY_BOOL_SGBM_LRC
r""" SGBM enable left right consist check"""
TY_INT_SGBM_LRC_DIFF = _pcammls.TY_INT_SGBM_LRC_DIFF
r""" SGBM max diff"""
TY_INT_SGBM_MEDFILTER_THRESH = _pcammls.TY_INT_SGBM_MEDFILTER_THRESH
r""" SGBM median filter thresh"""
TY_INT_SGBM_SEMI_PARAM_P1_SCALE = _pcammls.TY_INT_SGBM_SEMI_PARAM_P1_SCALE
r""" SGBM semi global param p1 scale"""
TY_INT_SGPM_PHASE_NUM = _pcammls.TY_INT_SGPM_PHASE_NUM
r""" Phase num to calc a depth"""
TY_INT_SGPM_NORMAL_PHASE_SCALE = _pcammls.TY_INT_SGPM_NORMAL_PHASE_SCALE
r""" phase scale when calc a depth"""
TY_INT_SGPM_NORMAL_PHASE_OFFSET = _pcammls.TY_INT_SGPM_NORMAL_PHASE_OFFSET
r""" Phase offset when calc a depth"""
TY_INT_SGPM_REF_PHASE_SCALE = _pcammls.TY_INT_SGPM_REF_PHASE_SCALE
r""" Reference Phase scale when calc a depth"""
TY_INT_SGPM_REF_PHASE_OFFSET = _pcammls.TY_INT_SGPM_REF_PHASE_OFFSET
r""" Reference Phase offset when calc a depth"""
TY_FLOAT_SGPM_EPI_HS = _pcammls.TY_FLOAT_SGPM_EPI_HS
r""" Epipolar Constraint pattern scale"""
TY_INT_SGPM_EPI_HF = _pcammls.TY_INT_SGPM_EPI_HF
r""" Epipolar Constraint pattern offset"""
TY_BOOL_SGPM_EPI_EN = _pcammls.TY_BOOL_SGPM_EPI_EN
r""" Epipolar Constraint enable"""
TY_INT_SGPM_EPI_CH0 = _pcammls.TY_INT_SGPM_EPI_CH0
r""" Epipolar Constraint channel0"""
TY_INT_SGPM_EPI_CH1 = _pcammls.TY_INT_SGPM_EPI_CH1
r""" Epipolar Constraint channel1"""
TY_INT_SGPM_EPI_THRESH = _pcammls.TY_INT_SGPM_EPI_THRESH
r""" Epipolar Constraint thresh"""
TY_BOOL_SGPM_ORDER_FILTER_EN = _pcammls.TY_BOOL_SGPM_ORDER_FILTER_EN
r""" Phase order filter enable"""
TY_INT_SGPM_ORDER_FILTER_CHN = _pcammls.TY_INT_SGPM_ORDER_FILTER_CHN
r""" Phase order filter channel"""
TY_INT_DEPTH_MIN_MM = _pcammls.TY_INT_DEPTH_MIN_MM
r""" min depth in mm output"""
TY_INT_DEPTH_MAX_MM = _pcammls.TY_INT_DEPTH_MAX_MM
r""" max depth in mm ouput"""
TY_INT_SGBM_TEXTURE_OFFSET = _pcammls.TY_INT_SGBM_TEXTURE_OFFSET
r""" texture filter value offset"""
TY_INT_SGBM_TEXTURE_THRESH = _pcammls.TY_INT_SGBM_TEXTURE_THRESH
r""" texture filter threshold"""
TY_STRUCT_PHC_GROUP_ATTR = _pcammls.TY_STRUCT_PHC_GROUP_ATTR
r""" Phase compute group attribute"""
TY_ENUM_DEPTH_QUALITY = _pcammls.TY_ENUM_DEPTH_QUALITY
r""" the quality of generated depth, see TY_DEPTH_QUALITY"""
TY_INT_FILTER_THRESHOLD = _pcammls.TY_INT_FILTER_THRESHOLD
r""" the threshold of the noise filter, 0 for disabled"""
TY_INT_TOF_CHANNEL = _pcammls.TY_INT_TOF_CHANNEL
r""" the frequency channel of tof"""
TY_INT_TOF_MODULATION_THRESHOLD = _pcammls.TY_INT_TOF_MODULATION_THRESHOLD
r""" the threshold of the tof modulation"""
TY_STRUCT_TOF_FREQ = _pcammls.TY_STRUCT_TOF_FREQ
r""" the frequency of tof, see TY_TOF_FREQ"""
TY_BOOL_TOF_ANTI_INTERFERENCE = _pcammls.TY_BOOL_TOF_ANTI_INTERFERENCE
r""" cooperation if multi-device used"""
TY_INT_TOF_ANTI_SUNLIGHT_INDEX = _pcammls.TY_INT_TOF_ANTI_SUNLIGHT_INDEX
r""" the index of anti-sunlight"""
TY_INT_MAX_SPECKLE_SIZE = _pcammls.TY_INT_MAX_SPECKLE_SIZE
r""" the max size of speckle"""
TY_INT_MAX_SPECKLE_DIFF = _pcammls.TY_INT_MAX_SPECKLE_DIFF
r""" the max diff of speckle"""
TY_CONFIG_MODE_PRESET0 = _pcammls.TY_CONFIG_MODE_PRESET0

TY_CONFIG_MODE_PRESET1 = _pcammls.TY_CONFIG_MODE_PRESET1

TY_CONFIG_MODE_PRESET2 = _pcammls.TY_CONFIG_MODE_PRESET2

TY_CONFIG_MODE_USERSET0 = _pcammls.TY_CONFIG_MODE_USERSET0

TY_CONFIG_MODE_USERSET1 = _pcammls.TY_CONFIG_MODE_USERSET1

TY_CONFIG_MODE_USERSET2 = _pcammls.TY_CONFIG_MODE_USERSET2

TY_DEPTH_QUALITY_BASIC = _pcammls.TY_DEPTH_QUALITY_BASIC

TY_DEPTH_QUALITY_MEDIUM = _pcammls.TY_DEPTH_QUALITY_MEDIUM

TY_DEPTH_QUALITY_HIGH = _pcammls.TY_DEPTH_QUALITY_HIGH

TY_TRIGGER_POL_FALLINGEDGE = _pcammls.TY_TRIGGER_POL_FALLINGEDGE

TY_TRIGGER_POL_RISINGEDGE = _pcammls.TY_TRIGGER_POL_RISINGEDGE

TY_INTERFACE_UNKNOWN = _pcammls.TY_INTERFACE_UNKNOWN

TY_INTERFACE_RAW = _pcammls.TY_INTERFACE_RAW

TY_INTERFACE_USB = _pcammls.TY_INTERFACE_USB

TY_INTERFACE_ETHERNET = _pcammls.TY_INTERFACE_ETHERNET

TY_INTERFACE_IEEE80211 = _pcammls.TY_INTERFACE_IEEE80211

TY_INTERFACE_ALL = _pcammls.TY_INTERFACE_ALL

TY_ACCESS_READABLE = _pcammls.TY_ACCESS_READABLE

TY_ACCESS_WRITABLE = _pcammls.TY_ACCESS_WRITABLE

TY_STREAM_ASYNC_OFF = _pcammls.TY_STREAM_ASYNC_OFF

TY_STREAM_ASYNC_DEPTH = _pcammls.TY_STREAM_ASYNC_DEPTH

TY_STREAM_ASYNC_RGB = _pcammls.TY_STREAM_ASYNC_RGB

TY_STREAM_ASYNC_DEPTH_RGB = _pcammls.TY_STREAM_ASYNC_DEPTH_RGB

TY_STREAM_ASYNC_ALL = _pcammls.TY_STREAM_ASYNC_ALL

TY_PIXEL_8BIT = _pcammls.TY_PIXEL_8BIT

TY_PIXEL_16BIT = _pcammls.TY_PIXEL_16BIT

TY_PIXEL_24BIT = _pcammls.TY_PIXEL_24BIT

TY_PIXEL_32BIT = _pcammls.TY_PIXEL_32BIT

TY_PIXEL_10BIT = _pcammls.TY_PIXEL_10BIT

TY_PIXEL_12BIT = _pcammls.TY_PIXEL_12BIT

TY_PIXEL_14BIT = _pcammls.TY_PIXEL_14BIT

TY_PIXEL_48BIT = _pcammls.TY_PIXEL_48BIT

TY_PIXEL_64BIT = _pcammls.TY_PIXEL_64BIT

TY_PIXEL_FORMAT_UNDEFINED = _pcammls.TY_PIXEL_FORMAT_UNDEFINED

TY_PIXEL_FORMAT_MONO = _pcammls.TY_PIXEL_FORMAT_MONO
r""" 0x10000000"""
TY_PIXEL_FORMAT_BAYER8GB = _pcammls.TY_PIXEL_FORMAT_BAYER8GB
r""" 0x11000000"""
TY_PIXEL_FORMAT_BAYER8BG = _pcammls.TY_PIXEL_FORMAT_BAYER8BG
r""" 0x12000000"""
TY_PIXEL_FORMAT_BAYER8GR = _pcammls.TY_PIXEL_FORMAT_BAYER8GR
r""" 0x13000000"""
TY_PIXEL_FORMAT_BAYER8RG = _pcammls.TY_PIXEL_FORMAT_BAYER8RG
r""" 0x14000000"""
TY_PIXEL_FORMAT_BAYER8GRBG = _pcammls.TY_PIXEL_FORMAT_BAYER8GRBG

TY_PIXEL_FORMAT_BAYER8RGGB = _pcammls.TY_PIXEL_FORMAT_BAYER8RGGB

TY_PIXEL_FORMAT_BAYER8GBRG = _pcammls.TY_PIXEL_FORMAT_BAYER8GBRG

TY_PIXEL_FORMAT_BAYER8BGGR = _pcammls.TY_PIXEL_FORMAT_BAYER8BGGR

TY_PIXEL_FORMAT_CSI_MONO10 = _pcammls.TY_PIXEL_FORMAT_CSI_MONO10
r""" 0x50000000"""
TY_PIXEL_FORMAT_CSI_BAYER10GRBG = _pcammls.TY_PIXEL_FORMAT_CSI_BAYER10GRBG
r""" 0x51000000"""
TY_PIXEL_FORMAT_CSI_BAYER10RGGB = _pcammls.TY_PIXEL_FORMAT_CSI_BAYER10RGGB
r""" 0x52000000"""
TY_PIXEL_FORMAT_CSI_BAYER10GBRG = _pcammls.TY_PIXEL_FORMAT_CSI_BAYER10GBRG
r""" 0x53000000"""
TY_PIXEL_FORMAT_CSI_BAYER10BGGR = _pcammls.TY_PIXEL_FORMAT_CSI_BAYER10BGGR
r""" 0x54000000"""
TY_PIXEL_FORMAT_CSI_MONO12 = _pcammls.TY_PIXEL_FORMAT_CSI_MONO12
r""" 0x60000000"""
TY_PIXEL_FORMAT_CSI_BAYER12GRBG = _pcammls.TY_PIXEL_FORMAT_CSI_BAYER12GRBG
r""" 0x61000000"""
TY_PIXEL_FORMAT_CSI_BAYER12RGGB = _pcammls.TY_PIXEL_FORMAT_CSI_BAYER12RGGB
r""" 0x62000000"""
TY_PIXEL_FORMAT_CSI_BAYER12GBRG = _pcammls.TY_PIXEL_FORMAT_CSI_BAYER12GBRG
r""" 0x63000000"""
TY_PIXEL_FORMAT_CSI_BAYER12BGGR = _pcammls.TY_PIXEL_FORMAT_CSI_BAYER12BGGR
r""" 0x64000000"""
TY_PIXEL_FORMAT_DEPTH16 = _pcammls.TY_PIXEL_FORMAT_DEPTH16
r""" 0x20000000"""
TY_PIXEL_FORMAT_YVYU = _pcammls.TY_PIXEL_FORMAT_YVYU
r""" 0x21000000, yvyu422"""
TY_PIXEL_FORMAT_YUYV = _pcammls.TY_PIXEL_FORMAT_YUYV
r""" 0x22000000, yuyv422"""
TY_PIXEL_FORMAT_MONO16 = _pcammls.TY_PIXEL_FORMAT_MONO16
r""" 0x23000000,"""
TY_PIXEL_FORMAT_TOF_IR_MONO16 = _pcammls.TY_PIXEL_FORMAT_TOF_IR_MONO16
r""" 0xa4000000,"""
TY_PIXEL_FORMAT_RGB = _pcammls.TY_PIXEL_FORMAT_RGB
r""" 0x30000000"""
TY_PIXEL_FORMAT_BGR = _pcammls.TY_PIXEL_FORMAT_BGR
r""" 0x31000000"""
TY_PIXEL_FORMAT_JPEG = _pcammls.TY_PIXEL_FORMAT_JPEG
r""" 0x32000000"""
TY_PIXEL_FORMAT_MJPG = _pcammls.TY_PIXEL_FORMAT_MJPG
r""" 0x33000000"""
TY_PIXEL_FORMAT_RGB48 = _pcammls.TY_PIXEL_FORMAT_RGB48
r""" 0x80000000"""
TY_PIXEL_FORMAT_BGR48 = _pcammls.TY_PIXEL_FORMAT_BGR48
r""" 0x81000000"""
TY_PIXEL_FORMAT_XYZ48 = _pcammls.TY_PIXEL_FORMAT_XYZ48
r""" 0x82000000"""
TY_RESOLUTION_MODE_160x100 = _pcammls.TY_RESOLUTION_MODE_160x100
r""" 0x000a0078"""
TY_RESOLUTION_MODE_160x120 = _pcammls.TY_RESOLUTION_MODE_160x120
r""" 0x000a0078"""
TY_RESOLUTION_MODE_240x320 = _pcammls.TY_RESOLUTION_MODE_240x320
r""" 0x000f0140"""
TY_RESOLUTION_MODE_320x180 = _pcammls.TY_RESOLUTION_MODE_320x180
r""" 0x001400b4"""
TY_RESOLUTION_MODE_320x200 = _pcammls.TY_RESOLUTION_MODE_320x200
r""" 0x001400c8"""
TY_RESOLUTION_MODE_320x240 = _pcammls.TY_RESOLUTION_MODE_320x240
r""" 0x001400f0"""
TY_RESOLUTION_MODE_480x640 = _pcammls.TY_RESOLUTION_MODE_480x640
r""" 0x001e0280"""
TY_RESOLUTION_MODE_640x360 = _pcammls.TY_RESOLUTION_MODE_640x360
r""" 0x00280168"""
TY_RESOLUTION_MODE_640x400 = _pcammls.TY_RESOLUTION_MODE_640x400
r""" 0x00280190"""
TY_RESOLUTION_MODE_640x480 = _pcammls.TY_RESOLUTION_MODE_640x480
r""" 0x002801e0"""
TY_RESOLUTION_MODE_960x1280 = _pcammls.TY_RESOLUTION_MODE_960x1280
r""" 0x003c0500"""
TY_RESOLUTION_MODE_1280x720 = _pcammls.TY_RESOLUTION_MODE_1280x720
r""" 0x005002d0"""
TY_RESOLUTION_MODE_1280x800 = _pcammls.TY_RESOLUTION_MODE_1280x800
r""" 0x00500320"""
TY_RESOLUTION_MODE_1280x960 = _pcammls.TY_RESOLUTION_MODE_1280x960
r""" 0x005003c0"""
TY_RESOLUTION_MODE_1600x1200 = _pcammls.TY_RESOLUTION_MODE_1600x1200
r""" 0x006404b0"""
TY_RESOLUTION_MODE_800x600 = _pcammls.TY_RESOLUTION_MODE_800x600
r""" 0x00320258"""
TY_RESOLUTION_MODE_1920x1080 = _pcammls.TY_RESOLUTION_MODE_1920x1080
r""" 0x00780438"""
TY_RESOLUTION_MODE_2560x1920 = _pcammls.TY_RESOLUTION_MODE_2560x1920
r""" 0x00a00780"""
TY_RESOLUTION_MODE_2592x1944 = _pcammls.TY_RESOLUTION_MODE_2592x1944
r""" 0x00a20798"""
TY_RESOLUTION_MODE_1920x1440 = _pcammls.TY_RESOLUTION_MODE_1920x1440
r""" 0x007805a0"""
TY_RESOLUTION_MODE_240x96 = _pcammls.TY_RESOLUTION_MODE_240x96
r""" 0x000f0060"""
TY_RESOLUTION_MODE_2048x1536 = _pcammls.TY_RESOLUTION_MODE_2048x1536
r""" 0x00800600"""
TY_IMAGE_MODE_MONO_160x100 = _pcammls.TY_IMAGE_MODE_MONO_160x100

TY_IMAGE_MODE_MONO_160x120 = _pcammls.TY_IMAGE_MODE_MONO_160x120

TY_IMAGE_MODE_MONO_320x180 = _pcammls.TY_IMAGE_MODE_MONO_320x180

TY_IMAGE_MODE_MONO_320x200 = _pcammls.TY_IMAGE_MODE_MONO_320x200

TY_IMAGE_MODE_MONO_320x240 = _pcammls.TY_IMAGE_MODE_MONO_320x240

TY_IMAGE_MODE_MONO_480x640 = _pcammls.TY_IMAGE_MODE_MONO_480x640

TY_IMAGE_MODE_MONO_640x360 = _pcammls.TY_IMAGE_MODE_MONO_640x360

TY_IMAGE_MODE_MONO_640x400 = _pcammls.TY_IMAGE_MODE_MONO_640x400

TY_IMAGE_MODE_MONO_640x480 = _pcammls.TY_IMAGE_MODE_MONO_640x480

TY_IMAGE_MODE_MONO_960x1280 = _pcammls.TY_IMAGE_MODE_MONO_960x1280

TY_IMAGE_MODE_MONO_1280x720 = _pcammls.TY_IMAGE_MODE_MONO_1280x720

TY_IMAGE_MODE_MONO_1280x960 = _pcammls.TY_IMAGE_MODE_MONO_1280x960

TY_IMAGE_MODE_MONO_1280x800 = _pcammls.TY_IMAGE_MODE_MONO_1280x800

TY_IMAGE_MODE_MONO_1600x1200 = _pcammls.TY_IMAGE_MODE_MONO_1600x1200

TY_IMAGE_MODE_MONO_800x600 = _pcammls.TY_IMAGE_MODE_MONO_800x600

TY_IMAGE_MODE_MONO_1920x1080 = _pcammls.TY_IMAGE_MODE_MONO_1920x1080

TY_IMAGE_MODE_MONO_2560x1920 = _pcammls.TY_IMAGE_MODE_MONO_2560x1920

TY_IMAGE_MODE_MONO_2592x1944 = _pcammls.TY_IMAGE_MODE_MONO_2592x1944

TY_IMAGE_MODE_MONO_1920x1440 = _pcammls.TY_IMAGE_MODE_MONO_1920x1440

TY_IMAGE_MODE_MONO_2048x1536 = _pcammls.TY_IMAGE_MODE_MONO_2048x1536

TY_IMAGE_MODE_MONO_240x96 = _pcammls.TY_IMAGE_MODE_MONO_240x96

TY_IMAGE_MODE_MONO16_160x100 = _pcammls.TY_IMAGE_MODE_MONO16_160x100

TY_IMAGE_MODE_MONO16_160x120 = _pcammls.TY_IMAGE_MODE_MONO16_160x120

TY_IMAGE_MODE_MONO16_320x180 = _pcammls.TY_IMAGE_MODE_MONO16_320x180

TY_IMAGE_MODE_MONO16_320x200 = _pcammls.TY_IMAGE_MODE_MONO16_320x200

TY_IMAGE_MODE_MONO16_320x240 = _pcammls.TY_IMAGE_MODE_MONO16_320x240

TY_IMAGE_MODE_MONO16_480x640 = _pcammls.TY_IMAGE_MODE_MONO16_480x640

TY_IMAGE_MODE_MONO16_640x360 = _pcammls.TY_IMAGE_MODE_MONO16_640x360

TY_IMAGE_MODE_MONO16_640x400 = _pcammls.TY_IMAGE_MODE_MONO16_640x400

TY_IMAGE_MODE_MONO16_640x480 = _pcammls.TY_IMAGE_MODE_MONO16_640x480

TY_IMAGE_MODE_MONO16_960x1280 = _pcammls.TY_IMAGE_MODE_MONO16_960x1280

TY_IMAGE_MODE_MONO16_1280x720 = _pcammls.TY_IMAGE_MODE_MONO16_1280x720

TY_IMAGE_MODE_MONO16_1280x960 = _pcammls.TY_IMAGE_MODE_MONO16_1280x960

TY_IMAGE_MODE_MONO16_1280x800 = _pcammls.TY_IMAGE_MODE_MONO16_1280x800

TY_IMAGE_MODE_MONO16_1600x1200 = _pcammls.TY_IMAGE_MODE_MONO16_1600x1200

TY_IMAGE_MODE_MONO16_800x600 = _pcammls.TY_IMAGE_MODE_MONO16_800x600

TY_IMAGE_MODE_MONO16_1920x1080 = _pcammls.TY_IMAGE_MODE_MONO16_1920x1080

TY_IMAGE_MODE_MONO16_2560x1920 = _pcammls.TY_IMAGE_MODE_MONO16_2560x1920

TY_IMAGE_MODE_MONO16_2592x1944 = _pcammls.TY_IMAGE_MODE_MONO16_2592x1944

TY_IMAGE_MODE_MONO16_1920x1440 = _pcammls.TY_IMAGE_MODE_MONO16_1920x1440

TY_IMAGE_MODE_MONO16_2048x1536 = _pcammls.TY_IMAGE_MODE_MONO16_2048x1536

TY_IMAGE_MODE_MONO16_240x96 = _pcammls.TY_IMAGE_MODE_MONO16_240x96

TY_IMAGE_MODE_TOF_IR_MONO16_160x100 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_160x100

TY_IMAGE_MODE_TOF_IR_MONO16_160x120 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_160x120

TY_IMAGE_MODE_TOF_IR_MONO16_320x180 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_320x180

TY_IMAGE_MODE_TOF_IR_MONO16_320x200 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_320x200

TY_IMAGE_MODE_TOF_IR_MONO16_320x240 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_320x240

TY_IMAGE_MODE_TOF_IR_MONO16_480x640 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_480x640

TY_IMAGE_MODE_TOF_IR_MONO16_640x360 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_640x360

TY_IMAGE_MODE_TOF_IR_MONO16_640x400 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_640x400

TY_IMAGE_MODE_TOF_IR_MONO16_640x480 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_640x480

TY_IMAGE_MODE_TOF_IR_MONO16_960x1280 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_960x1280

TY_IMAGE_MODE_TOF_IR_MONO16_1280x720 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_1280x720

TY_IMAGE_MODE_TOF_IR_MONO16_1280x960 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_1280x960

TY_IMAGE_MODE_TOF_IR_MONO16_1280x800 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_1280x800

TY_IMAGE_MODE_TOF_IR_MONO16_1600x1200 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_1600x1200

TY_IMAGE_MODE_TOF_IR_MONO16_800x600 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_800x600

TY_IMAGE_MODE_TOF_IR_MONO16_1920x1080 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_1920x1080

TY_IMAGE_MODE_TOF_IR_MONO16_2560x1920 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_2560x1920

TY_IMAGE_MODE_TOF_IR_MONO16_2592x1944 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_2592x1944

TY_IMAGE_MODE_TOF_IR_MONO16_1920x1440 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_1920x1440

TY_IMAGE_MODE_TOF_IR_MONO16_2048x1536 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_2048x1536

TY_IMAGE_MODE_TOF_IR_MONO16_240x96 = _pcammls.TY_IMAGE_MODE_TOF_IR_MONO16_240x96

TY_IMAGE_MODE_DEPTH16_160x100 = _pcammls.TY_IMAGE_MODE_DEPTH16_160x100

TY_IMAGE_MODE_DEPTH16_160x120 = _pcammls.TY_IMAGE_MODE_DEPTH16_160x120

TY_IMAGE_MODE_DEPTH16_320x180 = _pcammls.TY_IMAGE_MODE_DEPTH16_320x180

TY_IMAGE_MODE_DEPTH16_320x200 = _pcammls.TY_IMAGE_MODE_DEPTH16_320x200

TY_IMAGE_MODE_DEPTH16_320x240 = _pcammls.TY_IMAGE_MODE_DEPTH16_320x240

TY_IMAGE_MODE_DEPTH16_480x640 = _pcammls.TY_IMAGE_MODE_DEPTH16_480x640

TY_IMAGE_MODE_DEPTH16_640x360 = _pcammls.TY_IMAGE_MODE_DEPTH16_640x360

TY_IMAGE_MODE_DEPTH16_640x400 = _pcammls.TY_IMAGE_MODE_DEPTH16_640x400

TY_IMAGE_MODE_DEPTH16_640x480 = _pcammls.TY_IMAGE_MODE_DEPTH16_640x480

TY_IMAGE_MODE_DEPTH16_960x1280 = _pcammls.TY_IMAGE_MODE_DEPTH16_960x1280

TY_IMAGE_MODE_DEPTH16_1280x720 = _pcammls.TY_IMAGE_MODE_DEPTH16_1280x720

TY_IMAGE_MODE_DEPTH16_1280x960 = _pcammls.TY_IMAGE_MODE_DEPTH16_1280x960

TY_IMAGE_MODE_DEPTH16_1280x800 = _pcammls.TY_IMAGE_MODE_DEPTH16_1280x800

TY_IMAGE_MODE_DEPTH16_1600x1200 = _pcammls.TY_IMAGE_MODE_DEPTH16_1600x1200

TY_IMAGE_MODE_DEPTH16_800x600 = _pcammls.TY_IMAGE_MODE_DEPTH16_800x600

TY_IMAGE_MODE_DEPTH16_1920x1080 = _pcammls.TY_IMAGE_MODE_DEPTH16_1920x1080

TY_IMAGE_MODE_DEPTH16_2560x1920 = _pcammls.TY_IMAGE_MODE_DEPTH16_2560x1920

TY_IMAGE_MODE_DEPTH16_2592x1944 = _pcammls.TY_IMAGE_MODE_DEPTH16_2592x1944

TY_IMAGE_MODE_DEPTH16_1920x1440 = _pcammls.TY_IMAGE_MODE_DEPTH16_1920x1440

TY_IMAGE_MODE_DEPTH16_2048x1536 = _pcammls.TY_IMAGE_MODE_DEPTH16_2048x1536

TY_IMAGE_MODE_DEPTH16_240x96 = _pcammls.TY_IMAGE_MODE_DEPTH16_240x96

TY_IMAGE_MODE_YVYU_160x100 = _pcammls.TY_IMAGE_MODE_YVYU_160x100

TY_IMAGE_MODE_YVYU_160x120 = _pcammls.TY_IMAGE_MODE_YVYU_160x120

TY_IMAGE_MODE_YVYU_320x180 = _pcammls.TY_IMAGE_MODE_YVYU_320x180

TY_IMAGE_MODE_YVYU_320x200 = _pcammls.TY_IMAGE_MODE_YVYU_320x200

TY_IMAGE_MODE_YVYU_320x240 = _pcammls.TY_IMAGE_MODE_YVYU_320x240

TY_IMAGE_MODE_YVYU_480x640 = _pcammls.TY_IMAGE_MODE_YVYU_480x640

TY_IMAGE_MODE_YVYU_640x360 = _pcammls.TY_IMAGE_MODE_YVYU_640x360

TY_IMAGE_MODE_YVYU_640x400 = _pcammls.TY_IMAGE_MODE_YVYU_640x400

TY_IMAGE_MODE_YVYU_640x480 = _pcammls.TY_IMAGE_MODE_YVYU_640x480

TY_IMAGE_MODE_YVYU_960x1280 = _pcammls.TY_IMAGE_MODE_YVYU_960x1280

TY_IMAGE_MODE_YVYU_1280x720 = _pcammls.TY_IMAGE_MODE_YVYU_1280x720

TY_IMAGE_MODE_YVYU_1280x960 = _pcammls.TY_IMAGE_MODE_YVYU_1280x960

TY_IMAGE_MODE_YVYU_1280x800 = _pcammls.TY_IMAGE_MODE_YVYU_1280x800

TY_IMAGE_MODE_YVYU_1600x1200 = _pcammls.TY_IMAGE_MODE_YVYU_1600x1200

TY_IMAGE_MODE_YVYU_800x600 = _pcammls.TY_IMAGE_MODE_YVYU_800x600

TY_IMAGE_MODE_YVYU_1920x1080 = _pcammls.TY_IMAGE_MODE_YVYU_1920x1080

TY_IMAGE_MODE_YVYU_2560x1920 = _pcammls.TY_IMAGE_MODE_YVYU_2560x1920

TY_IMAGE_MODE_YVYU_2592x1944 = _pcammls.TY_IMAGE_MODE_YVYU_2592x1944

TY_IMAGE_MODE_YVYU_1920x1440 = _pcammls.TY_IMAGE_MODE_YVYU_1920x1440

TY_IMAGE_MODE_YVYU_2048x1536 = _pcammls.TY_IMAGE_MODE_YVYU_2048x1536

TY_IMAGE_MODE_YVYU_240x96 = _pcammls.TY_IMAGE_MODE_YVYU_240x96

TY_IMAGE_MODE_YUYV_160x100 = _pcammls.TY_IMAGE_MODE_YUYV_160x100

TY_IMAGE_MODE_YUYV_160x120 = _pcammls.TY_IMAGE_MODE_YUYV_160x120

TY_IMAGE_MODE_YUYV_320x180 = _pcammls.TY_IMAGE_MODE_YUYV_320x180

TY_IMAGE_MODE_YUYV_320x200 = _pcammls.TY_IMAGE_MODE_YUYV_320x200

TY_IMAGE_MODE_YUYV_320x240 = _pcammls.TY_IMAGE_MODE_YUYV_320x240

TY_IMAGE_MODE_YUYV_480x640 = _pcammls.TY_IMAGE_MODE_YUYV_480x640

TY_IMAGE_MODE_YUYV_640x360 = _pcammls.TY_IMAGE_MODE_YUYV_640x360

TY_IMAGE_MODE_YUYV_640x400 = _pcammls.TY_IMAGE_MODE_YUYV_640x400

TY_IMAGE_MODE_YUYV_640x480 = _pcammls.TY_IMAGE_MODE_YUYV_640x480

TY_IMAGE_MODE_YUYV_960x1280 = _pcammls.TY_IMAGE_MODE_YUYV_960x1280

TY_IMAGE_MODE_YUYV_1280x720 = _pcammls.TY_IMAGE_MODE_YUYV_1280x720

TY_IMAGE_MODE_YUYV_1280x960 = _pcammls.TY_IMAGE_MODE_YUYV_1280x960

TY_IMAGE_MODE_YUYV_1280x800 = _pcammls.TY_IMAGE_MODE_YUYV_1280x800

TY_IMAGE_MODE_YUYV_1600x1200 = _pcammls.TY_IMAGE_MODE_YUYV_1600x1200

TY_IMAGE_MODE_YUYV_800x600 = _pcammls.TY_IMAGE_MODE_YUYV_800x600

TY_IMAGE_MODE_YUYV_1920x1080 = _pcammls.TY_IMAGE_MODE_YUYV_1920x1080

TY_IMAGE_MODE_YUYV_2560x1920 = _pcammls.TY_IMAGE_MODE_YUYV_2560x1920

TY_IMAGE_MODE_YUYV_2592x1944 = _pcammls.TY_IMAGE_MODE_YUYV_2592x1944

TY_IMAGE_MODE_YUYV_1920x1440 = _pcammls.TY_IMAGE_MODE_YUYV_1920x1440

TY_IMAGE_MODE_YUYV_2048x1536 = _pcammls.TY_IMAGE_MODE_YUYV_2048x1536

TY_IMAGE_MODE_YUYV_240x96 = _pcammls.TY_IMAGE_MODE_YUYV_240x96

TY_IMAGE_MODE_RGB_160x100 = _pcammls.TY_IMAGE_MODE_RGB_160x100

TY_IMAGE_MODE_RGB_160x120 = _pcammls.TY_IMAGE_MODE_RGB_160x120

TY_IMAGE_MODE_RGB_320x180 = _pcammls.TY_IMAGE_MODE_RGB_320x180

TY_IMAGE_MODE_RGB_320x200 = _pcammls.TY_IMAGE_MODE_RGB_320x200

TY_IMAGE_MODE_RGB_320x240 = _pcammls.TY_IMAGE_MODE_RGB_320x240

TY_IMAGE_MODE_RGB_480x640 = _pcammls.TY_IMAGE_MODE_RGB_480x640

TY_IMAGE_MODE_RGB_640x360 = _pcammls.TY_IMAGE_MODE_RGB_640x360

TY_IMAGE_MODE_RGB_640x400 = _pcammls.TY_IMAGE_MODE_RGB_640x400

TY_IMAGE_MODE_RGB_640x480 = _pcammls.TY_IMAGE_MODE_RGB_640x480

TY_IMAGE_MODE_RGB_960x1280 = _pcammls.TY_IMAGE_MODE_RGB_960x1280

TY_IMAGE_MODE_RGB_1280x720 = _pcammls.TY_IMAGE_MODE_RGB_1280x720

TY_IMAGE_MODE_RGB_1280x960 = _pcammls.TY_IMAGE_MODE_RGB_1280x960

TY_IMAGE_MODE_RGB_1280x800 = _pcammls.TY_IMAGE_MODE_RGB_1280x800

TY_IMAGE_MODE_RGB_1600x1200 = _pcammls.TY_IMAGE_MODE_RGB_1600x1200

TY_IMAGE_MODE_RGB_800x600 = _pcammls.TY_IMAGE_MODE_RGB_800x600

TY_IMAGE_MODE_RGB_1920x1080 = _pcammls.TY_IMAGE_MODE_RGB_1920x1080

TY_IMAGE_MODE_RGB_2560x1920 = _pcammls.TY_IMAGE_MODE_RGB_2560x1920

TY_IMAGE_MODE_RGB_2592x1944 = _pcammls.TY_IMAGE_MODE_RGB_2592x1944

TY_IMAGE_MODE_RGB_1920x1440 = _pcammls.TY_IMAGE_MODE_RGB_1920x1440

TY_IMAGE_MODE_RGB_2048x1536 = _pcammls.TY_IMAGE_MODE_RGB_2048x1536

TY_IMAGE_MODE_RGB_240x96 = _pcammls.TY_IMAGE_MODE_RGB_240x96

TY_IMAGE_MODE_JPEG_160x100 = _pcammls.TY_IMAGE_MODE_JPEG_160x100

TY_IMAGE_MODE_JPEG_160x120 = _pcammls.TY_IMAGE_MODE_JPEG_160x120

TY_IMAGE_MODE_JPEG_320x180 = _pcammls.TY_IMAGE_MODE_JPEG_320x180

TY_IMAGE_MODE_JPEG_320x200 = _pcammls.TY_IMAGE_MODE_JPEG_320x200

TY_IMAGE_MODE_JPEG_320x240 = _pcammls.TY_IMAGE_MODE_JPEG_320x240

TY_IMAGE_MODE_JPEG_480x640 = _pcammls.TY_IMAGE_MODE_JPEG_480x640

TY_IMAGE_MODE_JPEG_640x360 = _pcammls.TY_IMAGE_MODE_JPEG_640x360

TY_IMAGE_MODE_JPEG_640x400 = _pcammls.TY_IMAGE_MODE_JPEG_640x400

TY_IMAGE_MODE_JPEG_640x480 = _pcammls.TY_IMAGE_MODE_JPEG_640x480

TY_IMAGE_MODE_JPEG_960x1280 = _pcammls.TY_IMAGE_MODE_JPEG_960x1280

TY_IMAGE_MODE_JPEG_1280x720 = _pcammls.TY_IMAGE_MODE_JPEG_1280x720

TY_IMAGE_MODE_JPEG_1280x960 = _pcammls.TY_IMAGE_MODE_JPEG_1280x960

TY_IMAGE_MODE_JPEG_1280x800 = _pcammls.TY_IMAGE_MODE_JPEG_1280x800

TY_IMAGE_MODE_JPEG_1600x1200 = _pcammls.TY_IMAGE_MODE_JPEG_1600x1200

TY_IMAGE_MODE_JPEG_800x600 = _pcammls.TY_IMAGE_MODE_JPEG_800x600

TY_IMAGE_MODE_JPEG_1920x1080 = _pcammls.TY_IMAGE_MODE_JPEG_1920x1080

TY_IMAGE_MODE_JPEG_2560x1920 = _pcammls.TY_IMAGE_MODE_JPEG_2560x1920

TY_IMAGE_MODE_JPEG_2592x1944 = _pcammls.TY_IMAGE_MODE_JPEG_2592x1944

TY_IMAGE_MODE_JPEG_1920x1440 = _pcammls.TY_IMAGE_MODE_JPEG_1920x1440

TY_IMAGE_MODE_JPEG_2048x1536 = _pcammls.TY_IMAGE_MODE_JPEG_2048x1536

TY_IMAGE_MODE_JPEG_240x96 = _pcammls.TY_IMAGE_MODE_JPEG_240x96

TY_IMAGE_MODE_BAYER8GB_160x100 = _pcammls.TY_IMAGE_MODE_BAYER8GB_160x100

TY_IMAGE_MODE_BAYER8GB_160x120 = _pcammls.TY_IMAGE_MODE_BAYER8GB_160x120

TY_IMAGE_MODE_BAYER8GB_320x180 = _pcammls.TY_IMAGE_MODE_BAYER8GB_320x180

TY_IMAGE_MODE_BAYER8GB_320x200 = _pcammls.TY_IMAGE_MODE_BAYER8GB_320x200

TY_IMAGE_MODE_BAYER8GB_320x240 = _pcammls.TY_IMAGE_MODE_BAYER8GB_320x240

TY_IMAGE_MODE_BAYER8GB_480x640 = _pcammls.TY_IMAGE_MODE_BAYER8GB_480x640

TY_IMAGE_MODE_BAYER8GB_640x360 = _pcammls.TY_IMAGE_MODE_BAYER8GB_640x360

TY_IMAGE_MODE_BAYER8GB_640x400 = _pcammls.TY_IMAGE_MODE_BAYER8GB_640x400

TY_IMAGE_MODE_BAYER8GB_640x480 = _pcammls.TY_IMAGE_MODE_BAYER8GB_640x480

TY_IMAGE_MODE_BAYER8GB_960x1280 = _pcammls.TY_IMAGE_MODE_BAYER8GB_960x1280

TY_IMAGE_MODE_BAYER8GB_1280x720 = _pcammls.TY_IMAGE_MODE_BAYER8GB_1280x720

TY_IMAGE_MODE_BAYER8GB_1280x960 = _pcammls.TY_IMAGE_MODE_BAYER8GB_1280x960

TY_IMAGE_MODE_BAYER8GB_1280x800 = _pcammls.TY_IMAGE_MODE_BAYER8GB_1280x800

TY_IMAGE_MODE_BAYER8GB_1600x1200 = _pcammls.TY_IMAGE_MODE_BAYER8GB_1600x1200

TY_IMAGE_MODE_BAYER8GB_800x600 = _pcammls.TY_IMAGE_MODE_BAYER8GB_800x600

TY_IMAGE_MODE_BAYER8GB_1920x1080 = _pcammls.TY_IMAGE_MODE_BAYER8GB_1920x1080

TY_IMAGE_MODE_BAYER8GB_2560x1920 = _pcammls.TY_IMAGE_MODE_BAYER8GB_2560x1920

TY_IMAGE_MODE_BAYER8GB_2592x1944 = _pcammls.TY_IMAGE_MODE_BAYER8GB_2592x1944

TY_IMAGE_MODE_BAYER8GB_1920x1440 = _pcammls.TY_IMAGE_MODE_BAYER8GB_1920x1440

TY_IMAGE_MODE_BAYER8GB_2048x1536 = _pcammls.TY_IMAGE_MODE_BAYER8GB_2048x1536

TY_IMAGE_MODE_BAYER8GB_240x96 = _pcammls.TY_IMAGE_MODE_BAYER8GB_240x96

TY_IMAGE_MODE_BAYER8BG_160x100 = _pcammls.TY_IMAGE_MODE_BAYER8BG_160x100

TY_IMAGE_MODE_BAYER8BG_160x120 = _pcammls.TY_IMAGE_MODE_BAYER8BG_160x120

TY_IMAGE_MODE_BAYER8BG_320x180 = _pcammls.TY_IMAGE_MODE_BAYER8BG_320x180

TY_IMAGE_MODE_BAYER8BG_320x200 = _pcammls.TY_IMAGE_MODE_BAYER8BG_320x200

TY_IMAGE_MODE_BAYER8BG_320x240 = _pcammls.TY_IMAGE_MODE_BAYER8BG_320x240

TY_IMAGE_MODE_BAYER8BG_480x640 = _pcammls.TY_IMAGE_MODE_BAYER8BG_480x640

TY_IMAGE_MODE_BAYER8BG_640x360 = _pcammls.TY_IMAGE_MODE_BAYER8BG_640x360

TY_IMAGE_MODE_BAYER8BG_640x400 = _pcammls.TY_IMAGE_MODE_BAYER8BG_640x400

TY_IMAGE_MODE_BAYER8BG_640x480 = _pcammls.TY_IMAGE_MODE_BAYER8BG_640x480

TY_IMAGE_MODE_BAYER8BG_960x1280 = _pcammls.TY_IMAGE_MODE_BAYER8BG_960x1280

TY_IMAGE_MODE_BAYER8BG_1280x720 = _pcammls.TY_IMAGE_MODE_BAYER8BG_1280x720

TY_IMAGE_MODE_BAYER8BG_1280x960 = _pcammls.TY_IMAGE_MODE_BAYER8BG_1280x960

TY_IMAGE_MODE_BAYER8BG_1280x800 = _pcammls.TY_IMAGE_MODE_BAYER8BG_1280x800

TY_IMAGE_MODE_BAYER8BG_1600x1200 = _pcammls.TY_IMAGE_MODE_BAYER8BG_1600x1200

TY_IMAGE_MODE_BAYER8BG_800x600 = _pcammls.TY_IMAGE_MODE_BAYER8BG_800x600

TY_IMAGE_MODE_BAYER8BG_1920x1080 = _pcammls.TY_IMAGE_MODE_BAYER8BG_1920x1080

TY_IMAGE_MODE_BAYER8BG_2560x1920 = _pcammls.TY_IMAGE_MODE_BAYER8BG_2560x1920

TY_IMAGE_MODE_BAYER8BG_2592x1944 = _pcammls.TY_IMAGE_MODE_BAYER8BG_2592x1944

TY_IMAGE_MODE_BAYER8BG_1920x1440 = _pcammls.TY_IMAGE_MODE_BAYER8BG_1920x1440

TY_IMAGE_MODE_BAYER8BG_2048x1536 = _pcammls.TY_IMAGE_MODE_BAYER8BG_2048x1536

TY_IMAGE_MODE_BAYER8BG_240x96 = _pcammls.TY_IMAGE_MODE_BAYER8BG_240x96

TY_IMAGE_MODE_BAYER8GR_160x100 = _pcammls.TY_IMAGE_MODE_BAYER8GR_160x100

TY_IMAGE_MODE_BAYER8GR_160x120 = _pcammls.TY_IMAGE_MODE_BAYER8GR_160x120

TY_IMAGE_MODE_BAYER8GR_320x180 = _pcammls.TY_IMAGE_MODE_BAYER8GR_320x180

TY_IMAGE_MODE_BAYER8GR_320x200 = _pcammls.TY_IMAGE_MODE_BAYER8GR_320x200

TY_IMAGE_MODE_BAYER8GR_320x240 = _pcammls.TY_IMAGE_MODE_BAYER8GR_320x240

TY_IMAGE_MODE_BAYER8GR_480x640 = _pcammls.TY_IMAGE_MODE_BAYER8GR_480x640

TY_IMAGE_MODE_BAYER8GR_640x360 = _pcammls.TY_IMAGE_MODE_BAYER8GR_640x360

TY_IMAGE_MODE_BAYER8GR_640x400 = _pcammls.TY_IMAGE_MODE_BAYER8GR_640x400

TY_IMAGE_MODE_BAYER8GR_640x480 = _pcammls.TY_IMAGE_MODE_BAYER8GR_640x480

TY_IMAGE_MODE_BAYER8GR_960x1280 = _pcammls.TY_IMAGE_MODE_BAYER8GR_960x1280

TY_IMAGE_MODE_BAYER8GR_1280x720 = _pcammls.TY_IMAGE_MODE_BAYER8GR_1280x720

TY_IMAGE_MODE_BAYER8GR_1280x960 = _pcammls.TY_IMAGE_MODE_BAYER8GR_1280x960

TY_IMAGE_MODE_BAYER8GR_1280x800 = _pcammls.TY_IMAGE_MODE_BAYER8GR_1280x800

TY_IMAGE_MODE_BAYER8GR_1600x1200 = _pcammls.TY_IMAGE_MODE_BAYER8GR_1600x1200

TY_IMAGE_MODE_BAYER8GR_800x600 = _pcammls.TY_IMAGE_MODE_BAYER8GR_800x600

TY_IMAGE_MODE_BAYER8GR_1920x1080 = _pcammls.TY_IMAGE_MODE_BAYER8GR_1920x1080

TY_IMAGE_MODE_BAYER8GR_2560x1920 = _pcammls.TY_IMAGE_MODE_BAYER8GR_2560x1920

TY_IMAGE_MODE_BAYER8GR_2592x1944 = _pcammls.TY_IMAGE_MODE_BAYER8GR_2592x1944

TY_IMAGE_MODE_BAYER8GR_1920x1440 = _pcammls.TY_IMAGE_MODE_BAYER8GR_1920x1440

TY_IMAGE_MODE_BAYER8GR_2048x1536 = _pcammls.TY_IMAGE_MODE_BAYER8GR_2048x1536

TY_IMAGE_MODE_BAYER8GR_240x96 = _pcammls.TY_IMAGE_MODE_BAYER8GR_240x96

TY_IMAGE_MODE_BAYER8RG_160x100 = _pcammls.TY_IMAGE_MODE_BAYER8RG_160x100

TY_IMAGE_MODE_BAYER8RG_160x120 = _pcammls.TY_IMAGE_MODE_BAYER8RG_160x120

TY_IMAGE_MODE_BAYER8RG_320x180 = _pcammls.TY_IMAGE_MODE_BAYER8RG_320x180

TY_IMAGE_MODE_BAYER8RG_320x200 = _pcammls.TY_IMAGE_MODE_BAYER8RG_320x200

TY_IMAGE_MODE_BAYER8RG_320x240 = _pcammls.TY_IMAGE_MODE_BAYER8RG_320x240

TY_IMAGE_MODE_BAYER8RG_480x640 = _pcammls.TY_IMAGE_MODE_BAYER8RG_480x640

TY_IMAGE_MODE_BAYER8RG_640x360 = _pcammls.TY_IMAGE_MODE_BAYER8RG_640x360

TY_IMAGE_MODE_BAYER8RG_640x400 = _pcammls.TY_IMAGE_MODE_BAYER8RG_640x400

TY_IMAGE_MODE_BAYER8RG_640x480 = _pcammls.TY_IMAGE_MODE_BAYER8RG_640x480

TY_IMAGE_MODE_BAYER8RG_960x1280 = _pcammls.TY_IMAGE_MODE_BAYER8RG_960x1280

TY_IMAGE_MODE_BAYER8RG_1280x720 = _pcammls.TY_IMAGE_MODE_BAYER8RG_1280x720

TY_IMAGE_MODE_BAYER8RG_1280x960 = _pcammls.TY_IMAGE_MODE_BAYER8RG_1280x960

TY_IMAGE_MODE_BAYER8RG_1280x800 = _pcammls.TY_IMAGE_MODE_BAYER8RG_1280x800

TY_IMAGE_MODE_BAYER8RG_1600x1200 = _pcammls.TY_IMAGE_MODE_BAYER8RG_1600x1200

TY_IMAGE_MODE_BAYER8RG_800x600 = _pcammls.TY_IMAGE_MODE_BAYER8RG_800x600

TY_IMAGE_MODE_BAYER8RG_1920x1080 = _pcammls.TY_IMAGE_MODE_BAYER8RG_1920x1080

TY_IMAGE_MODE_BAYER8RG_2560x1920 = _pcammls.TY_IMAGE_MODE_BAYER8RG_2560x1920

TY_IMAGE_MODE_BAYER8RG_2592x1944 = _pcammls.TY_IMAGE_MODE_BAYER8RG_2592x1944

TY_IMAGE_MODE_BAYER8RG_1920x1440 = _pcammls.TY_IMAGE_MODE_BAYER8RG_1920x1440

TY_IMAGE_MODE_BAYER8RG_2048x1536 = _pcammls.TY_IMAGE_MODE_BAYER8RG_2048x1536

TY_IMAGE_MODE_BAYER8RG_240x96 = _pcammls.TY_IMAGE_MODE_BAYER8RG_240x96

TY_IMAGE_MODE_BAYER8GRBG_160x100 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_160x100

TY_IMAGE_MODE_BAYER8GRBG_160x120 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_160x120

TY_IMAGE_MODE_BAYER8GRBG_320x180 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_320x180

TY_IMAGE_MODE_BAYER8GRBG_320x200 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_320x200

TY_IMAGE_MODE_BAYER8GRBG_320x240 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_320x240

TY_IMAGE_MODE_BAYER8GRBG_480x640 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_480x640

TY_IMAGE_MODE_BAYER8GRBG_640x360 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_640x360

TY_IMAGE_MODE_BAYER8GRBG_640x400 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_640x400

TY_IMAGE_MODE_BAYER8GRBG_640x480 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_640x480

TY_IMAGE_MODE_BAYER8GRBG_960x1280 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_960x1280

TY_IMAGE_MODE_BAYER8GRBG_1280x720 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_1280x720

TY_IMAGE_MODE_BAYER8GRBG_1280x960 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_1280x960

TY_IMAGE_MODE_BAYER8GRBG_1280x800 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_1280x800

TY_IMAGE_MODE_BAYER8GRBG_1600x1200 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_1600x1200

TY_IMAGE_MODE_BAYER8GRBG_800x600 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_800x600

TY_IMAGE_MODE_BAYER8GRBG_1920x1080 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_1920x1080

TY_IMAGE_MODE_BAYER8GRBG_2560x1920 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_2560x1920

TY_IMAGE_MODE_BAYER8GRBG_2592x1944 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_2592x1944

TY_IMAGE_MODE_BAYER8GRBG_1920x1440 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_1920x1440

TY_IMAGE_MODE_BAYER8GRBG_2048x1536 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_2048x1536

TY_IMAGE_MODE_BAYER8GRBG_240x96 = _pcammls.TY_IMAGE_MODE_BAYER8GRBG_240x96

TY_IMAGE_MODE_BAYER8RGGB_160x100 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_160x100

TY_IMAGE_MODE_BAYER8RGGB_160x120 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_160x120

TY_IMAGE_MODE_BAYER8RGGB_320x180 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_320x180

TY_IMAGE_MODE_BAYER8RGGB_320x200 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_320x200

TY_IMAGE_MODE_BAYER8RGGB_320x240 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_320x240

TY_IMAGE_MODE_BAYER8RGGB_480x640 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_480x640

TY_IMAGE_MODE_BAYER8RGGB_640x360 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_640x360

TY_IMAGE_MODE_BAYER8RGGB_640x400 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_640x400

TY_IMAGE_MODE_BAYER8RGGB_640x480 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_640x480

TY_IMAGE_MODE_BAYER8RGGB_960x1280 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_960x1280

TY_IMAGE_MODE_BAYER8RGGB_1280x720 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_1280x720

TY_IMAGE_MODE_BAYER8RGGB_1280x960 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_1280x960

TY_IMAGE_MODE_BAYER8RGGB_1280x800 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_1280x800

TY_IMAGE_MODE_BAYER8RGGB_1600x1200 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_1600x1200

TY_IMAGE_MODE_BAYER8RGGB_800x600 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_800x600

TY_IMAGE_MODE_BAYER8RGGB_1920x1080 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_1920x1080

TY_IMAGE_MODE_BAYER8RGGB_2560x1920 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_2560x1920

TY_IMAGE_MODE_BAYER8RGGB_2592x1944 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_2592x1944

TY_IMAGE_MODE_BAYER8RGGB_1920x1440 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_1920x1440

TY_IMAGE_MODE_BAYER8RGGB_2048x1536 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_2048x1536

TY_IMAGE_MODE_BAYER8RGGB_240x96 = _pcammls.TY_IMAGE_MODE_BAYER8RGGB_240x96

TY_IMAGE_MODE_BAYER8GBRG_160x100 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_160x100

TY_IMAGE_MODE_BAYER8GBRG_160x120 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_160x120

TY_IMAGE_MODE_BAYER8GBRG_320x180 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_320x180

TY_IMAGE_MODE_BAYER8GBRG_320x200 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_320x200

TY_IMAGE_MODE_BAYER8GBRG_320x240 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_320x240

TY_IMAGE_MODE_BAYER8GBRG_480x640 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_480x640

TY_IMAGE_MODE_BAYER8GBRG_640x360 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_640x360

TY_IMAGE_MODE_BAYER8GBRG_640x400 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_640x400

TY_IMAGE_MODE_BAYER8GBRG_640x480 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_640x480

TY_IMAGE_MODE_BAYER8GBRG_960x1280 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_960x1280

TY_IMAGE_MODE_BAYER8GBRG_1280x720 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_1280x720

TY_IMAGE_MODE_BAYER8GBRG_1280x960 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_1280x960

TY_IMAGE_MODE_BAYER8GBRG_1280x800 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_1280x800

TY_IMAGE_MODE_BAYER8GBRG_1600x1200 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_1600x1200

TY_IMAGE_MODE_BAYER8GBRG_800x600 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_800x600

TY_IMAGE_MODE_BAYER8GBRG_1920x1080 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_1920x1080

TY_IMAGE_MODE_BAYER8GBRG_2560x1920 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_2560x1920

TY_IMAGE_MODE_BAYER8GBRG_2592x1944 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_2592x1944

TY_IMAGE_MODE_BAYER8GBRG_1920x1440 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_1920x1440

TY_IMAGE_MODE_BAYER8GBRG_2048x1536 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_2048x1536

TY_IMAGE_MODE_BAYER8GBRG_240x96 = _pcammls.TY_IMAGE_MODE_BAYER8GBRG_240x96

TY_IMAGE_MODE_BAYER8BGGR_160x100 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_160x100

TY_IMAGE_MODE_BAYER8BGGR_160x120 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_160x120

TY_IMAGE_MODE_BAYER8BGGR_320x180 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_320x180

TY_IMAGE_MODE_BAYER8BGGR_320x200 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_320x200

TY_IMAGE_MODE_BAYER8BGGR_320x240 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_320x240

TY_IMAGE_MODE_BAYER8BGGR_480x640 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_480x640

TY_IMAGE_MODE_BAYER8BGGR_640x360 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_640x360

TY_IMAGE_MODE_BAYER8BGGR_640x400 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_640x400

TY_IMAGE_MODE_BAYER8BGGR_640x480 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_640x480

TY_IMAGE_MODE_BAYER8BGGR_960x1280 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_960x1280

TY_IMAGE_MODE_BAYER8BGGR_1280x720 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_1280x720

TY_IMAGE_MODE_BAYER8BGGR_1280x960 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_1280x960

TY_IMAGE_MODE_BAYER8BGGR_1280x800 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_1280x800

TY_IMAGE_MODE_BAYER8BGGR_1600x1200 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_1600x1200

TY_IMAGE_MODE_BAYER8BGGR_800x600 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_800x600

TY_IMAGE_MODE_BAYER8BGGR_1920x1080 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_1920x1080

TY_IMAGE_MODE_BAYER8BGGR_2560x1920 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_2560x1920

TY_IMAGE_MODE_BAYER8BGGR_2592x1944 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_2592x1944

TY_IMAGE_MODE_BAYER8BGGR_1920x1440 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_1920x1440

TY_IMAGE_MODE_BAYER8BGGR_2048x1536 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_2048x1536

TY_IMAGE_MODE_BAYER8BGGR_240x96 = _pcammls.TY_IMAGE_MODE_BAYER8BGGR_240x96

TY_IMAGE_MODE_CSI_MONO10_160x100 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_160x100

TY_IMAGE_MODE_CSI_MONO10_160x120 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_160x120

TY_IMAGE_MODE_CSI_MONO10_320x180 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_320x180

TY_IMAGE_MODE_CSI_MONO10_320x200 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_320x200

TY_IMAGE_MODE_CSI_MONO10_320x240 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_320x240

TY_IMAGE_MODE_CSI_MONO10_480x640 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_480x640

TY_IMAGE_MODE_CSI_MONO10_640x360 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_640x360

TY_IMAGE_MODE_CSI_MONO10_640x400 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_640x400

TY_IMAGE_MODE_CSI_MONO10_640x480 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_640x480

TY_IMAGE_MODE_CSI_MONO10_960x1280 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_960x1280

TY_IMAGE_MODE_CSI_MONO10_1280x720 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_1280x720

TY_IMAGE_MODE_CSI_MONO10_1280x960 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_1280x960

TY_IMAGE_MODE_CSI_MONO10_1280x800 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_1280x800

TY_IMAGE_MODE_CSI_MONO10_1600x1200 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_1600x1200

TY_IMAGE_MODE_CSI_MONO10_800x600 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_800x600

TY_IMAGE_MODE_CSI_MONO10_1920x1080 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_1920x1080

TY_IMAGE_MODE_CSI_MONO10_2560x1920 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_2560x1920

TY_IMAGE_MODE_CSI_MONO10_2592x1944 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_2592x1944

TY_IMAGE_MODE_CSI_MONO10_1920x1440 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_1920x1440

TY_IMAGE_MODE_CSI_MONO10_2048x1536 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_2048x1536

TY_IMAGE_MODE_CSI_MONO10_240x96 = _pcammls.TY_IMAGE_MODE_CSI_MONO10_240x96

TY_IMAGE_MODE_CSI_BAYER10GRBG_160x100 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_160x100

TY_IMAGE_MODE_CSI_BAYER10GRBG_160x120 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_160x120

TY_IMAGE_MODE_CSI_BAYER10GRBG_320x180 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_320x180

TY_IMAGE_MODE_CSI_BAYER10GRBG_320x200 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_320x200

TY_IMAGE_MODE_CSI_BAYER10GRBG_320x240 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_320x240

TY_IMAGE_MODE_CSI_BAYER10GRBG_480x640 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_480x640

TY_IMAGE_MODE_CSI_BAYER10GRBG_640x360 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_640x360

TY_IMAGE_MODE_CSI_BAYER10GRBG_640x400 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_640x400

TY_IMAGE_MODE_CSI_BAYER10GRBG_640x480 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_640x480

TY_IMAGE_MODE_CSI_BAYER10GRBG_960x1280 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_960x1280

TY_IMAGE_MODE_CSI_BAYER10GRBG_1280x720 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_1280x720

TY_IMAGE_MODE_CSI_BAYER10GRBG_1280x960 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_1280x960

TY_IMAGE_MODE_CSI_BAYER10GRBG_1280x800 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_1280x800

TY_IMAGE_MODE_CSI_BAYER10GRBG_1600x1200 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_1600x1200
)

TY_IMAGE_MODE_CSI_BAYER10GRBG_800x600 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_800x600

TY_IMAGE_MODE_CSI_BAYER10GRBG_1920x1080 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_1920x1080
)

TY_IMAGE_MODE_CSI_BAYER10GRBG_2560x1920 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_2560x1920
)

TY_IMAGE_MODE_CSI_BAYER10GRBG_2592x1944 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_2592x1944
)

TY_IMAGE_MODE_CSI_BAYER10GRBG_1920x1440 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_1920x1440
)

TY_IMAGE_MODE_CSI_BAYER10GRBG_2048x1536 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_2048x1536
)

TY_IMAGE_MODE_CSI_BAYER10GRBG_240x96 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GRBG_240x96

TY_IMAGE_MODE_CSI_BAYER10RGGB_160x100 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_160x100

TY_IMAGE_MODE_CSI_BAYER10RGGB_160x120 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_160x120

TY_IMAGE_MODE_CSI_BAYER10RGGB_320x180 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_320x180

TY_IMAGE_MODE_CSI_BAYER10RGGB_320x200 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_320x200

TY_IMAGE_MODE_CSI_BAYER10RGGB_320x240 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_320x240

TY_IMAGE_MODE_CSI_BAYER10RGGB_480x640 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_480x640

TY_IMAGE_MODE_CSI_BAYER10RGGB_640x360 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_640x360

TY_IMAGE_MODE_CSI_BAYER10RGGB_640x400 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_640x400

TY_IMAGE_MODE_CSI_BAYER10RGGB_640x480 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_640x480

TY_IMAGE_MODE_CSI_BAYER10RGGB_960x1280 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_960x1280

TY_IMAGE_MODE_CSI_BAYER10RGGB_1280x720 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_1280x720

TY_IMAGE_MODE_CSI_BAYER10RGGB_1280x960 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_1280x960

TY_IMAGE_MODE_CSI_BAYER10RGGB_1280x800 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_1280x800

TY_IMAGE_MODE_CSI_BAYER10RGGB_1600x1200 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_1600x1200
)

TY_IMAGE_MODE_CSI_BAYER10RGGB_800x600 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_800x600

TY_IMAGE_MODE_CSI_BAYER10RGGB_1920x1080 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_1920x1080
)

TY_IMAGE_MODE_CSI_BAYER10RGGB_2560x1920 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_2560x1920
)

TY_IMAGE_MODE_CSI_BAYER10RGGB_2592x1944 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_2592x1944
)

TY_IMAGE_MODE_CSI_BAYER10RGGB_1920x1440 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_1920x1440
)

TY_IMAGE_MODE_CSI_BAYER10RGGB_2048x1536 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_2048x1536
)

TY_IMAGE_MODE_CSI_BAYER10RGGB_240x96 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10RGGB_240x96

TY_IMAGE_MODE_CSI_BAYER10GBRG_160x100 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_160x100

TY_IMAGE_MODE_CSI_BAYER10GBRG_160x120 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_160x120

TY_IMAGE_MODE_CSI_BAYER10GBRG_320x180 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_320x180

TY_IMAGE_MODE_CSI_BAYER10GBRG_320x200 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_320x200

TY_IMAGE_MODE_CSI_BAYER10GBRG_320x240 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_320x240

TY_IMAGE_MODE_CSI_BAYER10GBRG_480x640 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_480x640

TY_IMAGE_MODE_CSI_BAYER10GBRG_640x360 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_640x360

TY_IMAGE_MODE_CSI_BAYER10GBRG_640x400 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_640x400

TY_IMAGE_MODE_CSI_BAYER10GBRG_640x480 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_640x480

TY_IMAGE_MODE_CSI_BAYER10GBRG_960x1280 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_960x1280

TY_IMAGE_MODE_CSI_BAYER10GBRG_1280x720 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_1280x720

TY_IMAGE_MODE_CSI_BAYER10GBRG_1280x960 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_1280x960

TY_IMAGE_MODE_CSI_BAYER10GBRG_1280x800 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_1280x800

TY_IMAGE_MODE_CSI_BAYER10GBRG_1600x1200 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_1600x1200
)

TY_IMAGE_MODE_CSI_BAYER10GBRG_800x600 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_800x600

TY_IMAGE_MODE_CSI_BAYER10GBRG_1920x1080 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_1920x1080
)

TY_IMAGE_MODE_CSI_BAYER10GBRG_2560x1920 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_2560x1920
)

TY_IMAGE_MODE_CSI_BAYER10GBRG_2592x1944 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_2592x1944
)

TY_IMAGE_MODE_CSI_BAYER10GBRG_1920x1440 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_1920x1440
)

TY_IMAGE_MODE_CSI_BAYER10GBRG_2048x1536 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_2048x1536
)

TY_IMAGE_MODE_CSI_BAYER10GBRG_240x96 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10GBRG_240x96

TY_IMAGE_MODE_CSI_BAYER10BGGR_160x100 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_160x100

TY_IMAGE_MODE_CSI_BAYER10BGGR_160x120 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_160x120

TY_IMAGE_MODE_CSI_BAYER10BGGR_320x180 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_320x180

TY_IMAGE_MODE_CSI_BAYER10BGGR_320x200 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_320x200

TY_IMAGE_MODE_CSI_BAYER10BGGR_320x240 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_320x240

TY_IMAGE_MODE_CSI_BAYER10BGGR_480x640 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_480x640

TY_IMAGE_MODE_CSI_BAYER10BGGR_640x360 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_640x360

TY_IMAGE_MODE_CSI_BAYER10BGGR_640x400 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_640x400

TY_IMAGE_MODE_CSI_BAYER10BGGR_640x480 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_640x480

TY_IMAGE_MODE_CSI_BAYER10BGGR_960x1280 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_960x1280

TY_IMAGE_MODE_CSI_BAYER10BGGR_1280x720 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_1280x720

TY_IMAGE_MODE_CSI_BAYER10BGGR_1280x960 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_1280x960

TY_IMAGE_MODE_CSI_BAYER10BGGR_1280x800 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_1280x800

TY_IMAGE_MODE_CSI_BAYER10BGGR_1600x1200 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_1600x1200
)

TY_IMAGE_MODE_CSI_BAYER10BGGR_800x600 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_800x600

TY_IMAGE_MODE_CSI_BAYER10BGGR_1920x1080 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_1920x1080
)

TY_IMAGE_MODE_CSI_BAYER10BGGR_2560x1920 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_2560x1920
)

TY_IMAGE_MODE_CSI_BAYER10BGGR_2592x1944 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_2592x1944
)

TY_IMAGE_MODE_CSI_BAYER10BGGR_1920x1440 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_1920x1440
)

TY_IMAGE_MODE_CSI_BAYER10BGGR_2048x1536 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_2048x1536
)

TY_IMAGE_MODE_CSI_BAYER10BGGR_240x96 = _pcammls.TY_IMAGE_MODE_CSI_BAYER10BGGR_240x96

TY_IMAGE_MODE_CSI_MONO12_160x100 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_160x100

TY_IMAGE_MODE_CSI_MONO12_160x120 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_160x120

TY_IMAGE_MODE_CSI_MONO12_320x180 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_320x180

TY_IMAGE_MODE_CSI_MONO12_320x200 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_320x200

TY_IMAGE_MODE_CSI_MONO12_320x240 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_320x240

TY_IMAGE_MODE_CSI_MONO12_480x640 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_480x640

TY_IMAGE_MODE_CSI_MONO12_640x360 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_640x360

TY_IMAGE_MODE_CSI_MONO12_640x400 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_640x400

TY_IMAGE_MODE_CSI_MONO12_640x480 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_640x480

TY_IMAGE_MODE_CSI_MONO12_960x1280 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_960x1280

TY_IMAGE_MODE_CSI_MONO12_1280x720 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_1280x720

TY_IMAGE_MODE_CSI_MONO12_1280x960 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_1280x960

TY_IMAGE_MODE_CSI_MONO12_1280x800 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_1280x800

TY_IMAGE_MODE_CSI_MONO12_1600x1200 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_1600x1200

TY_IMAGE_MODE_CSI_MONO12_800x600 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_800x600

TY_IMAGE_MODE_CSI_MONO12_1920x1080 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_1920x1080

TY_IMAGE_MODE_CSI_MONO12_2560x1920 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_2560x1920

TY_IMAGE_MODE_CSI_MONO12_2592x1944 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_2592x1944

TY_IMAGE_MODE_CSI_MONO12_1920x1440 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_1920x1440

TY_IMAGE_MODE_CSI_MONO12_2048x1536 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_2048x1536

TY_IMAGE_MODE_CSI_MONO12_240x96 = _pcammls.TY_IMAGE_MODE_CSI_MONO12_240x96

TY_IMAGE_MODE_CSI_BAYER12GRBG_160x100 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_160x100

TY_IMAGE_MODE_CSI_BAYER12GRBG_160x120 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_160x120

TY_IMAGE_MODE_CSI_BAYER12GRBG_320x180 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_320x180

TY_IMAGE_MODE_CSI_BAYER12GRBG_320x200 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_320x200

TY_IMAGE_MODE_CSI_BAYER12GRBG_320x240 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_320x240

TY_IMAGE_MODE_CSI_BAYER12GRBG_480x640 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_480x640

TY_IMAGE_MODE_CSI_BAYER12GRBG_640x360 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_640x360

TY_IMAGE_MODE_CSI_BAYER12GRBG_640x400 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_640x400

TY_IMAGE_MODE_CSI_BAYER12GRBG_640x480 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_640x480

TY_IMAGE_MODE_CSI_BAYER12GRBG_960x1280 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_960x1280

TY_IMAGE_MODE_CSI_BAYER12GRBG_1280x720 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_1280x720

TY_IMAGE_MODE_CSI_BAYER12GRBG_1280x960 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_1280x960

TY_IMAGE_MODE_CSI_BAYER12GRBG_1280x800 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_1280x800

TY_IMAGE_MODE_CSI_BAYER12GRBG_1600x1200 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_1600x1200
)

TY_IMAGE_MODE_CSI_BAYER12GRBG_800x600 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_800x600

TY_IMAGE_MODE_CSI_BAYER12GRBG_1920x1080 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_1920x1080
)

TY_IMAGE_MODE_CSI_BAYER12GRBG_2560x1920 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_2560x1920
)

TY_IMAGE_MODE_CSI_BAYER12GRBG_2592x1944 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_2592x1944
)

TY_IMAGE_MODE_CSI_BAYER12GRBG_1920x1440 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_1920x1440
)

TY_IMAGE_MODE_CSI_BAYER12GRBG_2048x1536 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_2048x1536
)

TY_IMAGE_MODE_CSI_BAYER12GRBG_240x96 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GRBG_240x96

TY_IMAGE_MODE_CSI_BAYER12RGGB_160x100 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_160x100

TY_IMAGE_MODE_CSI_BAYER12RGGB_160x120 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_160x120

TY_IMAGE_MODE_CSI_BAYER12RGGB_320x180 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_320x180

TY_IMAGE_MODE_CSI_BAYER12RGGB_320x200 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_320x200

TY_IMAGE_MODE_CSI_BAYER12RGGB_320x240 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_320x240

TY_IMAGE_MODE_CSI_BAYER12RGGB_480x640 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_480x640

TY_IMAGE_MODE_CSI_BAYER12RGGB_640x360 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_640x360

TY_IMAGE_MODE_CSI_BAYER12RGGB_640x400 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_640x400

TY_IMAGE_MODE_CSI_BAYER12RGGB_640x480 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_640x480

TY_IMAGE_MODE_CSI_BAYER12RGGB_960x1280 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_960x1280

TY_IMAGE_MODE_CSI_BAYER12RGGB_1280x720 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_1280x720

TY_IMAGE_MODE_CSI_BAYER12RGGB_1280x960 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_1280x960

TY_IMAGE_MODE_CSI_BAYER12RGGB_1280x800 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_1280x800

TY_IMAGE_MODE_CSI_BAYER12RGGB_1600x1200 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_1600x1200
)

TY_IMAGE_MODE_CSI_BAYER12RGGB_800x600 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_800x600

TY_IMAGE_MODE_CSI_BAYER12RGGB_1920x1080 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_1920x1080
)

TY_IMAGE_MODE_CSI_BAYER12RGGB_2560x1920 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_2560x1920
)

TY_IMAGE_MODE_CSI_BAYER12RGGB_2592x1944 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_2592x1944
)

TY_IMAGE_MODE_CSI_BAYER12RGGB_1920x1440 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_1920x1440
)

TY_IMAGE_MODE_CSI_BAYER12RGGB_2048x1536 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_2048x1536
)

TY_IMAGE_MODE_CSI_BAYER12RGGB_240x96 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12RGGB_240x96

TY_IMAGE_MODE_CSI_BAYER12GBRG_160x100 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_160x100

TY_IMAGE_MODE_CSI_BAYER12GBRG_160x120 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_160x120

TY_IMAGE_MODE_CSI_BAYER12GBRG_320x180 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_320x180

TY_IMAGE_MODE_CSI_BAYER12GBRG_320x200 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_320x200

TY_IMAGE_MODE_CSI_BAYER12GBRG_320x240 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_320x240

TY_IMAGE_MODE_CSI_BAYER12GBRG_480x640 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_480x640

TY_IMAGE_MODE_CSI_BAYER12GBRG_640x360 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_640x360

TY_IMAGE_MODE_CSI_BAYER12GBRG_640x400 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_640x400

TY_IMAGE_MODE_CSI_BAYER12GBRG_640x480 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_640x480

TY_IMAGE_MODE_CSI_BAYER12GBRG_960x1280 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_960x1280

TY_IMAGE_MODE_CSI_BAYER12GBRG_1280x720 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_1280x720

TY_IMAGE_MODE_CSI_BAYER12GBRG_1280x960 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_1280x960

TY_IMAGE_MODE_CSI_BAYER12GBRG_1280x800 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_1280x800

TY_IMAGE_MODE_CSI_BAYER12GBRG_1600x1200 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_1600x1200
)

TY_IMAGE_MODE_CSI_BAYER12GBRG_800x600 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_800x600

TY_IMAGE_MODE_CSI_BAYER12GBRG_1920x1080 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_1920x1080
)

TY_IMAGE_MODE_CSI_BAYER12GBRG_2560x1920 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_2560x1920
)

TY_IMAGE_MODE_CSI_BAYER12GBRG_2592x1944 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_2592x1944
)

TY_IMAGE_MODE_CSI_BAYER12GBRG_1920x1440 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_1920x1440
)

TY_IMAGE_MODE_CSI_BAYER12GBRG_2048x1536 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_2048x1536
)

TY_IMAGE_MODE_CSI_BAYER12GBRG_240x96 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12GBRG_240x96

TY_IMAGE_MODE_CSI_BAYER12BGGR_160x100 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_160x100

TY_IMAGE_MODE_CSI_BAYER12BGGR_160x120 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_160x120

TY_IMAGE_MODE_CSI_BAYER12BGGR_320x180 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_320x180

TY_IMAGE_MODE_CSI_BAYER12BGGR_320x200 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_320x200

TY_IMAGE_MODE_CSI_BAYER12BGGR_320x240 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_320x240

TY_IMAGE_MODE_CSI_BAYER12BGGR_480x640 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_480x640

TY_IMAGE_MODE_CSI_BAYER12BGGR_640x360 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_640x360

TY_IMAGE_MODE_CSI_BAYER12BGGR_640x400 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_640x400

TY_IMAGE_MODE_CSI_BAYER12BGGR_640x480 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_640x480

TY_IMAGE_MODE_CSI_BAYER12BGGR_960x1280 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_960x1280

TY_IMAGE_MODE_CSI_BAYER12BGGR_1280x720 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_1280x720

TY_IMAGE_MODE_CSI_BAYER12BGGR_1280x960 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_1280x960

TY_IMAGE_MODE_CSI_BAYER12BGGR_1280x800 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_1280x800

TY_IMAGE_MODE_CSI_BAYER12BGGR_1600x1200 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_1600x1200
)

TY_IMAGE_MODE_CSI_BAYER12BGGR_800x600 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_800x600

TY_IMAGE_MODE_CSI_BAYER12BGGR_1920x1080 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_1920x1080
)

TY_IMAGE_MODE_CSI_BAYER12BGGR_2560x1920 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_2560x1920
)

TY_IMAGE_MODE_CSI_BAYER12BGGR_2592x1944 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_2592x1944
)

TY_IMAGE_MODE_CSI_BAYER12BGGR_1920x1440 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_1920x1440
)

TY_IMAGE_MODE_CSI_BAYER12BGGR_2048x1536 = (
    _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_2048x1536
)

TY_IMAGE_MODE_CSI_BAYER12BGGR_240x96 = _pcammls.TY_IMAGE_MODE_CSI_BAYER12BGGR_240x96

TY_IMAGE_MODE_MJPG_160x100 = _pcammls.TY_IMAGE_MODE_MJPG_160x100

TY_IMAGE_MODE_MJPG_160x120 = _pcammls.TY_IMAGE_MODE_MJPG_160x120

TY_IMAGE_MODE_MJPG_320x180 = _pcammls.TY_IMAGE_MODE_MJPG_320x180

TY_IMAGE_MODE_MJPG_320x200 = _pcammls.TY_IMAGE_MODE_MJPG_320x200

TY_IMAGE_MODE_MJPG_320x240 = _pcammls.TY_IMAGE_MODE_MJPG_320x240

TY_IMAGE_MODE_MJPG_480x640 = _pcammls.TY_IMAGE_MODE_MJPG_480x640

TY_IMAGE_MODE_MJPG_640x360 = _pcammls.TY_IMAGE_MODE_MJPG_640x360

TY_IMAGE_MODE_MJPG_640x400 = _pcammls.TY_IMAGE_MODE_MJPG_640x400

TY_IMAGE_MODE_MJPG_640x480 = _pcammls.TY_IMAGE_MODE_MJPG_640x480

TY_IMAGE_MODE_MJPG_960x1280 = _pcammls.TY_IMAGE_MODE_MJPG_960x1280

TY_IMAGE_MODE_MJPG_1280x720 = _pcammls.TY_IMAGE_MODE_MJPG_1280x720

TY_IMAGE_MODE_MJPG_1280x960 = _pcammls.TY_IMAGE_MODE_MJPG_1280x960

TY_IMAGE_MODE_MJPG_1280x800 = _pcammls.TY_IMAGE_MODE_MJPG_1280x800

TY_IMAGE_MODE_MJPG_1600x1200 = _pcammls.TY_IMAGE_MODE_MJPG_1600x1200

TY_IMAGE_MODE_MJPG_800x600 = _pcammls.TY_IMAGE_MODE_MJPG_800x600

TY_IMAGE_MODE_MJPG_1920x1080 = _pcammls.TY_IMAGE_MODE_MJPG_1920x1080

TY_IMAGE_MODE_MJPG_2560x1920 = _pcammls.TY_IMAGE_MODE_MJPG_2560x1920

TY_IMAGE_MODE_MJPG_2592x1944 = _pcammls.TY_IMAGE_MODE_MJPG_2592x1944

TY_IMAGE_MODE_MJPG_1920x1440 = _pcammls.TY_IMAGE_MODE_MJPG_1920x1440

TY_IMAGE_MODE_MJPG_2048x1536 = _pcammls.TY_IMAGE_MODE_MJPG_2048x1536

TY_IMAGE_MODE_MJPG_240x96 = _pcammls.TY_IMAGE_MODE_MJPG_240x96

TY_IMAGE_MODE_RGB48_160x100 = _pcammls.TY_IMAGE_MODE_RGB48_160x100

TY_IMAGE_MODE_RGB48_160x120 = _pcammls.TY_IMAGE_MODE_RGB48_160x120

TY_IMAGE_MODE_RGB48_320x180 = _pcammls.TY_IMAGE_MODE_RGB48_320x180

TY_IMAGE_MODE_RGB48_320x200 = _pcammls.TY_IMAGE_MODE_RGB48_320x200

TY_IMAGE_MODE_RGB48_320x240 = _pcammls.TY_IMAGE_MODE_RGB48_320x240

TY_IMAGE_MODE_RGB48_480x640 = _pcammls.TY_IMAGE_MODE_RGB48_480x640

TY_IMAGE_MODE_RGB48_640x360 = _pcammls.TY_IMAGE_MODE_RGB48_640x360

TY_IMAGE_MODE_RGB48_640x400 = _pcammls.TY_IMAGE_MODE_RGB48_640x400

TY_IMAGE_MODE_RGB48_640x480 = _pcammls.TY_IMAGE_MODE_RGB48_640x480

TY_IMAGE_MODE_RGB48_960x1280 = _pcammls.TY_IMAGE_MODE_RGB48_960x1280

TY_IMAGE_MODE_RGB48_1280x720 = _pcammls.TY_IMAGE_MODE_RGB48_1280x720

TY_IMAGE_MODE_RGB48_1280x960 = _pcammls.TY_IMAGE_MODE_RGB48_1280x960

TY_IMAGE_MODE_RGB48_1280x800 = _pcammls.TY_IMAGE_MODE_RGB48_1280x800

TY_IMAGE_MODE_RGB48_1600x1200 = _pcammls.TY_IMAGE_MODE_RGB48_1600x1200

TY_IMAGE_MODE_RGB48_800x600 = _pcammls.TY_IMAGE_MODE_RGB48_800x600

TY_IMAGE_MODE_RGB48_1920x1080 = _pcammls.TY_IMAGE_MODE_RGB48_1920x1080

TY_IMAGE_MODE_RGB48_2560x1920 = _pcammls.TY_IMAGE_MODE_RGB48_2560x1920

TY_IMAGE_MODE_RGB48_2592x1944 = _pcammls.TY_IMAGE_MODE_RGB48_2592x1944

TY_IMAGE_MODE_RGB48_1920x1440 = _pcammls.TY_IMAGE_MODE_RGB48_1920x1440

TY_IMAGE_MODE_RGB48_2048x1536 = _pcammls.TY_IMAGE_MODE_RGB48_2048x1536

TY_IMAGE_MODE_RGB48_240x96 = _pcammls.TY_IMAGE_MODE_RGB48_240x96

TY_IMAGE_MODE_BGR48_160x100 = _pcammls.TY_IMAGE_MODE_BGR48_160x100

TY_IMAGE_MODE_BGR48_160x120 = _pcammls.TY_IMAGE_MODE_BGR48_160x120

TY_IMAGE_MODE_BGR48_320x180 = _pcammls.TY_IMAGE_MODE_BGR48_320x180

TY_IMAGE_MODE_BGR48_320x200 = _pcammls.TY_IMAGE_MODE_BGR48_320x200

TY_IMAGE_MODE_BGR48_320x240 = _pcammls.TY_IMAGE_MODE_BGR48_320x240

TY_IMAGE_MODE_BGR48_480x640 = _pcammls.TY_IMAGE_MODE_BGR48_480x640

TY_IMAGE_MODE_BGR48_640x360 = _pcammls.TY_IMAGE_MODE_BGR48_640x360

TY_IMAGE_MODE_BGR48_640x400 = _pcammls.TY_IMAGE_MODE_BGR48_640x400

TY_IMAGE_MODE_BGR48_640x480 = _pcammls.TY_IMAGE_MODE_BGR48_640x480

TY_IMAGE_MODE_BGR48_960x1280 = _pcammls.TY_IMAGE_MODE_BGR48_960x1280

TY_IMAGE_MODE_BGR48_1280x720 = _pcammls.TY_IMAGE_MODE_BGR48_1280x720

TY_IMAGE_MODE_BGR48_1280x960 = _pcammls.TY_IMAGE_MODE_BGR48_1280x960

TY_IMAGE_MODE_BGR48_1280x800 = _pcammls.TY_IMAGE_MODE_BGR48_1280x800

TY_IMAGE_MODE_BGR48_1600x1200 = _pcammls.TY_IMAGE_MODE_BGR48_1600x1200

TY_IMAGE_MODE_BGR48_800x600 = _pcammls.TY_IMAGE_MODE_BGR48_800x600

TY_IMAGE_MODE_BGR48_1920x1080 = _pcammls.TY_IMAGE_MODE_BGR48_1920x1080

TY_IMAGE_MODE_BGR48_2560x1920 = _pcammls.TY_IMAGE_MODE_BGR48_2560x1920

TY_IMAGE_MODE_BGR48_2592x1944 = _pcammls.TY_IMAGE_MODE_BGR48_2592x1944

TY_IMAGE_MODE_BGR48_1920x1440 = _pcammls.TY_IMAGE_MODE_BGR48_1920x1440

TY_IMAGE_MODE_BGR48_2048x1536 = _pcammls.TY_IMAGE_MODE_BGR48_2048x1536

TY_IMAGE_MODE_BGR48_240x96 = _pcammls.TY_IMAGE_MODE_BGR48_240x96

TY_IMAGE_MODE_BGR_160x100 = _pcammls.TY_IMAGE_MODE_BGR_160x100

TY_IMAGE_MODE_BGR_160x120 = _pcammls.TY_IMAGE_MODE_BGR_160x120

TY_IMAGE_MODE_BGR_320x180 = _pcammls.TY_IMAGE_MODE_BGR_320x180

TY_IMAGE_MODE_BGR_320x200 = _pcammls.TY_IMAGE_MODE_BGR_320x200

TY_IMAGE_MODE_BGR_320x240 = _pcammls.TY_IMAGE_MODE_BGR_320x240

TY_IMAGE_MODE_BGR_480x640 = _pcammls.TY_IMAGE_MODE_BGR_480x640

TY_IMAGE_MODE_BGR_640x360 = _pcammls.TY_IMAGE_MODE_BGR_640x360

TY_IMAGE_MODE_BGR_640x400 = _pcammls.TY_IMAGE_MODE_BGR_640x400

TY_IMAGE_MODE_BGR_640x480 = _pcammls.TY_IMAGE_MODE_BGR_640x480

TY_IMAGE_MODE_BGR_960x1280 = _pcammls.TY_IMAGE_MODE_BGR_960x1280

TY_IMAGE_MODE_BGR_1280x720 = _pcammls.TY_IMAGE_MODE_BGR_1280x720

TY_IMAGE_MODE_BGR_1280x960 = _pcammls.TY_IMAGE_MODE_BGR_1280x960

TY_IMAGE_MODE_BGR_1280x800 = _pcammls.TY_IMAGE_MODE_BGR_1280x800

TY_IMAGE_MODE_BGR_1600x1200 = _pcammls.TY_IMAGE_MODE_BGR_1600x1200

TY_IMAGE_MODE_BGR_800x600 = _pcammls.TY_IMAGE_MODE_BGR_800x600

TY_IMAGE_MODE_BGR_1920x1080 = _pcammls.TY_IMAGE_MODE_BGR_1920x1080

TY_IMAGE_MODE_BGR_2560x1920 = _pcammls.TY_IMAGE_MODE_BGR_2560x1920

TY_IMAGE_MODE_BGR_2592x1944 = _pcammls.TY_IMAGE_MODE_BGR_2592x1944

TY_IMAGE_MODE_BGR_1920x1440 = _pcammls.TY_IMAGE_MODE_BGR_1920x1440

TY_IMAGE_MODE_BGR_2048x1536 = _pcammls.TY_IMAGE_MODE_BGR_2048x1536

TY_IMAGE_MODE_BGR_240x96 = _pcammls.TY_IMAGE_MODE_BGR_240x96

TY_IMAGE_MODE_XYZ48_160x100 = _pcammls.TY_IMAGE_MODE_XYZ48_160x100

TY_IMAGE_MODE_XYZ48_160x120 = _pcammls.TY_IMAGE_MODE_XYZ48_160x120

TY_IMAGE_MODE_XYZ48_320x180 = _pcammls.TY_IMAGE_MODE_XYZ48_320x180

TY_IMAGE_MODE_XYZ48_320x200 = _pcammls.TY_IMAGE_MODE_XYZ48_320x200

TY_IMAGE_MODE_XYZ48_320x240 = _pcammls.TY_IMAGE_MODE_XYZ48_320x240

TY_IMAGE_MODE_XYZ48_480x640 = _pcammls.TY_IMAGE_MODE_XYZ48_480x640

TY_IMAGE_MODE_XYZ48_640x360 = _pcammls.TY_IMAGE_MODE_XYZ48_640x360

TY_IMAGE_MODE_XYZ48_640x400 = _pcammls.TY_IMAGE_MODE_XYZ48_640x400

TY_IMAGE_MODE_XYZ48_640x480 = _pcammls.TY_IMAGE_MODE_XYZ48_640x480

TY_IMAGE_MODE_XYZ48_960x1280 = _pcammls.TY_IMAGE_MODE_XYZ48_960x1280

TY_IMAGE_MODE_XYZ48_1280x720 = _pcammls.TY_IMAGE_MODE_XYZ48_1280x720

TY_IMAGE_MODE_XYZ48_1280x960 = _pcammls.TY_IMAGE_MODE_XYZ48_1280x960

TY_IMAGE_MODE_XYZ48_1280x800 = _pcammls.TY_IMAGE_MODE_XYZ48_1280x800

TY_IMAGE_MODE_XYZ48_1600x1200 = _pcammls.TY_IMAGE_MODE_XYZ48_1600x1200

TY_IMAGE_MODE_XYZ48_800x600 = _pcammls.TY_IMAGE_MODE_XYZ48_800x600

TY_IMAGE_MODE_XYZ48_1920x1080 = _pcammls.TY_IMAGE_MODE_XYZ48_1920x1080

TY_IMAGE_MODE_XYZ48_2560x1920 = _pcammls.TY_IMAGE_MODE_XYZ48_2560x1920

TY_IMAGE_MODE_XYZ48_2592x1944 = _pcammls.TY_IMAGE_MODE_XYZ48_2592x1944

TY_IMAGE_MODE_XYZ48_1920x1440 = _pcammls.TY_IMAGE_MODE_XYZ48_1920x1440

TY_IMAGE_MODE_XYZ48_2048x1536 = _pcammls.TY_IMAGE_MODE_XYZ48_2048x1536

TY_IMAGE_MODE_XYZ48_240x96 = _pcammls.TY_IMAGE_MODE_XYZ48_240x96

TY_TRIGGER_MODE_OFF = _pcammls.TY_TRIGGER_MODE_OFF
r"""not trigger mode, continuous mode"""
TY_TRIGGER_MODE_SLAVE = _pcammls.TY_TRIGGER_MODE_SLAVE
r"""slave mode, receive soft/hardware triggers"""
TY_TRIGGER_MODE_M_SIG = _pcammls.TY_TRIGGER_MODE_M_SIG
r"""master mode 1, sending one trigger signal once received a soft trigger"""
TY_TRIGGER_MODE_M_PER = _pcammls.TY_TRIGGER_MODE_M_PER
r"""master mode 2, periodic sending one trigger signals, 'fps' param should be set"""
TY_TRIGGER_MODE_SIG_PASS = _pcammls.TY_TRIGGER_MODE_SIG_PASS
r"""discard, using TY_TRIGGER_MODE28"""
TY_TRIGGER_MODE_PER_PASS = _pcammls.TY_TRIGGER_MODE_PER_PASS
r"""discard, using TY_TRIGGER_MODE29"""
TY_TRIGGER_MODE_TIMER_LIST = _pcammls.TY_TRIGGER_MODE_TIMER_LIST

TY_TRIGGER_MODE_TIMER_PERIOD = _pcammls.TY_TRIGGER_MODE_TIMER_PERIOD

TY_TRIGGER_MODE28 = _pcammls.TY_TRIGGER_MODE28

TY_TRIGGER_MODE29 = _pcammls.TY_TRIGGER_MODE29

TY_TRIGGER_MODE_PER_PASS2 = _pcammls.TY_TRIGGER_MODE_PER_PASS2
r"""trigger mode 30,Alternate output depth image/ir image"""
TY_TRIGGER_WORK_MODE31 = _pcammls.TY_TRIGGER_WORK_MODE31

TY_TRIGGER_MODE_SIG_LASER = _pcammls.TY_TRIGGER_MODE_SIG_LASER

TY_TIME_SYNC_TYPE_NONE = _pcammls.TY_TIME_SYNC_TYPE_NONE

TY_TIME_SYNC_TYPE_HOST = _pcammls.TY_TIME_SYNC_TYPE_HOST

TY_TIME_SYNC_TYPE_NTP = _pcammls.TY_TIME_SYNC_TYPE_NTP

TY_TIME_SYNC_TYPE_PTP = _pcammls.TY_TIME_SYNC_TYPE_PTP

TY_TIME_SYNC_TYPE_CAN = _pcammls.TY_TIME_SYNC_TYPE_CAN

TY_TIME_SYNC_TYPE_PTP_MASTER = _pcammls.TY_TIME_SYNC_TYPE_PTP_MASTER

TY_EXT_SUP = _pcammls.TY_EXT_SUP

TY_DO_5V = _pcammls.TY_DO_5V

TY_DO_12V = _pcammls.TY_DO_12V

TY_DO_LOW = _pcammls.TY_DO_LOW

TY_DO_HIGH = _pcammls.TY_DO_HIGH

TY_DO_PWM = _pcammls.TY_DO_PWM

TY_DO_CAM_TRIG = _pcammls.TY_DO_CAM_TRIG

TY_DI_POLL = _pcammls.TY_DI_POLL

TY_DI_NE_INT = _pcammls.TY_DI_NE_INT

TY_DI_PE_INT = _pcammls.TY_DI_PE_INT

TY_DI_INT_NO_OP = _pcammls.TY_DI_INT_NO_OP

TY_DI_INT_TRIG_CAP = _pcammls.TY_DI_INT_TRIG_CAP

TY_DI_INT_EVENT = _pcammls.TY_DI_INT_EVENT

TY_TEMPERATURE_LEFT = _pcammls.TY_TEMPERATURE_LEFT

TY_TEMPERATURE_RIGHT = _pcammls.TY_TEMPERATURE_RIGHT

TY_TEMPERATURE_COLOR = _pcammls.TY_TEMPERATURE_COLOR

TY_TEMPERATURE_CPU = _pcammls.TY_TEMPERATURE_CPU

TY_TEMPERATURE_MAIN_BOARD = _pcammls.TY_TEMPERATURE_MAIN_BOARD

TY_LOG_LEVEL_VERBOSE = _pcammls.TY_LOG_LEVEL_VERBOSE

TY_LOG_LEVEL_DEBUG = _pcammls.TY_LOG_LEVEL_DEBUG

TY_LOG_LEVEL_INFO = _pcammls.TY_LOG_LEVEL_INFO

TY_LOG_LEVEL_WARNING = _pcammls.TY_LOG_LEVEL_WARNING

TY_LOG_LEVEL_ERROR = _pcammls.TY_LOG_LEVEL_ERROR

TY_LOG_LEVEL_NEVER = _pcammls.TY_LOG_LEVEL_NEVER


class TY_VERSION_INFO(object):
    r"""Proxy of C++ TY_VERSION_INFO class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    major = property(
        _pcammls.TY_VERSION_INFO_major_get,
        _pcammls.TY_VERSION_INFO_major_set,
        doc=r"""major""",
    )
    minor = property(
        _pcammls.TY_VERSION_INFO_minor_get,
        _pcammls.TY_VERSION_INFO_minor_set,
        doc=r"""minor""",
    )
    patch = property(
        _pcammls.TY_VERSION_INFO_patch_get,
        _pcammls.TY_VERSION_INFO_patch_set,
        doc=r"""patch""",
    )
    reserved = property(
        _pcammls.TY_VERSION_INFO_reserved_get,
        _pcammls.TY_VERSION_INFO_reserved_set,
        doc=r"""reserved""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_VERSION_INFO"""
        _pcammls.TY_VERSION_INFO_swiginit(self, _pcammls.new_TY_VERSION_INFO())

    __swig_destroy__ = _pcammls.delete_TY_VERSION_INFO


# Register TY_VERSION_INFO in _pcammls:
_pcammls.TY_VERSION_INFO_swigregister(TY_VERSION_INFO)


class TY_DEVICE_NET_INFO(object):
    r"""Proxy of C++ TY_DEVICE_NET_INFO class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    reserved = property(
        _pcammls.TY_DEVICE_NET_INFO_reserved_get,
        _pcammls.TY_DEVICE_NET_INFO_reserved_set,
        doc=r"""reserved""",
    )

    def mac(self):
        r"""mac(self) -> char const *"""
        return _pcammls.TY_DEVICE_NET_INFO_mac(self)

    def ip(self):
        r"""ip(self) -> char const *"""
        return _pcammls.TY_DEVICE_NET_INFO_ip(self)

    def netmask(self):
        r"""netmask(self) -> char const *"""
        return _pcammls.TY_DEVICE_NET_INFO_netmask(self)

    def gateway(self):
        r"""gateway(self) -> char const *"""
        return _pcammls.TY_DEVICE_NET_INFO_gateway(self)

    def broadcast(self):
        r"""broadcast(self) -> char const *"""
        return _pcammls.TY_DEVICE_NET_INFO_broadcast(self)

    def __init__(self):
        r"""__init__(self) -> TY_DEVICE_NET_INFO"""
        _pcammls.TY_DEVICE_NET_INFO_swiginit(self, _pcammls.new_TY_DEVICE_NET_INFO())

    __swig_destroy__ = _pcammls.delete_TY_DEVICE_NET_INFO


# Register TY_DEVICE_NET_INFO in _pcammls:
_pcammls.TY_DEVICE_NET_INFO_swigregister(TY_DEVICE_NET_INFO)


class TY_DEVICE_USB_INFO(object):
    r"""Proxy of C++ TY_DEVICE_USB_INFO class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    bus = property(
        _pcammls.TY_DEVICE_USB_INFO_bus_get,
        _pcammls.TY_DEVICE_USB_INFO_bus_set,
        doc=r"""bus""",
    )
    addr = property(
        _pcammls.TY_DEVICE_USB_INFO_addr_get,
        _pcammls.TY_DEVICE_USB_INFO_addr_set,
        doc=r"""addr""",
    )
    reserved = property(
        _pcammls.TY_DEVICE_USB_INFO_reserved_get,
        _pcammls.TY_DEVICE_USB_INFO_reserved_set,
        doc=r"""reserved""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_DEVICE_USB_INFO"""
        _pcammls.TY_DEVICE_USB_INFO_swiginit(self, _pcammls.new_TY_DEVICE_USB_INFO())

    __swig_destroy__ = _pcammls.delete_TY_DEVICE_USB_INFO


# Register TY_DEVICE_USB_INFO in _pcammls:
_pcammls.TY_DEVICE_USB_INFO_swigregister(TY_DEVICE_USB_INFO)


class TY_INTERFACE_INFO(object):
    r"""Proxy of C++ TY_INTERFACE_INFO class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    name = property(
        _pcammls.TY_INTERFACE_INFO_name_get,
        _pcammls.TY_INTERFACE_INFO_name_set,
        doc=r"""name""",
    )
    id = property(
        _pcammls.TY_INTERFACE_INFO_id_get,
        _pcammls.TY_INTERFACE_INFO_id_set,
        doc=r"""id""",
    )
    type = property(
        _pcammls.TY_INTERFACE_INFO_type_get,
        _pcammls.TY_INTERFACE_INFO_type_set,
        doc=r"""type""",
    )
    reserved = property(
        _pcammls.TY_INTERFACE_INFO_reserved_get,
        _pcammls.TY_INTERFACE_INFO_reserved_set,
        doc=r"""reserved""",
    )
    netInfo = property(
        _pcammls.TY_INTERFACE_INFO_netInfo_get,
        _pcammls.TY_INTERFACE_INFO_netInfo_set,
        doc=r"""netInfo""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_INTERFACE_INFO"""
        _pcammls.TY_INTERFACE_INFO_swiginit(self, _pcammls.new_TY_INTERFACE_INFO())

    __swig_destroy__ = _pcammls.delete_TY_INTERFACE_INFO


# Register TY_INTERFACE_INFO in _pcammls:
_pcammls.TY_INTERFACE_INFO_swigregister(TY_INTERFACE_INFO)


class TY_DEVICE_BASE_INFO(object):
    r"""Proxy of C++ TY_DEVICE_BASE_INFO class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    iface = property(
        _pcammls.TY_DEVICE_BASE_INFO_iface_get,
        _pcammls.TY_DEVICE_BASE_INFO_iface_set,
        doc=r"""iface""",
    )
    id = property(
        _pcammls.TY_DEVICE_BASE_INFO_id_get,
        _pcammls.TY_DEVICE_BASE_INFO_id_set,
        doc=r"""id""",
    )
    vendorName = property(
        _pcammls.TY_DEVICE_BASE_INFO_vendorName_get,
        _pcammls.TY_DEVICE_BASE_INFO_vendorName_set,
        doc=r"""vendorName""",
    )
    userDefinedName = property(
        _pcammls.TY_DEVICE_BASE_INFO_userDefinedName_get,
        _pcammls.TY_DEVICE_BASE_INFO_userDefinedName_set,
        doc=r"""userDefinedName""",
    )
    modelName = property(
        _pcammls.TY_DEVICE_BASE_INFO_modelName_get,
        _pcammls.TY_DEVICE_BASE_INFO_modelName_set,
        doc=r"""modelName""",
    )
    hardwareVersion = property(
        _pcammls.TY_DEVICE_BASE_INFO_hardwareVersion_get,
        _pcammls.TY_DEVICE_BASE_INFO_hardwareVersion_set,
        doc=r"""hardwareVersion""",
    )
    firmwareVersion = property(
        _pcammls.TY_DEVICE_BASE_INFO_firmwareVersion_get,
        _pcammls.TY_DEVICE_BASE_INFO_firmwareVersion_set,
        doc=r"""firmwareVersion""",
    )
    buildHash = property(
        _pcammls.TY_DEVICE_BASE_INFO_buildHash_get,
        _pcammls.TY_DEVICE_BASE_INFO_buildHash_set,
        doc=r"""buildHash""",
    )
    configVersion = property(
        _pcammls.TY_DEVICE_BASE_INFO_configVersion_get,
        _pcammls.TY_DEVICE_BASE_INFO_configVersion_set,
        doc=r"""configVersion""",
    )
    reserved = property(
        _pcammls.TY_DEVICE_BASE_INFO_reserved_get,
        _pcammls.TY_DEVICE_BASE_INFO_reserved_set,
        doc=r"""reserved""",
    )

    def type(self):
        r"""type(self) -> TY_INTERFACE_TYPE"""
        return _pcammls.TY_DEVICE_BASE_INFO_type(self)

    def get_netinfo(self):
        r"""get_netinfo(self) -> TY_DEVICE_NET_INFO"""
        return _pcammls.TY_DEVICE_BASE_INFO_get_netinfo(self)

    def get_usbinfo(self):
        r"""get_usbinfo(self) -> TY_DEVICE_USB_INFO"""
        return _pcammls.TY_DEVICE_BASE_INFO_get_usbinfo(self)

    def __init__(self):
        r"""__init__(self) -> TY_DEVICE_BASE_INFO"""
        _pcammls.TY_DEVICE_BASE_INFO_swiginit(self, _pcammls.new_TY_DEVICE_BASE_INFO())

    __swig_destroy__ = _pcammls.delete_TY_DEVICE_BASE_INFO


# Register TY_DEVICE_BASE_INFO in _pcammls:
_pcammls.TY_DEVICE_BASE_INFO_swigregister(TY_DEVICE_BASE_INFO)

BEGINNER = _pcammls.BEGINNER

EXPERT = _pcammls.EXPERT

GURU = _pcammls.GURU


class TY_FEATURE_INFO(object):
    r"""Proxy of C++ TY_FEATURE_INFO class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    isValid = property(
        _pcammls.TY_FEATURE_INFO_isValid_get,
        _pcammls.TY_FEATURE_INFO_isValid_set,
        doc=r"""isValid""",
    )
    accessMode = property(
        _pcammls.TY_FEATURE_INFO_accessMode_get,
        _pcammls.TY_FEATURE_INFO_accessMode_set,
        doc=r"""accessMode""",
    )
    writableAtRun = property(
        _pcammls.TY_FEATURE_INFO_writableAtRun_get,
        _pcammls.TY_FEATURE_INFO_writableAtRun_set,
        doc=r"""writableAtRun""",
    )
    reserved0 = property(
        _pcammls.TY_FEATURE_INFO_reserved0_get,
        _pcammls.TY_FEATURE_INFO_reserved0_set,
        doc=r"""reserved0""",
    )
    componentID = property(
        _pcammls.TY_FEATURE_INFO_componentID_get,
        _pcammls.TY_FEATURE_INFO_componentID_set,
        doc=r"""componentID""",
    )
    featureID = property(
        _pcammls.TY_FEATURE_INFO_featureID_get,
        _pcammls.TY_FEATURE_INFO_featureID_set,
        doc=r"""featureID""",
    )
    name = property(
        _pcammls.TY_FEATURE_INFO_name_get,
        _pcammls.TY_FEATURE_INFO_name_set,
        doc=r"""name""",
    )
    bindComponentID = property(
        _pcammls.TY_FEATURE_INFO_bindComponentID_get,
        _pcammls.TY_FEATURE_INFO_bindComponentID_set,
        doc=r"""bindComponentID""",
    )
    bindFeatureID = property(
        _pcammls.TY_FEATURE_INFO_bindFeatureID_get,
        _pcammls.TY_FEATURE_INFO_bindFeatureID_set,
        doc=r"""bindFeatureID""",
    )
    visibility = property(
        _pcammls.TY_FEATURE_INFO_visibility_get,
        _pcammls.TY_FEATURE_INFO_visibility_set,
        doc=r"""visibility""",
    )
    reserved = property(
        _pcammls.TY_FEATURE_INFO_reserved_get,
        _pcammls.TY_FEATURE_INFO_reserved_set,
        doc=r"""reserved""",
    )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_FEATURE_INFO_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_FEATURE_INFO"""
        _pcammls.TY_FEATURE_INFO_swiginit(self, _pcammls.new_TY_FEATURE_INFO())

    __swig_destroy__ = _pcammls.delete_TY_FEATURE_INFO


# Register TY_FEATURE_INFO in _pcammls:
_pcammls.TY_FEATURE_INFO_swigregister(TY_FEATURE_INFO)


class TY_INT_RANGE(object):
    r"""Proxy of C++ TY_INT_RANGE class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    min = property(
        _pcammls.TY_INT_RANGE_min_get, _pcammls.TY_INT_RANGE_min_set, doc=r"""min"""
    )
    max = property(
        _pcammls.TY_INT_RANGE_max_get, _pcammls.TY_INT_RANGE_max_set, doc=r"""max"""
    )
    inc = property(
        _pcammls.TY_INT_RANGE_inc_get, _pcammls.TY_INT_RANGE_inc_set, doc=r"""inc"""
    )
    reserved = property(
        _pcammls.TY_INT_RANGE_reserved_get,
        _pcammls.TY_INT_RANGE_reserved_set,
        doc=r"""reserved""",
    )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_INT_RANGE_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_INT_RANGE"""
        _pcammls.TY_INT_RANGE_swiginit(self, _pcammls.new_TY_INT_RANGE())

    __swig_destroy__ = _pcammls.delete_TY_INT_RANGE


# Register TY_INT_RANGE in _pcammls:
_pcammls.TY_INT_RANGE_swigregister(TY_INT_RANGE)


class TY_FLOAT_RANGE(object):
    r"""Proxy of C++ TY_FLOAT_RANGE class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    min = property(
        _pcammls.TY_FLOAT_RANGE_min_get, _pcammls.TY_FLOAT_RANGE_min_set, doc=r"""min"""
    )
    max = property(
        _pcammls.TY_FLOAT_RANGE_max_get, _pcammls.TY_FLOAT_RANGE_max_set, doc=r"""max"""
    )
    inc = property(
        _pcammls.TY_FLOAT_RANGE_inc_get, _pcammls.TY_FLOAT_RANGE_inc_set, doc=r"""inc"""
    )
    reserved = property(
        _pcammls.TY_FLOAT_RANGE_reserved_get,
        _pcammls.TY_FLOAT_RANGE_reserved_set,
        doc=r"""reserved""",
    )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_FLOAT_RANGE_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_FLOAT_RANGE"""
        _pcammls.TY_FLOAT_RANGE_swiginit(self, _pcammls.new_TY_FLOAT_RANGE())

    __swig_destroy__ = _pcammls.delete_TY_FLOAT_RANGE


# Register TY_FLOAT_RANGE in _pcammls:
_pcammls.TY_FLOAT_RANGE_swigregister(TY_FLOAT_RANGE)


class TY_BYTEARRAY_ATTR(object):
    r"""Proxy of C++ TY_BYTEARRAY_ATTR class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    size = property(
        _pcammls.TY_BYTEARRAY_ATTR_size_get,
        _pcammls.TY_BYTEARRAY_ATTR_size_set,
        doc=r"""size""",
    )
    unit_size = property(
        _pcammls.TY_BYTEARRAY_ATTR_unit_size_get,
        _pcammls.TY_BYTEARRAY_ATTR_unit_size_set,
        doc=r"""unit_size""",
    )
    valid_size = property(
        _pcammls.TY_BYTEARRAY_ATTR_valid_size_get,
        _pcammls.TY_BYTEARRAY_ATTR_valid_size_set,
        doc=r"""valid_size""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_BYTEARRAY_ATTR"""
        _pcammls.TY_BYTEARRAY_ATTR_swiginit(self, _pcammls.new_TY_BYTEARRAY_ATTR())

    __swig_destroy__ = _pcammls.delete_TY_BYTEARRAY_ATTR


# Register TY_BYTEARRAY_ATTR in _pcammls:
_pcammls.TY_BYTEARRAY_ATTR_swigregister(TY_BYTEARRAY_ATTR)


class TY_ENUM_ENTRY(object):
    r"""Proxy of C++ TY_ENUM_ENTRY class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    description = property(
        _pcammls.TY_ENUM_ENTRY_description_get,
        _pcammls.TY_ENUM_ENTRY_description_set,
        doc=r"""description""",
    )
    value = property(
        _pcammls.TY_ENUM_ENTRY_value_get,
        _pcammls.TY_ENUM_ENTRY_value_set,
        doc=r"""value""",
    )
    reserved = property(
        _pcammls.TY_ENUM_ENTRY_reserved_get,
        _pcammls.TY_ENUM_ENTRY_reserved_set,
        doc=r"""reserved""",
    )

    def getDesc(self):
        r"""getDesc(self) -> char const *"""
        return _pcammls.TY_ENUM_ENTRY_getDesc(self)

    def getValue(self):
        r"""getValue(self) -> unsigned int"""
        return _pcammls.TY_ENUM_ENTRY_getValue(self)

    def __init__(self):
        r"""__init__(self) -> TY_ENUM_ENTRY"""
        _pcammls.TY_ENUM_ENTRY_swiginit(self, _pcammls.new_TY_ENUM_ENTRY())

    __swig_destroy__ = _pcammls.delete_TY_ENUM_ENTRY


# Register TY_ENUM_ENTRY in _pcammls:
_pcammls.TY_ENUM_ENTRY_swigregister(TY_ENUM_ENTRY)


class TY_VECT_3F(object):
    r"""Proxy of C++ TY_VECT_3F class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    x = property(_pcammls.TY_VECT_3F_x_get, _pcammls.TY_VECT_3F_x_set, doc=r"""x""")
    y = property(_pcammls.TY_VECT_3F_y_get, _pcammls.TY_VECT_3F_y_set, doc=r"""y""")
    z = property(_pcammls.TY_VECT_3F_z_get, _pcammls.TY_VECT_3F_z_set, doc=r"""z""")

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_VECT_3F_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_VECT_3F"""
        _pcammls.TY_VECT_3F_swiginit(self, _pcammls.new_TY_VECT_3F())

    __swig_destroy__ = _pcammls.delete_TY_VECT_3F


# Register TY_VECT_3F in _pcammls:
_pcammls.TY_VECT_3F_swigregister(TY_VECT_3F)


class TY_CAMERA_INTRINSIC(object):
    r"""Proxy of C++ TY_CAMERA_INTRINSIC class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    data = property(
        _pcammls.TY_CAMERA_INTRINSIC_data_get,
        _pcammls.TY_CAMERA_INTRINSIC_data_set,
        doc=r"""data""",
    )

    def set(self, idx, value):
        r"""
        set(self, idx, value)

        Parameters
        ----------
        idx: int
        value: float

        """
        return _pcammls.TY_CAMERA_INTRINSIC_set(self, idx, value)

    def resize(self, src, scaleX, scaleY):
        r"""
        resize(self, src, scaleX, scaleY)

        Parameters
        ----------
        src: TY_CAMERA_INTRINSIC
        scaleX: float
        scaleY: float

        """
        return _pcammls.TY_CAMERA_INTRINSIC_resize(self, src, scaleX, scaleY)

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_CAMERA_INTRINSIC_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_CAMERA_INTRINSIC"""
        _pcammls.TY_CAMERA_INTRINSIC_swiginit(self, _pcammls.new_TY_CAMERA_INTRINSIC())

    __swig_destroy__ = _pcammls.delete_TY_CAMERA_INTRINSIC


# Register TY_CAMERA_INTRINSIC in _pcammls:
_pcammls.TY_CAMERA_INTRINSIC_swigregister(TY_CAMERA_INTRINSIC)


class TY_CAMERA_EXTRINSIC(object):
    r"""Proxy of C++ TY_CAMERA_EXTRINSIC class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    data = property(
        _pcammls.TY_CAMERA_EXTRINSIC_data_get,
        _pcammls.TY_CAMERA_EXTRINSIC_data_set,
        doc=r"""data""",
    )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_CAMERA_EXTRINSIC_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_CAMERA_EXTRINSIC"""
        _pcammls.TY_CAMERA_EXTRINSIC_swiginit(self, _pcammls.new_TY_CAMERA_EXTRINSIC())

    __swig_destroy__ = _pcammls.delete_TY_CAMERA_EXTRINSIC


# Register TY_CAMERA_EXTRINSIC in _pcammls:
_pcammls.TY_CAMERA_EXTRINSIC_swigregister(TY_CAMERA_EXTRINSIC)


class TY_CAMERA_DISTORTION(object):
    r"""Proxy of C++ TY_CAMERA_DISTORTION class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    data = property(
        _pcammls.TY_CAMERA_DISTORTION_data_get,
        _pcammls.TY_CAMERA_DISTORTION_data_set,
        doc=r"""data""",
    )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_CAMERA_DISTORTION_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_CAMERA_DISTORTION"""
        _pcammls.TY_CAMERA_DISTORTION_swiginit(
            self, _pcammls.new_TY_CAMERA_DISTORTION()
        )

    __swig_destroy__ = _pcammls.delete_TY_CAMERA_DISTORTION


# Register TY_CAMERA_DISTORTION in _pcammls:
_pcammls.TY_CAMERA_DISTORTION_swigregister(TY_CAMERA_DISTORTION)


class TY_CAMERA_CALIB_INFO(object):
    r"""Proxy of C++ TY_CAMERA_CALIB_INFO class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    intrinsicWidth = property(
        _pcammls.TY_CAMERA_CALIB_INFO_intrinsicWidth_get,
        _pcammls.TY_CAMERA_CALIB_INFO_intrinsicWidth_set,
        doc=r"""intrinsicWidth""",
    )
    intrinsicHeight = property(
        _pcammls.TY_CAMERA_CALIB_INFO_intrinsicHeight_get,
        _pcammls.TY_CAMERA_CALIB_INFO_intrinsicHeight_set,
        doc=r"""intrinsicHeight""",
    )
    intrinsic = property(
        _pcammls.TY_CAMERA_CALIB_INFO_intrinsic_get,
        _pcammls.TY_CAMERA_CALIB_INFO_intrinsic_set,
        doc=r"""intrinsic""",
    )
    extrinsic = property(
        _pcammls.TY_CAMERA_CALIB_INFO_extrinsic_get,
        _pcammls.TY_CAMERA_CALIB_INFO_extrinsic_set,
        doc=r"""extrinsic""",
    )
    distortion = property(
        _pcammls.TY_CAMERA_CALIB_INFO_distortion_get,
        _pcammls.TY_CAMERA_CALIB_INFO_distortion_set,
        doc=r"""distortion""",
    )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_CAMERA_CALIB_INFO_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_CAMERA_CALIB_INFO"""
        _pcammls.TY_CAMERA_CALIB_INFO_swiginit(
            self, _pcammls.new_TY_CAMERA_CALIB_INFO()
        )

    __swig_destroy__ = _pcammls.delete_TY_CAMERA_CALIB_INFO


# Register TY_CAMERA_CALIB_INFO in _pcammls:
_pcammls.TY_CAMERA_CALIB_INFO_swigregister(TY_CAMERA_CALIB_INFO)


class TY_TRIGGER_PARAM(object):
    r"""Proxy of C++ TY_TRIGGER_PARAM class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    mode = property(
        _pcammls.TY_TRIGGER_PARAM_mode_get,
        _pcammls.TY_TRIGGER_PARAM_mode_set,
        doc=r"""mode""",
    )
    fps = property(
        _pcammls.TY_TRIGGER_PARAM_fps_get,
        _pcammls.TY_TRIGGER_PARAM_fps_set,
        doc=r"""fps""",
    )
    rsvd = property(
        _pcammls.TY_TRIGGER_PARAM_rsvd_get,
        _pcammls.TY_TRIGGER_PARAM_rsvd_set,
        doc=r"""rsvd""",
    )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_TRIGGER_PARAM_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_TRIGGER_PARAM"""
        _pcammls.TY_TRIGGER_PARAM_swiginit(self, _pcammls.new_TY_TRIGGER_PARAM())

    __swig_destroy__ = _pcammls.delete_TY_TRIGGER_PARAM


# Register TY_TRIGGER_PARAM in _pcammls:
_pcammls.TY_TRIGGER_PARAM_swigregister(TY_TRIGGER_PARAM)


class TY_TRIGGER_PARAM_EX(object):
    r"""Proxy of C++ TY_TRIGGER_PARAM_EX class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    mode = property(
        _pcammls.TY_TRIGGER_PARAM_EX_mode_get,
        _pcammls.TY_TRIGGER_PARAM_EX_mode_set,
        doc=r"""mode""",
    )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_TRIGGER_PARAM_EX_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_TRIGGER_PARAM_EX"""
        _pcammls.TY_TRIGGER_PARAM_EX_swiginit(self, _pcammls.new_TY_TRIGGER_PARAM_EX())

    __swig_destroy__ = _pcammls.delete_TY_TRIGGER_PARAM_EX


# Register TY_TRIGGER_PARAM_EX in _pcammls:
_pcammls.TY_TRIGGER_PARAM_EX_swigregister(TY_TRIGGER_PARAM_EX)


class TY_TRIGGER_TIMER_LIST(object):
    r"""Proxy of C++ TY_TRIGGER_TIMER_LIST class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    start_time_us = property(
        _pcammls.TY_TRIGGER_TIMER_LIST_start_time_us_get,
        _pcammls.TY_TRIGGER_TIMER_LIST_start_time_us_set,
        doc=r"""start_time_us""",
    )
    offset_us_count = property(
        _pcammls.TY_TRIGGER_TIMER_LIST_offset_us_count_get,
        _pcammls.TY_TRIGGER_TIMER_LIST_offset_us_count_set,
        doc=r"""offset_us_count""",
    )
    offset_us_list = property(
        _pcammls.TY_TRIGGER_TIMER_LIST_offset_us_list_get,
        _pcammls.TY_TRIGGER_TIMER_LIST_offset_us_list_set,
        doc=r"""offset_us_list""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_TRIGGER_TIMER_LIST"""
        _pcammls.TY_TRIGGER_TIMER_LIST_swiginit(
            self, _pcammls.new_TY_TRIGGER_TIMER_LIST()
        )

    __swig_destroy__ = _pcammls.delete_TY_TRIGGER_TIMER_LIST


# Register TY_TRIGGER_TIMER_LIST in _pcammls:
_pcammls.TY_TRIGGER_TIMER_LIST_swigregister(TY_TRIGGER_TIMER_LIST)


class TY_TRIGGER_TIMER_PERIOD(object):
    r"""Proxy of C++ TY_TRIGGER_TIMER_PERIOD class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    start_time_us = property(
        _pcammls.TY_TRIGGER_TIMER_PERIOD_start_time_us_get,
        _pcammls.TY_TRIGGER_TIMER_PERIOD_start_time_us_set,
        doc=r"""start_time_us""",
    )
    trigger_count = property(
        _pcammls.TY_TRIGGER_TIMER_PERIOD_trigger_count_get,
        _pcammls.TY_TRIGGER_TIMER_PERIOD_trigger_count_set,
        doc=r"""trigger_count""",
    )
    period_us = property(
        _pcammls.TY_TRIGGER_TIMER_PERIOD_period_us_get,
        _pcammls.TY_TRIGGER_TIMER_PERIOD_period_us_set,
        doc=r"""period_us""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_TRIGGER_TIMER_PERIOD"""
        _pcammls.TY_TRIGGER_TIMER_PERIOD_swiginit(
            self, _pcammls.new_TY_TRIGGER_TIMER_PERIOD()
        )

    __swig_destroy__ = _pcammls.delete_TY_TRIGGER_TIMER_PERIOD


# Register TY_TRIGGER_TIMER_PERIOD in _pcammls:
_pcammls.TY_TRIGGER_TIMER_PERIOD_swigregister(TY_TRIGGER_TIMER_PERIOD)


class TY_AEC_ROI_PARAM(object):
    r"""Proxy of C++ TY_AEC_ROI_PARAM class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    x = property(
        _pcammls.TY_AEC_ROI_PARAM_x_get, _pcammls.TY_AEC_ROI_PARAM_x_set, doc=r"""x"""
    )
    y = property(
        _pcammls.TY_AEC_ROI_PARAM_y_get, _pcammls.TY_AEC_ROI_PARAM_y_set, doc=r"""y"""
    )
    w = property(
        _pcammls.TY_AEC_ROI_PARAM_w_get, _pcammls.TY_AEC_ROI_PARAM_w_set, doc=r"""w"""
    )
    h = property(
        _pcammls.TY_AEC_ROI_PARAM_h_get, _pcammls.TY_AEC_ROI_PARAM_h_set, doc=r"""h"""
    )

    def __init__(self):
        r"""__init__(self) -> TY_AEC_ROI_PARAM"""
        _pcammls.TY_AEC_ROI_PARAM_swiginit(self, _pcammls.new_TY_AEC_ROI_PARAM())

    __swig_destroy__ = _pcammls.delete_TY_AEC_ROI_PARAM


# Register TY_AEC_ROI_PARAM in _pcammls:
_pcammls.TY_AEC_ROI_PARAM_swigregister(TY_AEC_ROI_PARAM)

TY_PATTERN_SINE_TYPE = _pcammls.TY_PATTERN_SINE_TYPE

TY_PATTERN_GRAY_TYPE = _pcammls.TY_PATTERN_GRAY_TYPE

TY_PATTERN_BIN_TYPE = _pcammls.TY_PATTERN_BIN_TYPE

TY_PATTERN_EMPTY_TYPE = _pcammls.TY_PATTERN_EMPTY_TYPE


class TY_PHC_GROUP_ATTR(object):
    r"""Proxy of C++ TY_PHC_GROUP_ATTR class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    offset = property(
        _pcammls.TY_PHC_GROUP_ATTR_offset_get,
        _pcammls.TY_PHC_GROUP_ATTR_offset_set,
        doc=r"""offset""",
    )
    size = property(
        _pcammls.TY_PHC_GROUP_ATTR_size_get,
        _pcammls.TY_PHC_GROUP_ATTR_size_set,
        doc=r"""size""",
    )
    phc_attr = property(
        _pcammls.TY_PHC_GROUP_ATTR_phc_attr_get,
        _pcammls.TY_PHC_GROUP_ATTR_phc_attr_set,
        doc=r"""phc_attr""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_PHC_GROUP_ATTR"""
        _pcammls.TY_PHC_GROUP_ATTR_swiginit(self, _pcammls.new_TY_PHC_GROUP_ATTR())

    __swig_destroy__ = _pcammls.delete_TY_PHC_GROUP_ATTR


# Register TY_PHC_GROUP_ATTR in _pcammls:
_pcammls.TY_PHC_GROUP_ATTR_swigregister(TY_PHC_GROUP_ATTR)


class pattern_sine_param(object):
    r"""Proxy of C++ pattern_sine_param class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    phase_num = property(
        _pcammls.pattern_sine_param_phase_num_get,
        _pcammls.pattern_sine_param_phase_num_set,
        doc=r"""phase_num""",
    )
    period = property(
        _pcammls.pattern_sine_param_period_get,
        _pcammls.pattern_sine_param_period_set,
        doc=r"""period""",
    )

    def __init__(self):
        r"""__init__(self) -> pattern_sine_param"""
        _pcammls.pattern_sine_param_swiginit(self, _pcammls.new_pattern_sine_param())

    __swig_destroy__ = _pcammls.delete_pattern_sine_param


# Register pattern_sine_param in _pcammls:
_pcammls.pattern_sine_param_swigregister(pattern_sine_param)


class pattern_gray_param(object):
    r"""Proxy of C++ pattern_gray_param class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    phase_num = property(
        _pcammls.pattern_gray_param_phase_num_get,
        _pcammls.pattern_gray_param_phase_num_set,
        doc=r"""phase_num""",
    )
    param1 = property(
        _pcammls.pattern_gray_param_param1_get,
        _pcammls.pattern_gray_param_param1_set,
        doc=r"""param1""",
    )
    param2 = property(
        _pcammls.pattern_gray_param_param2_get,
        _pcammls.pattern_gray_param_param2_set,
        doc=r"""param2""",
    )
    param3 = property(
        _pcammls.pattern_gray_param_param3_get,
        _pcammls.pattern_gray_param_param3_set,
        doc=r"""param3""",
    )

    def __init__(self):
        r"""__init__(self) -> pattern_gray_param"""
        _pcammls.pattern_gray_param_swiginit(self, _pcammls.new_pattern_gray_param())

    __swig_destroy__ = _pcammls.delete_pattern_gray_param


# Register pattern_gray_param in _pcammls:
_pcammls.pattern_gray_param_swigregister(pattern_gray_param)


class pattern_bin_param(object):
    r"""Proxy of C++ pattern_bin_param class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    offset = property(
        _pcammls.pattern_bin_param_offset_get,
        _pcammls.pattern_bin_param_offset_set,
        doc=r"""offset""",
    )
    data = property(
        _pcammls.pattern_bin_param_data_get,
        _pcammls.pattern_bin_param_data_set,
        doc=r"""data""",
    )

    def __init__(self):
        r"""__init__(self) -> pattern_bin_param"""
        _pcammls.pattern_bin_param_swiginit(self, _pcammls.new_pattern_bin_param())

    __swig_destroy__ = _pcammls.delete_pattern_bin_param


# Register pattern_bin_param in _pcammls:
_pcammls.pattern_bin_param_swigregister(pattern_bin_param)


class TY_LASER_PATTERN_PARAM(object):
    r"""Proxy of C++ TY_LASER_PATTERN_PARAM class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    img_index = property(
        _pcammls.TY_LASER_PATTERN_PARAM_img_index_get,
        _pcammls.TY_LASER_PATTERN_PARAM_img_index_set,
        doc=r"""img_index""",
    )
    type = property(
        _pcammls.TY_LASER_PATTERN_PARAM_type_get,
        _pcammls.TY_LASER_PATTERN_PARAM_type_set,
        doc=r"""type""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_LASER_PATTERN_PARAM"""
        _pcammls.TY_LASER_PATTERN_PARAM_swiginit(
            self, _pcammls.new_TY_LASER_PATTERN_PARAM()
        )

    __swig_destroy__ = _pcammls.delete_TY_LASER_PATTERN_PARAM


# Register TY_LASER_PATTERN_PARAM in _pcammls:
_pcammls.TY_LASER_PATTERN_PARAM_swigregister(TY_LASER_PATTERN_PARAM)

TY_NORMAL_PHASE_TYPE = _pcammls.TY_NORMAL_PHASE_TYPE

TY_REFER_PHASE_TYPE = _pcammls.TY_REFER_PHASE_TYPE


class TY_CAMERA_STATISTICS(object):
    r"""Proxy of C++ TY_CAMERA_STATISTICS class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    packetReceived = property(
        _pcammls.TY_CAMERA_STATISTICS_packetReceived_get,
        _pcammls.TY_CAMERA_STATISTICS_packetReceived_set,
        doc=r"""packetReceived""",
    )
    packetLost = property(
        _pcammls.TY_CAMERA_STATISTICS_packetLost_get,
        _pcammls.TY_CAMERA_STATISTICS_packetLost_set,
        doc=r"""packetLost""",
    )
    imageOutputed = property(
        _pcammls.TY_CAMERA_STATISTICS_imageOutputed_get,
        _pcammls.TY_CAMERA_STATISTICS_imageOutputed_set,
        doc=r"""imageOutputed""",
    )
    imageDropped = property(
        _pcammls.TY_CAMERA_STATISTICS_imageDropped_get,
        _pcammls.TY_CAMERA_STATISTICS_imageDropped_set,
        doc=r"""imageDropped""",
    )
    rsvd = property(
        _pcammls.TY_CAMERA_STATISTICS_rsvd_get,
        _pcammls.TY_CAMERA_STATISTICS_rsvd_set,
        doc=r"""rsvd""",
    )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_CAMERA_STATISTICS_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_CAMERA_STATISTICS"""
        _pcammls.TY_CAMERA_STATISTICS_swiginit(
            self, _pcammls.new_TY_CAMERA_STATISTICS()
        )

    __swig_destroy__ = _pcammls.delete_TY_CAMERA_STATISTICS


# Register TY_CAMERA_STATISTICS in _pcammls:
_pcammls.TY_CAMERA_STATISTICS_swigregister(TY_CAMERA_STATISTICS)


class TY_IMU_DATA(object):
    r"""Proxy of C++ TY_IMU_DATA class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    timestamp = property(
        _pcammls.TY_IMU_DATA_timestamp_get,
        _pcammls.TY_IMU_DATA_timestamp_set,
        doc=r"""timestamp""",
    )
    acc_x = property(
        _pcammls.TY_IMU_DATA_acc_x_get, _pcammls.TY_IMU_DATA_acc_x_set, doc=r"""acc_x"""
    )
    acc_y = property(
        _pcammls.TY_IMU_DATA_acc_y_get, _pcammls.TY_IMU_DATA_acc_y_set, doc=r"""acc_y"""
    )
    acc_z = property(
        _pcammls.TY_IMU_DATA_acc_z_get, _pcammls.TY_IMU_DATA_acc_z_set, doc=r"""acc_z"""
    )
    gyro_x = property(
        _pcammls.TY_IMU_DATA_gyro_x_get,
        _pcammls.TY_IMU_DATA_gyro_x_set,
        doc=r"""gyro_x""",
    )
    gyro_y = property(
        _pcammls.TY_IMU_DATA_gyro_y_get,
        _pcammls.TY_IMU_DATA_gyro_y_set,
        doc=r"""gyro_y""",
    )
    gyro_z = property(
        _pcammls.TY_IMU_DATA_gyro_z_get,
        _pcammls.TY_IMU_DATA_gyro_z_set,
        doc=r"""gyro_z""",
    )
    temperature = property(
        _pcammls.TY_IMU_DATA_temperature_get,
        _pcammls.TY_IMU_DATA_temperature_set,
        doc=r"""temperature""",
    )
    reserved = property(
        _pcammls.TY_IMU_DATA_reserved_get,
        _pcammls.TY_IMU_DATA_reserved_set,
        doc=r"""reserved""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_IMU_DATA"""
        _pcammls.TY_IMU_DATA_swiginit(self, _pcammls.new_TY_IMU_DATA())

    __swig_destroy__ = _pcammls.delete_TY_IMU_DATA


# Register TY_IMU_DATA in _pcammls:
_pcammls.TY_IMU_DATA_swigregister(TY_IMU_DATA)


class TY_ACC_BIAS(object):
    r"""Proxy of C++ TY_ACC_BIAS class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    data = property(
        _pcammls.TY_ACC_BIAS_data_get, _pcammls.TY_ACC_BIAS_data_set, doc=r"""data"""
    )

    def __init__(self):
        r"""__init__(self) -> TY_ACC_BIAS"""
        _pcammls.TY_ACC_BIAS_swiginit(self, _pcammls.new_TY_ACC_BIAS())

    __swig_destroy__ = _pcammls.delete_TY_ACC_BIAS


# Register TY_ACC_BIAS in _pcammls:
_pcammls.TY_ACC_BIAS_swigregister(TY_ACC_BIAS)


class TY_ACC_MISALIGNMENT(object):
    r"""Proxy of C++ TY_ACC_MISALIGNMENT class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    data = property(
        _pcammls.TY_ACC_MISALIGNMENT_data_get,
        _pcammls.TY_ACC_MISALIGNMENT_data_set,
        doc=r"""data""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_ACC_MISALIGNMENT"""
        _pcammls.TY_ACC_MISALIGNMENT_swiginit(self, _pcammls.new_TY_ACC_MISALIGNMENT())

    __swig_destroy__ = _pcammls.delete_TY_ACC_MISALIGNMENT


# Register TY_ACC_MISALIGNMENT in _pcammls:
_pcammls.TY_ACC_MISALIGNMENT_swigregister(TY_ACC_MISALIGNMENT)


class TY_ACC_SCALE(object):
    r"""Proxy of C++ TY_ACC_SCALE class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    data = property(
        _pcammls.TY_ACC_SCALE_data_get, _pcammls.TY_ACC_SCALE_data_set, doc=r"""data"""
    )

    def __init__(self):
        r"""__init__(self) -> TY_ACC_SCALE"""
        _pcammls.TY_ACC_SCALE_swiginit(self, _pcammls.new_TY_ACC_SCALE())

    __swig_destroy__ = _pcammls.delete_TY_ACC_SCALE


# Register TY_ACC_SCALE in _pcammls:
_pcammls.TY_ACC_SCALE_swigregister(TY_ACC_SCALE)


class TY_GYRO_BIAS(object):
    r"""Proxy of C++ TY_GYRO_BIAS class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    data = property(
        _pcammls.TY_GYRO_BIAS_data_get, _pcammls.TY_GYRO_BIAS_data_set, doc=r"""data"""
    )

    def __init__(self):
        r"""__init__(self) -> TY_GYRO_BIAS"""
        _pcammls.TY_GYRO_BIAS_swiginit(self, _pcammls.new_TY_GYRO_BIAS())

    __swig_destroy__ = _pcammls.delete_TY_GYRO_BIAS


# Register TY_GYRO_BIAS in _pcammls:
_pcammls.TY_GYRO_BIAS_swigregister(TY_GYRO_BIAS)


class TY_GYRO_MISALIGNMENT(object):
    r"""Proxy of C++ TY_GYRO_MISALIGNMENT class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    data = property(
        _pcammls.TY_GYRO_MISALIGNMENT_data_get,
        _pcammls.TY_GYRO_MISALIGNMENT_data_set,
        doc=r"""data""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_GYRO_MISALIGNMENT"""
        _pcammls.TY_GYRO_MISALIGNMENT_swiginit(
            self, _pcammls.new_TY_GYRO_MISALIGNMENT()
        )

    __swig_destroy__ = _pcammls.delete_TY_GYRO_MISALIGNMENT


# Register TY_GYRO_MISALIGNMENT in _pcammls:
_pcammls.TY_GYRO_MISALIGNMENT_swigregister(TY_GYRO_MISALIGNMENT)


class TY_GYRO_SCALE(object):
    r"""Proxy of C++ TY_GYRO_SCALE class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    data = property(
        _pcammls.TY_GYRO_SCALE_data_get,
        _pcammls.TY_GYRO_SCALE_data_set,
        doc=r"""data""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_GYRO_SCALE"""
        _pcammls.TY_GYRO_SCALE_swiginit(self, _pcammls.new_TY_GYRO_SCALE())

    __swig_destroy__ = _pcammls.delete_TY_GYRO_SCALE


# Register TY_GYRO_SCALE in _pcammls:
_pcammls.TY_GYRO_SCALE_swigregister(TY_GYRO_SCALE)


class TY_CAMERA_TO_IMU(object):
    r"""Proxy of C++ TY_CAMERA_TO_IMU class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    data = property(
        _pcammls.TY_CAMERA_TO_IMU_data_get,
        _pcammls.TY_CAMERA_TO_IMU_data_set,
        doc=r"""data""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_CAMERA_TO_IMU"""
        _pcammls.TY_CAMERA_TO_IMU_swiginit(self, _pcammls.new_TY_CAMERA_TO_IMU())

    __swig_destroy__ = _pcammls.delete_TY_CAMERA_TO_IMU


# Register TY_CAMERA_TO_IMU in _pcammls:
_pcammls.TY_CAMERA_TO_IMU_swigregister(TY_CAMERA_TO_IMU)


class TY_TOF_FREQ(object):
    r"""Proxy of C++ TY_TOF_FREQ class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    freq1 = property(
        _pcammls.TY_TOF_FREQ_freq1_get, _pcammls.TY_TOF_FREQ_freq1_set, doc=r"""freq1"""
    )
    freq2 = property(
        _pcammls.TY_TOF_FREQ_freq2_get, _pcammls.TY_TOF_FREQ_freq2_set, doc=r"""freq2"""
    )

    def __init__(self):
        r"""__init__(self) -> TY_TOF_FREQ"""
        _pcammls.TY_TOF_FREQ_swiginit(self, _pcammls.new_TY_TOF_FREQ())

    __swig_destroy__ = _pcammls.delete_TY_TOF_FREQ


# Register TY_TOF_FREQ in _pcammls:
_pcammls.TY_TOF_FREQ_swigregister(TY_TOF_FREQ)

TY_IMU_FPS_100HZ = _pcammls.TY_IMU_FPS_100HZ

TY_IMU_FPS_200HZ = _pcammls.TY_IMU_FPS_200HZ

TY_IMU_FPS_400HZ = _pcammls.TY_IMU_FPS_400HZ


class TY_LASER_PARAM(object):
    r"""Proxy of C++ TY_LASER_PARAM class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    idx = property(
        _pcammls.TY_LASER_PARAM_idx_get, _pcammls.TY_LASER_PARAM_idx_set, doc=r"""idx"""
    )
    en = property(
        _pcammls.TY_LASER_PARAM_en_get, _pcammls.TY_LASER_PARAM_en_set, doc=r"""en"""
    )
    power = property(
        _pcammls.TY_LASER_PARAM_power_get,
        _pcammls.TY_LASER_PARAM_power_set,
        doc=r"""power""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_LASER_PARAM"""
        _pcammls.TY_LASER_PARAM_swiginit(self, _pcammls.new_TY_LASER_PARAM())

    __swig_destroy__ = _pcammls.delete_TY_LASER_PARAM


# Register TY_LASER_PARAM in _pcammls:
_pcammls.TY_LASER_PARAM_swigregister(TY_LASER_PARAM)


class TY_IMAGE_DATA(object):
    r"""Proxy of C++ TY_IMAGE_DATA class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    timestamp = property(
        _pcammls.TY_IMAGE_DATA_timestamp_get,
        _pcammls.TY_IMAGE_DATA_timestamp_set,
        doc=r"""timestamp""",
    )
    imageIndex = property(
        _pcammls.TY_IMAGE_DATA_imageIndex_get,
        _pcammls.TY_IMAGE_DATA_imageIndex_set,
        doc=r"""imageIndex""",
    )
    status = property(
        _pcammls.TY_IMAGE_DATA_status_get,
        _pcammls.TY_IMAGE_DATA_status_set,
        doc=r"""status""",
    )
    componentID = property(
        _pcammls.TY_IMAGE_DATA_componentID_get,
        _pcammls.TY_IMAGE_DATA_componentID_set,
        doc=r"""componentID""",
    )
    size = property(
        _pcammls.TY_IMAGE_DATA_size_get,
        _pcammls.TY_IMAGE_DATA_size_set,
        doc=r"""size""",
    )
    buffer = property(
        _pcammls.TY_IMAGE_DATA_buffer_get,
        _pcammls.TY_IMAGE_DATA_buffer_set,
        doc=r"""buffer""",
    )
    width = property(
        _pcammls.TY_IMAGE_DATA_width_get,
        _pcammls.TY_IMAGE_DATA_width_set,
        doc=r"""width""",
    )
    height = property(
        _pcammls.TY_IMAGE_DATA_height_get,
        _pcammls.TY_IMAGE_DATA_height_set,
        doc=r"""height""",
    )
    pixelFormat = property(
        _pcammls.TY_IMAGE_DATA_pixelFormat_get,
        _pcammls.TY_IMAGE_DATA_pixelFormat_set,
        doc=r"""pixelFormat""",
    )
    reserved = property(
        _pcammls.TY_IMAGE_DATA_reserved_get,
        _pcammls.TY_IMAGE_DATA_reserved_set,
        doc=r"""reserved""",
    )

    __U8CX = [TY_PIXEL_FORMAT_MJPG, TY_PIXEL_FORMAT_JPEG]
    __U8C1 = [TY_PIXEL_FORMAT_MONO, TY_PIXEL_FORMAT_BAYER8GB, TY_PIXEL_FORMAT_BAYER8BG]
    __U8C2 = [TY_PIXEL_FORMAT_YVYU, TY_PIXEL_FORMAT_YUYV]
    __U8C3 = [TY_PIXEL_FORMAT_RGB, TY_PIXEL_FORMAT_BGR]
    __U16C1 = [TY_PIXEL_FORMAT_DEPTH16, TY_PIXEL_FORMAT_MONO16]

    def as_nparray(self):
        """
        convert image to numpy array
        *** YOU SHOULD COPY DATA TO YOUR OWN ARRAY BEFORE invoking Enqueuebuffer  ***
        """
        if self.buffer == None or self.width <= 0 or self.height <= 0:
            return None
        pformat = self.pixelFormat
        if pformat in self.__U8CX:
            sz = self.size
            return uint8_t_ARRAY.ptr_as_nparray1d(self.buffer, self.size)
        if pformat in self.__U8C1:
            sz = self.height * self.width
            return uint8_t_ARRAY.ptr_as_nparray2d(self.buffer, self.height, self.width)
        elif pformat in self.__U8C2:
            return uint8_t_ARRAY.ptr_as_nparray3d(
                self.buffer, self.height, self.width, 2
            )
        elif pformat in self.__U8C3:
            sz = self.height * self.width * 3
            return uint8_t_ARRAY.ptr_as_nparray3d(
                self.buffer, self.height, self.width, 3
            )
        elif pformat in self.__U16C1:
            sz = self.height * self.width
            return uint16_t_ARRAY.ptr_as_nparray3d(
                self.buffer, self.height, self.width, 1
            )
        else:
            raise Exception("not supported format {}".format(pformat))
        return None

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_IMAGE_DATA_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_IMAGE_DATA"""
        _pcammls.TY_IMAGE_DATA_swiginit(self, _pcammls.new_TY_IMAGE_DATA())

    __swig_destroy__ = _pcammls.delete_TY_IMAGE_DATA


# Register TY_IMAGE_DATA in _pcammls:
_pcammls.TY_IMAGE_DATA_swigregister(TY_IMAGE_DATA)


class TY_FRAME_DATA(object):
    r"""Proxy of C++ TY_FRAME_DATA class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    userBuffer = property(
        _pcammls.TY_FRAME_DATA_userBuffer_get,
        _pcammls.TY_FRAME_DATA_userBuffer_set,
        doc=r"""userBuffer""",
    )
    bufferSize = property(
        _pcammls.TY_FRAME_DATA_bufferSize_get,
        _pcammls.TY_FRAME_DATA_bufferSize_set,
        doc=r"""bufferSize""",
    )
    validCount = property(
        _pcammls.TY_FRAME_DATA_validCount_get,
        _pcammls.TY_FRAME_DATA_validCount_set,
        doc=r"""validCount""",
    )
    reserved = property(
        _pcammls.TY_FRAME_DATA_reserved_get,
        _pcammls.TY_FRAME_DATA_reserved_set,
        doc=r"""reserved""",
    )
    image = property(
        _pcammls.TY_FRAME_DATA_image_get,
        _pcammls.TY_FRAME_DATA_image_set,
        doc=r"""image""",
    )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_FRAME_DATA_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_FRAME_DATA"""
        _pcammls.TY_FRAME_DATA_swiginit(self, _pcammls.new_TY_FRAME_DATA())

    __swig_destroy__ = _pcammls.delete_TY_FRAME_DATA


# Register TY_FRAME_DATA in _pcammls:
_pcammls.TY_FRAME_DATA_swigregister(TY_FRAME_DATA)


class TY_EVENT_INFO(object):
    r"""Proxy of C++ TY_EVENT_INFO class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    eventId = property(
        _pcammls.TY_EVENT_INFO_eventId_get,
        _pcammls.TY_EVENT_INFO_eventId_set,
        doc=r"""eventId""",
    )
    message = property(
        _pcammls.TY_EVENT_INFO_message_get,
        _pcammls.TY_EVENT_INFO_message_set,
        doc=r"""message""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_EVENT_INFO"""
        _pcammls.TY_EVENT_INFO_swiginit(self, _pcammls.new_TY_EVENT_INFO())

    __swig_destroy__ = _pcammls.delete_TY_EVENT_INFO


# Register TY_EVENT_INFO in _pcammls:
_pcammls.TY_EVENT_INFO_swigregister(TY_EVENT_INFO)


class TY_DO_WORKMODE(object):
    r"""Proxy of C++ TY_DO_WORKMODE class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    mode = property(
        _pcammls.TY_DO_WORKMODE_mode_get,
        _pcammls.TY_DO_WORKMODE_mode_set,
        doc=r"""mode""",
    )
    volt = property(
        _pcammls.TY_DO_WORKMODE_volt_get,
        _pcammls.TY_DO_WORKMODE_volt_set,
        doc=r"""volt""",
    )
    freq = property(
        _pcammls.TY_DO_WORKMODE_freq_get,
        _pcammls.TY_DO_WORKMODE_freq_set,
        doc=r"""freq""",
    )
    duty = property(
        _pcammls.TY_DO_WORKMODE_duty_get,
        _pcammls.TY_DO_WORKMODE_duty_set,
        doc=r"""duty""",
    )
    mode_supported = property(
        _pcammls.TY_DO_WORKMODE_mode_supported_get,
        _pcammls.TY_DO_WORKMODE_mode_supported_set,
        doc=r"""mode_supported""",
    )
    volt_supported = property(
        _pcammls.TY_DO_WORKMODE_volt_supported_get,
        _pcammls.TY_DO_WORKMODE_volt_supported_set,
        doc=r"""volt_supported""",
    )
    reserved = property(
        _pcammls.TY_DO_WORKMODE_reserved_get,
        _pcammls.TY_DO_WORKMODE_reserved_set,
        doc=r"""reserved""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_DO_WORKMODE"""
        _pcammls.TY_DO_WORKMODE_swiginit(self, _pcammls.new_TY_DO_WORKMODE())

    __swig_destroy__ = _pcammls.delete_TY_DO_WORKMODE


# Register TY_DO_WORKMODE in _pcammls:
_pcammls.TY_DO_WORKMODE_swigregister(TY_DO_WORKMODE)


class TY_DI_WORKMODE(object):
    r"""Proxy of C++ TY_DI_WORKMODE class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    mode = property(
        _pcammls.TY_DI_WORKMODE_mode_get,
        _pcammls.TY_DI_WORKMODE_mode_set,
        doc=r"""mode""",
    )
    int_act = property(
        _pcammls.TY_DI_WORKMODE_int_act_get,
        _pcammls.TY_DI_WORKMODE_int_act_set,
        doc=r"""int_act""",
    )
    mode_supported = property(
        _pcammls.TY_DI_WORKMODE_mode_supported_get,
        _pcammls.TY_DI_WORKMODE_mode_supported_set,
        doc=r"""mode_supported""",
    )
    int_act_supported = property(
        _pcammls.TY_DI_WORKMODE_int_act_supported_get,
        _pcammls.TY_DI_WORKMODE_int_act_supported_set,
        doc=r"""int_act_supported""",
    )
    status = property(
        _pcammls.TY_DI_WORKMODE_status_get,
        _pcammls.TY_DI_WORKMODE_status_set,
        doc=r"""status""",
    )
    reserved = property(
        _pcammls.TY_DI_WORKMODE_reserved_get,
        _pcammls.TY_DI_WORKMODE_reserved_set,
        doc=r"""reserved""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_DI_WORKMODE"""
        _pcammls.TY_DI_WORKMODE_swiginit(self, _pcammls.new_TY_DI_WORKMODE())

    __swig_destroy__ = _pcammls.delete_TY_DI_WORKMODE


# Register TY_DI_WORKMODE in _pcammls:
_pcammls.TY_DI_WORKMODE_swigregister(TY_DI_WORKMODE)


class TY_TEMP_DATA(object):
    r"""Proxy of C++ TY_TEMP_DATA class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    id = property(
        _pcammls.TY_TEMP_DATA_id_get, _pcammls.TY_TEMP_DATA_id_set, doc=r"""id"""
    )
    name = property(
        _pcammls.TY_TEMP_DATA_name_get, _pcammls.TY_TEMP_DATA_name_set, doc=r"""name"""
    )
    temp = property(
        _pcammls.TY_TEMP_DATA_temp_get, _pcammls.TY_TEMP_DATA_temp_set, doc=r"""temp"""
    )
    desc = property(
        _pcammls.TY_TEMP_DATA_desc_get, _pcammls.TY_TEMP_DATA_desc_set, doc=r"""desc"""
    )

    def __init__(self):
        r"""__init__(self) -> TY_TEMP_DATA"""
        _pcammls.TY_TEMP_DATA_swiginit(self, _pcammls.new_TY_TEMP_DATA())

    __swig_destroy__ = _pcammls.delete_TY_TEMP_DATA


# Register TY_TEMP_DATA in _pcammls:
_pcammls.TY_TEMP_DATA_swigregister(TY_TEMP_DATA)


class TY_CAMERA_ROTATION(object):
    r"""Proxy of C++ TY_CAMERA_ROTATION class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    data = property(
        _pcammls.TY_CAMERA_ROTATION_data_get,
        _pcammls.TY_CAMERA_ROTATION_data_set,
        doc=r"""data""",
    )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_CAMERA_ROTATION_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_CAMERA_ROTATION"""
        _pcammls.TY_CAMERA_ROTATION_swiginit(self, _pcammls.new_TY_CAMERA_ROTATION())

    __swig_destroy__ = _pcammls.delete_TY_CAMERA_ROTATION


# Register TY_CAMERA_ROTATION in _pcammls:
_pcammls.TY_CAMERA_ROTATION_swigregister(TY_CAMERA_ROTATION)


def TYIsNetworkInterface(interfaceType):
    r"""
    TYIsNetworkInterface(interfaceType) -> bool

    Parameters
    ----------
    interfaceType: int32_t

    """
    return _pcammls.TYIsNetworkInterface(interfaceType)


def TYIntToIPv4(addr, out):
    r"""
    TYIntToIPv4(addr, out)

    Parameters
    ----------
    addr: uint32_t
    out: uint8_t [4]

    """
    return _pcammls.TYIntToIPv4(addr, out)


def TYIPv4ToInt(ip):
    r"""
    TYIPv4ToInt(ip) -> uint32_t

    Parameters
    ----------
    ip: uint8_t [4]

    """
    return _pcammls.TYIPv4ToInt(ip)


def TYInitImageData(size, buffer, width, height):
    r"""
    TYInitImageData(size, buffer, width, height) -> TY_IMAGE_DATA

    Parameters
    ----------
    size: size_t
    buffer: void *
    width: size_t
    height: size_t

    """
    return _pcammls.TYInitImageData(size, buffer, width, height)


def TYFeatureType(id):
    r"""
    TYFeatureType(id) -> TY_FEATURE_TYPE

    Parameters
    ----------
    id: TY_FEATURE_ID

    """
    return _pcammls.TYFeatureType(id)


def TYPixelSize(imageMode):
    r"""
    TYPixelSize(imageMode) -> int32_t

    Parameters
    ----------
    imageMode: TY_IMAGE_MODE

    """
    return _pcammls.TYPixelSize(imageMode)


def TYBitsPerPixel(imageMode):
    r"""
    TYBitsPerPixel(imageMode) -> int32_t

    Parameters
    ----------
    imageMode: TY_IMAGE_MODE

    """
    return _pcammls.TYBitsPerPixel(imageMode)


def TYPixelLineSize(width, imageMode):
    r"""
    TYPixelLineSize(width, imageMode) -> int32_t

    Parameters
    ----------
    width: int
    imageMode: TY_IMAGE_MODE

    """
    return _pcammls.TYPixelLineSize(width, imageMode)


def TYImageMode(pix, res):
    r"""
    TYImageMode(pix, res) -> TY_IMAGE_MODE

    Parameters
    ----------
    pix: TY_PIXEL_FORMAT
    res: TY_RESOLUTION_MODE

    """
    return _pcammls.TYImageMode(pix, res)


def TYResolutionMode2(width, height):
    r"""
    TYResolutionMode2(width, height) -> TY_RESOLUTION_MODE

    Parameters
    ----------
    width: int
    height: int

    """
    return _pcammls.TYResolutionMode2(width, height)


def TYImageMode2(pix, width, height):
    r"""
    TYImageMode2(pix, width, height) -> TY_IMAGE_MODE

    Parameters
    ----------
    pix: TY_PIXEL_FORMAT
    width: int
    height: int

    """
    return _pcammls.TYImageMode2(pix, width, height)


def TYPixelFormat(imageMode):
    r"""
    TYPixelFormat(imageMode) -> TY_PIXEL_FORMAT

    Parameters
    ----------
    imageMode: TY_IMAGE_MODE

    """
    return _pcammls.TYPixelFormat(imageMode)


def TYResolutionMode(imageMode):
    r"""
    TYResolutionMode(imageMode) -> TY_RESOLUTION_MODE

    Parameters
    ----------
    imageMode: TY_IMAGE_MODE

    """
    return _pcammls.TYResolutionMode(imageMode)


def TYImageWidth(imageMode):
    r"""
    TYImageWidth(imageMode) -> int32_t

    Parameters
    ----------
    imageMode: TY_IMAGE_MODE

    """
    return _pcammls.TYImageWidth(imageMode)


def TYImageHeight(imageMode):
    r"""
    TYImageHeight(imageMode) -> int32_t

    Parameters
    ----------
    imageMode: TY_IMAGE_MODE

    """
    return _pcammls.TYImageHeight(imageMode)


def _TYInitLib():
    r"""_TYInitLib() -> TY_STATUS"""
    return _pcammls._TYInitLib()


def TYLibVersion(version):
    r"""
    TYLibVersion(version) -> TY_STATUS

    Parameters
    ----------
    version: TY_VERSION_INFO *

    """
    return _pcammls.TYLibVersion(version)


def TYInitLib():
    r"""TYInitLib() -> TY_STATUS"""
    return _pcammls.TYInitLib()


def TYErrorString(errorID):
    r"""
    TYErrorString(errorID) -> char const *

    Parameters
    ----------
    errorID: TY_STATUS

    """
    return _pcammls.TYErrorString(errorID)


def TYDeinitLib():
    r"""TYDeinitLib() -> TY_STATUS"""
    return _pcammls.TYDeinitLib()


def TYSetLogLevel(lvl):
    r"""
    TYSetLogLevel(lvl) -> TY_STATUS

    Parameters
    ----------
    lvl: TY_LOG_LEVEL

    """
    return _pcammls.TYSetLogLevel(lvl)


def TYSetLogPrefix(prefix):
    r"""
    TYSetLogPrefix(prefix) -> TY_STATUS

    Parameters
    ----------
    prefix: char const *

    """
    return _pcammls.TYSetLogPrefix(prefix)


def TYAppendLogToFile(filePath, lvl):
    r"""
    TYAppendLogToFile(filePath, lvl) -> TY_STATUS

    Parameters
    ----------
    filePath: char const *
    lvl: TY_LOG_LEVEL

    """
    return _pcammls.TYAppendLogToFile(filePath, lvl)


def TYRemoveLogFile(filePath):
    r"""
    TYRemoveLogFile(filePath) -> TY_STATUS

    Parameters
    ----------
    filePath: char const *

    """
    return _pcammls.TYRemoveLogFile(filePath)


def TYAppendLogToServer(protocol, ip, port, lvl):
    r"""
    TYAppendLogToServer(protocol, ip, port, lvl) -> TY_STATUS

    Parameters
    ----------
    protocol: char const *
    ip: char const *
    port: uint16_t
    lvl: TY_LOG_LEVEL

    """
    return _pcammls.TYAppendLogToServer(protocol, ip, port, lvl)


def TYRemoveLogServer(protocol, ip, port):
    r"""
    TYRemoveLogServer(protocol, ip, port) -> TY_STATUS

    Parameters
    ----------
    protocol: char const *
    ip: char const *
    port: uint16_t

    """
    return _pcammls.TYRemoveLogServer(protocol, ip, port)


def TYUpdateInterfaceList():
    r"""TYUpdateInterfaceList() -> TY_STATUS"""
    return _pcammls.TYUpdateInterfaceList()


def TYGetInterfaceNumber():
    r"""TYGetInterfaceNumber() -> TY_STATUS"""
    return _pcammls.TYGetInterfaceNumber()


def TYGetInterfaceList(pIfaceInfos, bufferCount):
    r"""
    TYGetInterfaceList(pIfaceInfos, bufferCount) -> TY_STATUS

    Parameters
    ----------
    pIfaceInfos: TY_INTERFACE_INFO *
    bufferCount: uint32_t

    """
    return _pcammls.TYGetInterfaceList(pIfaceInfos, bufferCount)


def TYHasInterface(ifaceID):
    r"""
    TYHasInterface(ifaceID) -> TY_STATUS

    Parameters
    ----------
    ifaceID: char const *

    """
    return _pcammls.TYHasInterface(ifaceID)


def TYOpenInterface(ifaceID):
    r"""
    TYOpenInterface(ifaceID) -> TY_STATUS

    Parameters
    ----------
    ifaceID: char const *

    """
    return _pcammls.TYOpenInterface(ifaceID)


def TYCloseInterface(ifaceHandle):
    r"""
    TYCloseInterface(ifaceHandle) -> TY_STATUS

    Parameters
    ----------
    ifaceHandle: TY_INTERFACE_HANDLE

    """
    return _pcammls.TYCloseInterface(ifaceHandle)


def TYUpdateDeviceList(ifaceHandle):
    r"""
    TYUpdateDeviceList(ifaceHandle) -> TY_STATUS

    Parameters
    ----------
    ifaceHandle: TY_INTERFACE_HANDLE

    """
    return _pcammls.TYUpdateDeviceList(ifaceHandle)


def TYUpdateAllDeviceList():
    r"""TYUpdateAllDeviceList() -> TY_STATUS"""
    return _pcammls.TYUpdateAllDeviceList()


def TYGetDeviceNumber(ifaceHandle):
    r"""
    TYGetDeviceNumber(ifaceHandle) -> TY_STATUS

    Parameters
    ----------
    ifaceHandle: TY_INTERFACE_HANDLE

    """
    return _pcammls.TYGetDeviceNumber(ifaceHandle)


def TYGetDeviceList(ifaceHandle, deviceInfos, bufferCount):
    r"""
    TYGetDeviceList(ifaceHandle, deviceInfos, bufferCount) -> TY_STATUS

    Parameters
    ----------
    ifaceHandle: TY_INTERFACE_HANDLE
    deviceInfos: TY_DEVICE_BASE_INFO *
    bufferCount: uint32_t

    """
    return _pcammls.TYGetDeviceList(ifaceHandle, deviceInfos, bufferCount)


def TYHasDevice(ifaceHandle, deviceID):
    r"""
    TYHasDevice(ifaceHandle, deviceID) -> TY_STATUS

    Parameters
    ----------
    ifaceHandle: TY_INTERFACE_HANDLE
    deviceID: char const *

    """
    return _pcammls.TYHasDevice(ifaceHandle, deviceID)


def TYOpenDevice(ifaceHandle, deviceID, outFwErrorcode=None):
    r"""
    TYOpenDevice(ifaceHandle, deviceID, outFwErrorcode=None) -> TY_STATUS

    Parameters
    ----------
    ifaceHandle: TY_INTERFACE_HANDLE
    deviceID: char const *
    outFwErrorcode: TY_FW_ERRORCODE *

    """
    return _pcammls.TYOpenDevice(ifaceHandle, deviceID, outFwErrorcode)


def TYOpenDeviceWithIP(ifaceHandle, IP):
    r"""
    TYOpenDeviceWithIP(ifaceHandle, IP) -> TY_STATUS

    Parameters
    ----------
    ifaceHandle: TY_INTERFACE_HANDLE
    IP: char const *

    """
    return _pcammls.TYOpenDeviceWithIP(ifaceHandle, IP)


def TYGetDeviceInterface(hDevice):
    r"""
    TYGetDeviceInterface(hDevice) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE

    """
    return _pcammls.TYGetDeviceInterface(hDevice)


def TYForceDeviceIP(ifaceHandle, MAC, newIP, newNetMask, newGateway):
    r"""
    TYForceDeviceIP(ifaceHandle, MAC, newIP, newNetMask, newGateway) -> TY_STATUS

    Parameters
    ----------
    ifaceHandle: TY_INTERFACE_HANDLE
    MAC: char const *
    newIP: char const *
    newNetMask: char const *
    newGateway: char const *

    """
    return _pcammls.TYForceDeviceIP(ifaceHandle, MAC, newIP, newNetMask, newGateway)


def TYCloseDevice(hDevice, reboot=False):
    r"""
    TYCloseDevice(hDevice, reboot=False) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    reboot: bool

    """
    return _pcammls.TYCloseDevice(hDevice, reboot)


def TYGetDeviceInfo(hDevice, info):
    r"""
    TYGetDeviceInfo(hDevice, info) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    info: TY_DEVICE_BASE_INFO *

    """
    return _pcammls.TYGetDeviceInfo(hDevice, info)


def TYGetComponentIDs(hDevice):
    r"""
    TYGetComponentIDs(hDevice) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE

    """
    return _pcammls.TYGetComponentIDs(hDevice)


def TYGetEnabledComponents(hDevice):
    r"""
    TYGetEnabledComponents(hDevice) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE

    """
    return _pcammls.TYGetEnabledComponents(hDevice)


def TYEnableComponents(hDevice, componentIDs):
    r"""
    TYEnableComponents(hDevice, componentIDs) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentIDs: TY_COMPONENT_ID

    """
    return _pcammls.TYEnableComponents(hDevice, componentIDs)


def TYDisableComponents(hDevice, componentIDs):
    r"""
    TYDisableComponents(hDevice, componentIDs) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentIDs: TY_COMPONENT_ID

    """
    return _pcammls.TYDisableComponents(hDevice, componentIDs)


def TYGetFrameBufferSize(hDevice):
    r"""
    TYGetFrameBufferSize(hDevice) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE

    """
    return _pcammls.TYGetFrameBufferSize(hDevice)


def TYEnqueueBuffer(hDevice, buffer, bufferSize):
    r"""
    TYEnqueueBuffer(hDevice, buffer, bufferSize) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    buffer: void *
    bufferSize: uint32_t

    """
    return _pcammls.TYEnqueueBuffer(hDevice, buffer, bufferSize)


def TYClearBufferQueue(hDevice):
    r"""
    TYClearBufferQueue(hDevice) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE

    """
    return _pcammls.TYClearBufferQueue(hDevice)


def TYStartCapture(hDevice):
    r"""
    TYStartCapture(hDevice) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE

    """
    return _pcammls.TYStartCapture(hDevice)


def TYStopCapture(hDevice):
    r"""
    TYStopCapture(hDevice) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE

    """
    return _pcammls.TYStopCapture(hDevice)


def TYSendSoftTrigger(hDevice):
    r"""
    TYSendSoftTrigger(hDevice) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE

    """
    return _pcammls.TYSendSoftTrigger(hDevice)


def TYRegisterEventCallback(hDevice, callback, userdata):
    r"""
    TYRegisterEventCallback(hDevice, callback, userdata) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    callback: TY_EVENT_CALLBACK
    userdata: void *

    """
    return _pcammls.TYRegisterEventCallback(hDevice, callback, userdata)


def TYRegisterImuCallback(hDevice, callback, userdata):
    r"""
    TYRegisterImuCallback(hDevice, callback, userdata) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    callback: TY_IMU_CALLBACK
    userdata: void *

    """
    return _pcammls.TYRegisterImuCallback(hDevice, callback, userdata)


def TYFetchFrame(hDevice, frame, timeout):
    r"""
    TYFetchFrame(hDevice, frame, timeout) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    frame: TY_FRAME_DATA *
    timeout: int32_t

    """
    return _pcammls.TYFetchFrame(hDevice, frame, timeout)


def TYHasFeature(hDevice, componentID, featureID):
    r"""
    TYHasFeature(hDevice, componentID, featureID) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID

    """
    return _pcammls.TYHasFeature(hDevice, componentID, featureID)


def TYGetFeatureInfo(hDevice, componentID, featureID, featureInfo):
    r"""
    TYGetFeatureInfo(hDevice, componentID, featureID, featureInfo) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    featureInfo: TY_FEATURE_INFO *

    """
    return _pcammls.TYGetFeatureInfo(hDevice, componentID, featureID, featureInfo)


def TYGetIntRange(hDevice, componentID, featureID, intRange):
    r"""
    TYGetIntRange(hDevice, componentID, featureID, intRange) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    intRange: TY_INT_RANGE *

    """
    return _pcammls.TYGetIntRange(hDevice, componentID, featureID, intRange)


def TYGetInt(hDevice, componentID, featureID):
    r"""
    TYGetInt(hDevice, componentID, featureID) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID

    """
    return _pcammls.TYGetInt(hDevice, componentID, featureID)


def TYSetInt(hDevice, componentID, featureID, value):
    r"""
    TYSetInt(hDevice, componentID, featureID, value) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    value: int32_t

    """
    return _pcammls.TYSetInt(hDevice, componentID, featureID, value)


def TYGetFloatRange(hDevice, componentID, featureID, floatRange):
    r"""
    TYGetFloatRange(hDevice, componentID, featureID, floatRange) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    floatRange: TY_FLOAT_RANGE *

    """
    return _pcammls.TYGetFloatRange(hDevice, componentID, featureID, floatRange)


def TYGetFloat(hDevice, componentID, featureID):
    r"""
    TYGetFloat(hDevice, componentID, featureID) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID

    """
    return _pcammls.TYGetFloat(hDevice, componentID, featureID)


def TYSetFloat(hDevice, componentID, featureID, value):
    r"""
    TYSetFloat(hDevice, componentID, featureID, value) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    value: float

    """
    return _pcammls.TYSetFloat(hDevice, componentID, featureID, value)


def TYGetEnumEntryCount(hDevice, componentID, featureID):
    r"""
    TYGetEnumEntryCount(hDevice, componentID, featureID) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID

    """
    return _pcammls.TYGetEnumEntryCount(hDevice, componentID, featureID)


def TYGetEnumEntryInfo(hDevice, componentID, featureID, entries, entryCount):
    r"""
    TYGetEnumEntryInfo(hDevice, componentID, featureID, entries, entryCount) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    entries: TY_ENUM_ENTRY *
    entryCount: uint32_t

    """
    return _pcammls.TYGetEnumEntryInfo(
        hDevice, componentID, featureID, entries, entryCount
    )


def TYGetEnum(hDevice, componentID, featureID, value):
    r"""
    TYGetEnum(hDevice, componentID, featureID, value) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    value: uint32_t *

    """
    return _pcammls.TYGetEnum(hDevice, componentID, featureID, value)


def TYSetEnum(hDevice, componentID, featureID, value):
    r"""
    TYSetEnum(hDevice, componentID, featureID, value) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    value: uint32_t

    """
    return _pcammls.TYSetEnum(hDevice, componentID, featureID, value)


def TYGetBool(hDevice, componentID, featureID):
    r"""
    TYGetBool(hDevice, componentID, featureID) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID

    """
    return _pcammls.TYGetBool(hDevice, componentID, featureID)


def TYSetBool(hDevice, componentID, featureID, value):
    r"""
    TYSetBool(hDevice, componentID, featureID, value) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    value: bool

    """
    return _pcammls.TYSetBool(hDevice, componentID, featureID, value)


def TYGetStringLength(hDevice, componentID, featureID, size):
    r"""
    TYGetStringLength(hDevice, componentID, featureID, size) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    size: uint32_t *

    """
    return _pcammls.TYGetStringLength(hDevice, componentID, featureID, size)


def TYGetString(hDevice, componentID, featureID, buffer, bufferSize):
    r"""
    TYGetString(hDevice, componentID, featureID, buffer, bufferSize) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    buffer: char *
    bufferSize: uint32_t

    """
    return _pcammls.TYGetString(hDevice, componentID, featureID, buffer, bufferSize)


def TYSetString(hDevice, componentID, featureID, buffer):
    r"""
    TYSetString(hDevice, componentID, featureID, buffer) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    buffer: char const *

    """
    return _pcammls.TYSetString(hDevice, componentID, featureID, buffer)


def TYGetStruct(hDevice, componentID, featureID, pStruct, structSize):
    r"""
    TYGetStruct(hDevice, componentID, featureID, pStruct, structSize) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    pStruct: void *
    structSize: uint32_t

    """
    return _pcammls.TYGetStruct(hDevice, componentID, featureID, pStruct, structSize)


def TYSetStruct(hDevice, componentID, featureID, pStruct, structSize):
    r"""
    TYSetStruct(hDevice, componentID, featureID, pStruct, structSize) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    pStruct: void *
    structSize: uint32_t

    """
    return _pcammls.TYSetStruct(hDevice, componentID, featureID, pStruct, structSize)


def TYGetByteArraySize(hDevice, componentID, featureID):
    r"""
    TYGetByteArraySize(hDevice, componentID, featureID) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID

    """
    return _pcammls.TYGetByteArraySize(hDevice, componentID, featureID)


def TYGetByteArray(hDevice, componentID, featureID, pBuffer, bufferSize):
    r"""
    TYGetByteArray(hDevice, componentID, featureID, pBuffer, bufferSize) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    pBuffer: uint8_t *
    bufferSize: uint32_t

    """
    return _pcammls.TYGetByteArray(hDevice, componentID, featureID, pBuffer, bufferSize)


def TYSetByteArray(hDevice, componentID, featureID, pBuffer, bufferSize):
    r"""
    TYSetByteArray(hDevice, componentID, featureID, pBuffer, bufferSize) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    pBuffer: uint8_t const *
    bufferSize: uint32_t

    """
    return _pcammls.TYSetByteArray(hDevice, componentID, featureID, pBuffer, bufferSize)


def TYGetByteArrayAttr(hDevice, componentID, featureID, pAttr):
    r"""
    TYGetByteArrayAttr(hDevice, componentID, featureID, pAttr) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureID: TY_FEATURE_ID
    pAttr: TY_BYTEARRAY_ATTR *

    """
    return _pcammls.TYGetByteArrayAttr(hDevice, componentID, featureID, pAttr)


def TYGetDeviceFeatureNumber(hDevice, componentID, size):
    r"""
    TYGetDeviceFeatureNumber(hDevice, componentID, size) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    size: uint32_t *

    """
    return _pcammls.TYGetDeviceFeatureNumber(hDevice, componentID, size)


def TYGetDeviceFeatureInfo(hDevice, componentID, featureInfo, entryCount):
    r"""
    TYGetDeviceFeatureInfo(hDevice, componentID, featureInfo, entryCount) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    componentID: TY_COMPONENT_ID
    featureInfo: TY_FEATURE_INFO *
    entryCount: uint32_t

    """
    return _pcammls.TYGetDeviceFeatureInfo(
        hDevice, componentID, featureInfo, entryCount
    )


def TYGetDeviceXMLSize(hDevice, size):
    r"""
    TYGetDeviceXMLSize(hDevice, size) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    size: uint32_t *

    """
    return _pcammls.TYGetDeviceXMLSize(hDevice, size)


def TYGetDeviceXML(hDevice, xml, in_size, out_size):
    r"""
    TYGetDeviceXML(hDevice, xml, in_size, out_size) -> TY_STATUS

    Parameters
    ----------
    hDevice: TY_DEV_HANDLE
    xml: char *
    in_size: uint32_t const
    out_size: uint32_t *

    """
    return _pcammls.TYGetDeviceXML(hDevice, xml, in_size, out_size)


def TYImageProcesAcceEnable(en):
    r"""
    TYImageProcesAcceEnable(en) -> TY_STATUS

    Parameters
    ----------
    en: bool

    """
    return _pcammls.TYImageProcesAcceEnable(en)


def TYUndistortImage(srcCalibInfo, srcImage, cameraNewIntrinsic, dstImage):
    r"""
    TYUndistortImage(srcCalibInfo, srcImage, cameraNewIntrinsic, dstImage) -> TY_STATUS

    Parameters
    ----------
    srcCalibInfo: TY_CAMERA_CALIB_INFO const *
    srcImage: TY_IMAGE_DATA const *
    cameraNewIntrinsic: TY_CAMERA_INTRINSIC const *
    dstImage: TY_IMAGE_DATA *

    """
    return _pcammls.TYUndistortImage(
        srcCalibInfo, srcImage, cameraNewIntrinsic, dstImage
    )


class DepthSpeckleFilterParameters(object):
    r"""Proxy of C++ DepthSpeckleFilterParameters class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    max_speckle_size = property(
        _pcammls.DepthSpeckleFilterParameters_max_speckle_size_get,
        _pcammls.DepthSpeckleFilterParameters_max_speckle_size_set,
        doc=r"""max_speckle_size""",
    )
    max_speckle_diff = property(
        _pcammls.DepthSpeckleFilterParameters_max_speckle_diff_get,
        _pcammls.DepthSpeckleFilterParameters_max_speckle_diff_set,
        doc=r"""max_speckle_diff""",
    )

    def __init__(self):
        r"""__init__(self) -> DepthSpeckleFilterParameters"""
        _pcammls.DepthSpeckleFilterParameters_swiginit(
            self, _pcammls.new_DepthSpeckleFilterParameters()
        )

    __swig_destroy__ = _pcammls.delete_DepthSpeckleFilterParameters


# Register DepthSpeckleFilterParameters in _pcammls:
_pcammls.DepthSpeckleFilterParameters_swigregister(DepthSpeckleFilterParameters)


def TYDepthSpeckleFilter(depthImage, param):
    r"""
    TYDepthSpeckleFilter(depthImage, param) -> TY_STATUS

    Parameters
    ----------
    depthImage: TY_IMAGE_DATA *
    param: DepthSpeckleFilterParameters const *

    """
    return _pcammls.TYDepthSpeckleFilter(depthImage, param)


class DepthEnhenceParameters(object):
    r"""Proxy of C++ DepthEnhenceParameters class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    sigma_s = property(
        _pcammls.DepthEnhenceParameters_sigma_s_get,
        _pcammls.DepthEnhenceParameters_sigma_s_set,
        doc=r"""sigma_s""",
    )
    sigma_r = property(
        _pcammls.DepthEnhenceParameters_sigma_r_get,
        _pcammls.DepthEnhenceParameters_sigma_r_set,
        doc=r"""sigma_r""",
    )
    outlier_win_sz = property(
        _pcammls.DepthEnhenceParameters_outlier_win_sz_get,
        _pcammls.DepthEnhenceParameters_outlier_win_sz_set,
        doc=r"""outlier_win_sz""",
    )
    outlier_rate = property(
        _pcammls.DepthEnhenceParameters_outlier_rate_get,
        _pcammls.DepthEnhenceParameters_outlier_rate_set,
        doc=r"""outlier_rate""",
    )

    def __init__(self):
        r"""__init__(self) -> DepthEnhenceParameters"""
        _pcammls.DepthEnhenceParameters_swiginit(
            self, _pcammls.new_DepthEnhenceParameters()
        )

    __swig_destroy__ = _pcammls.delete_DepthEnhenceParameters


# Register DepthEnhenceParameters in _pcammls:
_pcammls.DepthEnhenceParameters_swigregister(DepthEnhenceParameters)


def TYDepthEnhenceFilter(depthImages, imageNum, guide, output, param):
    r"""
    TYDepthEnhenceFilter(depthImages, imageNum, guide, output, param) -> TY_STATUS

    Parameters
    ----------
    depthImages: TY_IMAGE_DATA const *
    imageNum: int
    guide: TY_IMAGE_DATA *
    output: TY_IMAGE_DATA *
    param: DepthEnhenceParameters const *

    """
    return _pcammls.TYDepthEnhenceFilter(depthImages, imageNum, guide, output, param)


class TY_PIXEL_DESC(object):
    r"""Proxy of C++ TY_PIXEL_DESC class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    x = property(
        _pcammls.TY_PIXEL_DESC_x_get, _pcammls.TY_PIXEL_DESC_x_set, doc=r"""x"""
    )
    y = property(
        _pcammls.TY_PIXEL_DESC_y_get, _pcammls.TY_PIXEL_DESC_y_set, doc=r"""y"""
    )
    depth = property(
        _pcammls.TY_PIXEL_DESC_depth_get,
        _pcammls.TY_PIXEL_DESC_depth_set,
        doc=r"""depth""",
    )
    rsvd = property(
        _pcammls.TY_PIXEL_DESC_rsvd_get,
        _pcammls.TY_PIXEL_DESC_rsvd_set,
        doc=r"""rsvd""",
    )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_PIXEL_DESC_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_PIXEL_DESC"""
        _pcammls.TY_PIXEL_DESC_swiginit(self, _pcammls.new_TY_PIXEL_DESC())

    __swig_destroy__ = _pcammls.delete_TY_PIXEL_DESC


# Register TY_PIXEL_DESC in _pcammls:
_pcammls.TY_PIXEL_DESC_swigregister(TY_PIXEL_DESC)


class TY_PIXEL_COLOR_DESC(object):
    r"""Proxy of C++ TY_PIXEL_COLOR_DESC class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    x = property(
        _pcammls.TY_PIXEL_COLOR_DESC_x_get,
        _pcammls.TY_PIXEL_COLOR_DESC_x_set,
        doc=r"""x""",
    )
    y = property(
        _pcammls.TY_PIXEL_COLOR_DESC_y_get,
        _pcammls.TY_PIXEL_COLOR_DESC_y_set,
        doc=r"""y""",
    )
    bgr_ch1 = property(
        _pcammls.TY_PIXEL_COLOR_DESC_bgr_ch1_get,
        _pcammls.TY_PIXEL_COLOR_DESC_bgr_ch1_set,
        doc=r"""bgr_ch1""",
    )
    bgr_ch2 = property(
        _pcammls.TY_PIXEL_COLOR_DESC_bgr_ch2_get,
        _pcammls.TY_PIXEL_COLOR_DESC_bgr_ch2_set,
        doc=r"""bgr_ch2""",
    )
    bgr_ch3 = property(
        _pcammls.TY_PIXEL_COLOR_DESC_bgr_ch3_get,
        _pcammls.TY_PIXEL_COLOR_DESC_bgr_ch3_set,
        doc=r"""bgr_ch3""",
    )
    rsvd = property(
        _pcammls.TY_PIXEL_COLOR_DESC_rsvd_get,
        _pcammls.TY_PIXEL_COLOR_DESC_rsvd_set,
        doc=r"""rsvd""",
    )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.TY_PIXEL_COLOR_DESC_CSize(self)

    def __init__(self):
        r"""__init__(self) -> TY_PIXEL_COLOR_DESC"""
        _pcammls.TY_PIXEL_COLOR_DESC_swiginit(self, _pcammls.new_TY_PIXEL_COLOR_DESC())

    __swig_destroy__ = _pcammls.delete_TY_PIXEL_COLOR_DESC


# Register TY_PIXEL_COLOR_DESC in _pcammls:
_pcammls.TY_PIXEL_COLOR_DESC_swigregister(TY_PIXEL_COLOR_DESC)


def TYInvertExtrinsic(orgExtrinsic, invExtrinsic):
    r"""
    TYInvertExtrinsic(orgExtrinsic, invExtrinsic) -> TY_STATUS

    Parameters
    ----------
    orgExtrinsic: TY_CAMERA_EXTRINSIC const *
    invExtrinsic: TY_CAMERA_EXTRINSIC *

    """
    return _pcammls.TYInvertExtrinsic(orgExtrinsic, invExtrinsic)


def TYMapDepthToPoint3d(
    src_calib, depthW, depthH, depthPixels, count, point3d, f_scale_unit=1.0
):
    r"""
    TYMapDepthToPoint3d(src_calib, depthW, depthH, depthPixels, count, point3d, f_scale_unit=1.0) -> TY_STATUS

    Parameters
    ----------
    src_calib: TY_CAMERA_CALIB_INFO const *
    depthW: uint32_t
    depthH: uint32_t
    depthPixels: TY_PIXEL_DESC const *
    count: uint32_t
    point3d: TY_VECT_3F *
    f_scale_unit: float

    """
    return _pcammls.TYMapDepthToPoint3d(
        src_calib, depthW, depthH, depthPixels, count, point3d, f_scale_unit
    )


def TYMapPoint3dToDepth(
    dst_calib, point3d, count, depthW, depthH, depth, f_scale_unit=1.0
):
    r"""
    TYMapPoint3dToDepth(dst_calib, point3d, count, depthW, depthH, depth, f_scale_unit=1.0) -> TY_STATUS

    Parameters
    ----------
    dst_calib: TY_CAMERA_CALIB_INFO const *
    point3d: TY_VECT_3F const *
    count: uint32_t
    depthW: uint32_t
    depthH: uint32_t
    depth: TY_PIXEL_DESC *
    f_scale_unit: float

    """
    return _pcammls.TYMapPoint3dToDepth(
        dst_calib, point3d, count, depthW, depthH, depth, f_scale_unit
    )


def TYMapDepthImageToPoint3d(
    src_calib, imageW, imageH, depth, point3d, f_scale_unit=1.0
):
    r"""
    TYMapDepthImageToPoint3d(src_calib, imageW, imageH, depth, point3d, f_scale_unit=1.0) -> TY_STATUS

    Parameters
    ----------
    src_calib: TY_CAMERA_CALIB_INFO const *
    imageW: int32_t
    imageH: int32_t
    depth: uint16_t const *
    point3d: TY_VECT_3F *
    f_scale_unit: float

    """
    return _pcammls.TYMapDepthImageToPoint3d(
        src_calib, imageW, imageH, depth, point3d, f_scale_unit
    )


def TYDepthImageFillEmptyRegion(depth, depthW, depthH):
    r"""
    TYDepthImageFillEmptyRegion(depth, depthW, depthH) -> TY_STATUS

    Parameters
    ----------
    depth: uint16_t *
    depthW: uint32_t
    depthH: uint32_t

    """
    return _pcammls.TYDepthImageFillEmptyRegion(depth, depthW, depthH)


def TYMapPoint3dToDepthImage(
    dst_calib, point3d, count, depthW, depthH, depth, f_target_scale=1.0
):
    r"""
    TYMapPoint3dToDepthImage(dst_calib, point3d, count, depthW, depthH, depth, f_target_scale=1.0) -> TY_STATUS

    Parameters
    ----------
    dst_calib: TY_CAMERA_CALIB_INFO const *
    point3d: TY_VECT_3F const *
    count: uint32_t
    depthW: uint32_t
    depthH: uint32_t
    depth: uint16_t *
    f_target_scale: float

    """
    return _pcammls.TYMapPoint3dToDepthImage(
        dst_calib, point3d, count, depthW, depthH, depth, f_target_scale
    )


def TYMapPoint3dToPoint3d(extrinsic, point3dFrom, count, point3dTo):
    r"""
    TYMapPoint3dToPoint3d(extrinsic, point3dFrom, count, point3dTo) -> TY_STATUS

    Parameters
    ----------
    extrinsic: TY_CAMERA_EXTRINSIC const *
    point3dFrom: TY_VECT_3F const *
    count: int32_t
    point3dTo: TY_VECT_3F *

    """
    return _pcammls.TYMapPoint3dToPoint3d(extrinsic, point3dFrom, count, point3dTo)


def TYMapDepthToColorCoordinate(
    depth_calib,
    depthW,
    depthH,
    depth,
    count,
    color_calib,
    mappedW,
    mappedH,
    mappedDepth,
    f_scale_unit=1.0,
):
    r"""
    TYMapDepthToColorCoordinate(depth_calib, depthW, depthH, depth, count, color_calib, mappedW, mappedH, mappedDepth, f_scale_unit=1.0) -> TY_STATUS

    Parameters
    ----------
    depth_calib: TY_CAMERA_CALIB_INFO const *
    depthW: uint32_t
    depthH: uint32_t
    depth: TY_PIXEL_DESC const *
    count: uint32_t
    color_calib: TY_CAMERA_CALIB_INFO const *
    mappedW: uint32_t
    mappedH: uint32_t
    mappedDepth: TY_PIXEL_DESC *
    f_scale_unit: float

    """
    return _pcammls.TYMapDepthToColorCoordinate(
        depth_calib,
        depthW,
        depthH,
        depth,
        count,
        color_calib,
        mappedW,
        mappedH,
        mappedDepth,
        f_scale_unit,
    )


def TYMapDepthImageToColorCoordinate(
    depth_calib,
    depthW,
    depthH,
    depth,
    color_calib,
    mappedW,
    mappedH,
    mappedDepth,
    f_scale_unit=1.0,
):
    r"""
    TYMapDepthImageToColorCoordinate(depth_calib, depthW, depthH, depth, color_calib, mappedW, mappedH, mappedDepth, f_scale_unit=1.0) -> TY_STATUS

    Parameters
    ----------
    depth_calib: TY_CAMERA_CALIB_INFO const *
    depthW: uint32_t
    depthH: uint32_t
    depth: uint16_t const *
    color_calib: TY_CAMERA_CALIB_INFO const *
    mappedW: uint32_t
    mappedH: uint32_t
    mappedDepth: uint16_t *
    f_scale_unit: float

    """
    return _pcammls.TYMapDepthImageToColorCoordinate(
        depth_calib,
        depthW,
        depthH,
        depth,
        color_calib,
        mappedW,
        mappedH,
        mappedDepth,
        f_scale_unit,
    )


def TYCreateDepthToColorCoordinateLookupTable(
    depth_calib,
    depthW,
    depthH,
    depth,
    color_calib,
    mappedW,
    mappedH,
    lut,
    f_scale_unit=1.0,
):
    r"""
    TYCreateDepthToColorCoordinateLookupTable(depth_calib, depthW, depthH, depth, color_calib, mappedW, mappedH, lut, f_scale_unit=1.0) -> TY_STATUS

    Parameters
    ----------
    depth_calib: TY_CAMERA_CALIB_INFO const *
    depthW: uint32_t
    depthH: uint32_t
    depth: uint16_t const *
    color_calib: TY_CAMERA_CALIB_INFO const *
    mappedW: uint32_t
    mappedH: uint32_t
    lut: TY_PIXEL_DESC *
    f_scale_unit: float

    """
    return _pcammls.TYCreateDepthToColorCoordinateLookupTable(
        depth_calib,
        depthW,
        depthH,
        depth,
        color_calib,
        mappedW,
        mappedH,
        lut,
        f_scale_unit,
    )


def TYMapRGBPixelsToDepthCoordinate(
    depth_calib,
    depthW,
    depthH,
    depth,
    color_calib,
    rgbW,
    rgbH,
    src,
    cnt,
    min_distance,
    max_distance,
    dst,
    f_scale_unit=1.0,
):
    r"""
    TYMapRGBPixelsToDepthCoordinate(depth_calib, depthW, depthH, depth, color_calib, rgbW, rgbH, src, cnt, min_distance, max_distance, dst, f_scale_unit=1.0) -> TY_STATUS

    Parameters
    ----------
    depth_calib: TY_CAMERA_CALIB_INFO const *
    depthW: uint32_t
    depthH: uint32_t
    depth: uint16_t const *
    color_calib: TY_CAMERA_CALIB_INFO const *
    rgbW: uint32_t
    rgbH: uint32_t
    src: TY_PIXEL_COLOR_DESC *
    cnt: uint32_t
    min_distance: uint32_t
    max_distance: uint32_t
    dst: TY_PIXEL_COLOR_DESC *
    f_scale_unit: float

    """
    return _pcammls.TYMapRGBPixelsToDepthCoordinate(
        depth_calib,
        depthW,
        depthH,
        depth,
        color_calib,
        rgbW,
        rgbH,
        src,
        cnt,
        min_distance,
        max_distance,
        dst,
        f_scale_unit,
    )


def TYMapRGBImageToDepthCoordinate(
    depth_calib,
    depthW,
    depthH,
    depth,
    color_calib,
    rgbW,
    rgbH,
    inRgb,
    mappedRgb,
    f_scale_unit=1.0,
):
    r"""
    TYMapRGBImageToDepthCoordinate(depth_calib, depthW, depthH, depth, color_calib, rgbW, rgbH, inRgb, mappedRgb, f_scale_unit=1.0) -> TY_STATUS

    Parameters
    ----------
    depth_calib: TY_CAMERA_CALIB_INFO const *
    depthW: uint32_t
    depthH: uint32_t
    depth: uint16_t const *
    color_calib: TY_CAMERA_CALIB_INFO const *
    rgbW: uint32_t
    rgbH: uint32_t
    inRgb: uint8_t const *
    mappedRgb: uint8_t *
    f_scale_unit: float

    """
    return _pcammls.TYMapRGBImageToDepthCoordinate(
        depth_calib,
        depthW,
        depthH,
        depth,
        color_calib,
        rgbW,
        rgbH,
        inRgb,
        mappedRgb,
        f_scale_unit,
    )


def TYMapRGB48ImageToDepthCoordinate(
    depth_calib,
    depthW,
    depthH,
    depth,
    color_calib,
    rgbW,
    rgbH,
    inRgb,
    mappedRgb,
    f_scale_unit=1.0,
):
    r"""
    TYMapRGB48ImageToDepthCoordinate(depth_calib, depthW, depthH, depth, color_calib, rgbW, rgbH, inRgb, mappedRgb, f_scale_unit=1.0) -> TY_STATUS

    Parameters
    ----------
    depth_calib: TY_CAMERA_CALIB_INFO const *
    depthW: uint32_t
    depthH: uint32_t
    depth: uint16_t const *
    color_calib: TY_CAMERA_CALIB_INFO const *
    rgbW: uint32_t
    rgbH: uint32_t
    inRgb: uint16_t const *
    mappedRgb: uint16_t *
    f_scale_unit: float

    """
    return _pcammls.TYMapRGB48ImageToDepthCoordinate(
        depth_calib,
        depthW,
        depthH,
        depth,
        color_calib,
        rgbW,
        rgbH,
        inRgb,
        mappedRgb,
        f_scale_unit,
    )


def TYMapMono16ImageToDepthCoordinate(
    depth_calib,
    depthW,
    depthH,
    depth,
    color_calib,
    rgbW,
    rgbH,
    gray,
    mappedGray,
    f_scale_unit=1.0,
):
    r"""
    TYMapMono16ImageToDepthCoordinate(depth_calib, depthW, depthH, depth, color_calib, rgbW, rgbH, gray, mappedGray, f_scale_unit=1.0) -> TY_STATUS

    Parameters
    ----------
    depth_calib: TY_CAMERA_CALIB_INFO const *
    depthW: uint32_t
    depthH: uint32_t
    depth: uint16_t const *
    color_calib: TY_CAMERA_CALIB_INFO const *
    rgbW: uint32_t
    rgbH: uint32_t
    gray: uint16_t const *
    mappedGray: uint16_t *
    f_scale_unit: float

    """
    return _pcammls.TYMapMono16ImageToDepthCoordinate(
        depth_calib,
        depthW,
        depthH,
        depth,
        color_calib,
        rgbW,
        rgbH,
        gray,
        mappedGray,
        f_scale_unit,
    )


def TYMapMono8ImageToDepthCoordinate(
    depth_calib,
    depthW,
    depthH,
    depth,
    color_calib,
    monoW,
    monoH,
    inMono,
    mappedMono,
    f_scale_unit=1.0,
):
    r"""
    TYMapMono8ImageToDepthCoordinate(depth_calib, depthW, depthH, depth, color_calib, monoW, monoH, inMono, mappedMono, f_scale_unit=1.0) -> TY_STATUS

    Parameters
    ----------
    depth_calib: TY_CAMERA_CALIB_INFO const *
    depthW: uint32_t
    depthH: uint32_t
    depth: uint16_t const *
    color_calib: TY_CAMERA_CALIB_INFO const *
    monoW: uint32_t
    monoH: uint32_t
    inMono: uint8_t const *
    mappedMono: uint8_t *
    f_scale_unit: float

    """
    return _pcammls.TYMapMono8ImageToDepthCoordinate(
        depth_calib,
        depthW,
        depthH,
        depth,
        color_calib,
        monoW,
        monoH,
        inMono,
        mappedMono,
        f_scale_unit,
    )


def TYPixelsOverlapRemove(lut, count, imageW, imageH):
    r"""
    TYPixelsOverlapRemove(lut, count, imageW, imageH)

    Parameters
    ----------
    lut: TY_PIXEL_DESC *
    count: uint32_t
    imageW: uint32_t
    imageH: uint32_t

    """
    return _pcammls.TYPixelsOverlapRemove(lut, count, imageW, imageH)


TY_ISP_FEATURE_CAM_MODEL = _pcammls.TY_ISP_FEATURE_CAM_MODEL

TY_ISP_FEATURE_CAM_DEV_HANDLE = _pcammls.TY_ISP_FEATURE_CAM_DEV_HANDLE
r"""device handle for device control"""
TY_ISP_FEATURE_CAM_DEV_COMPONENT = _pcammls.TY_ISP_FEATURE_CAM_DEV_COMPONENT
r"""the component to control"""
TY_ISP_FEATURE_IMAGE_SIZE = _pcammls.TY_ISP_FEATURE_IMAGE_SIZE
r"""image size width"""
TY_ISP_FEATURE_WHITEBALANCE_GAIN = _pcammls.TY_ISP_FEATURE_WHITEBALANCE_GAIN

TY_ISP_FEATURE_ENABLE_AUTO_WHITEBALANCE = (
    _pcammls.TY_ISP_FEATURE_ENABLE_AUTO_WHITEBALANCE
)

TY_ISP_FEATURE_SHADING = _pcammls.TY_ISP_FEATURE_SHADING

TY_ISP_FEATURE_SHADING_CENTER = _pcammls.TY_ISP_FEATURE_SHADING_CENTER

TY_ISP_FEATURE_BLACK_LEVEL = _pcammls.TY_ISP_FEATURE_BLACK_LEVEL
r"""global black level"""
TY_ISP_FEATURE_BLACK_LEVEL_COLUMN = _pcammls.TY_ISP_FEATURE_BLACK_LEVEL_COLUMN
r"""to set different black level for each image column"""
TY_ISP_FEATURE_BLACK_LEVEL_GAIN = _pcammls.TY_ISP_FEATURE_BLACK_LEVEL_GAIN
r"""global pixel gain"""
TY_ISP_FEATURE_BLACK_LEVEL_GAIN_COLUMN = _pcammls.TY_ISP_FEATURE_BLACK_LEVEL_GAIN_COLUMN
r"""to set different gain for each image column"""
TY_ISP_FEATURE_BAYER_PATTERN = _pcammls.TY_ISP_FEATURE_BAYER_PATTERN

TY_ISP_FEATURE_DEMOSAIC_METHOD = _pcammls.TY_ISP_FEATURE_DEMOSAIC_METHOD

TY_ISP_FEATURE_GAMMA = _pcammls.TY_ISP_FEATURE_GAMMA

TY_ISP_FEATURE_DEFECT_PIXEL_LIST = _pcammls.TY_ISP_FEATURE_DEFECT_PIXEL_LIST

TY_ISP_FEATURE_CCM = _pcammls.TY_ISP_FEATURE_CCM

TY_ISP_FEATURE_CCM_ENABLE = _pcammls.TY_ISP_FEATURE_CCM_ENABLE
r"""ENABLE CCM"""
TY_ISP_FEATURE_BRIGHT = _pcammls.TY_ISP_FEATURE_BRIGHT

TY_ISP_FEATURE_CONTRAST = _pcammls.TY_ISP_FEATURE_CONTRAST

TY_ISP_FEATURE_AUTOBRIGHT = _pcammls.TY_ISP_FEATURE_AUTOBRIGHT

TY_ISP_FEATURE_INPUT_RESAMPLE_SCALE = _pcammls.TY_ISP_FEATURE_INPUT_RESAMPLE_SCALE

TY_ISP_FEATURE_ENABLE_AUTO_EXPOSURE_GAIN = (
    _pcammls.TY_ISP_FEATURE_ENABLE_AUTO_EXPOSURE_GAIN
)

TY_ISP_FEATURE_AUTO_EXPOSURE_RANGE = _pcammls.TY_ISP_FEATURE_AUTO_EXPOSURE_RANGE
r"""exposure range ,default no limit"""
TY_ISP_FEATURE_AUTO_GAIN_RANGE = _pcammls.TY_ISP_FEATURE_AUTO_GAIN_RANGE
r"""gain range ,default no limit"""
TY_ISP_FEATURE_AUTO_EXPOSURE_UPDATE_INTERVAL = (
    _pcammls.TY_ISP_FEATURE_AUTO_EXPOSURE_UPDATE_INTERVAL
)
r"""update device exposure interval , default 5 frame"""
TY_ISP_FEATURE_DEBUG_LOG = _pcammls.TY_ISP_FEATURE_DEBUG_LOG
r"""display detail log information"""
TY_ISP_BAYER_GB = _pcammls.TY_ISP_BAYER_GB

TY_ISP_BAYER_BG = _pcammls.TY_ISP_BAYER_BG

TY_ISP_BAYER_RG = _pcammls.TY_ISP_BAYER_RG

TY_ISP_BAYER_GR = _pcammls.TY_ISP_BAYER_GR

TY_ISP_BAYER_AUTO = _pcammls.TY_ISP_BAYER_AUTO

TY_DEMOSAIC_METHOD_SIMPLE = _pcammls.TY_DEMOSAIC_METHOD_SIMPLE

TY_DEMOSAIC_METHOD_BILINEAR = _pcammls.TY_DEMOSAIC_METHOD_BILINEAR

TY_DEMOSAIC_METHOD_HQLINEAR = _pcammls.TY_DEMOSAIC_METHOD_HQLINEAR

TY_DEMOSAIC_METHOD_EDGESENSE = _pcammls.TY_DEMOSAIC_METHOD_EDGESENSE


class TY_ISP_FEATURE_INFO(object):
    r"""Proxy of C++ TY_ISP_FEATURE_INFO class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    id = property(
        _pcammls.TY_ISP_FEATURE_INFO_id_get,
        _pcammls.TY_ISP_FEATURE_INFO_id_set,
        doc=r"""id""",
    )
    size = property(
        _pcammls.TY_ISP_FEATURE_INFO_size_get,
        _pcammls.TY_ISP_FEATURE_INFO_size_set,
        doc=r"""size""",
    )
    name = property(
        _pcammls.TY_ISP_FEATURE_INFO_name_get,
        _pcammls.TY_ISP_FEATURE_INFO_name_set,
        doc=r"""name""",
    )
    value_type = property(
        _pcammls.TY_ISP_FEATURE_INFO_value_type_get,
        _pcammls.TY_ISP_FEATURE_INFO_value_type_set,
        doc=r"""value_type""",
    )
    mode = property(
        _pcammls.TY_ISP_FEATURE_INFO_mode_get,
        _pcammls.TY_ISP_FEATURE_INFO_mode_set,
        doc=r"""mode""",
    )

    def __init__(self):
        r"""__init__(self) -> TY_ISP_FEATURE_INFO"""
        _pcammls.TY_ISP_FEATURE_INFO_swiginit(self, _pcammls.new_TY_ISP_FEATURE_INFO())

    __swig_destroy__ = _pcammls.delete_TY_ISP_FEATURE_INFO


# Register TY_ISP_FEATURE_INFO in _pcammls:
_pcammls.TY_ISP_FEATURE_INFO_swigregister(TY_ISP_FEATURE_INFO)


def TYISPCreate():
    r"""TYISPCreate() -> TY_STATUS"""
    return _pcammls.TYISPCreate()


def TYISPRelease(*args):
    r"""
    TYISPRelease(handle)

    Parameters
    ----------
    handle: TY_ISP_HANDLE

    TYISPRelease() -> TY_STATUS
    """
    return _pcammls.TYISPRelease(*args)


def TYISPLoadConfig(handle, config, config_size):
    r"""
    TYISPLoadConfig(handle, config, config_size) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE
    config: uint8_t const *
    config_size: uint32_t

    """
    return _pcammls.TYISPLoadConfig(handle, config, config_size)


def TYISPUpdateDevice(handle):
    r"""
    TYISPUpdateDevice(handle) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE

    """
    return _pcammls.TYISPUpdateDevice(handle)


def TYISPGetFeatureSize(handle, feature_id, size):
    r"""
    TYISPGetFeatureSize(handle, feature_id, size) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE
    feature_id: enum TY_ISP_FEATURE_ID
    size: int32_t *

    """
    return _pcammls.TYISPGetFeatureSize(handle, feature_id, size)


def TYISPHasFeature(handle, feature_id):
    r"""
    TYISPHasFeature(handle, feature_id) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE
    feature_id: enum TY_ISP_FEATURE_ID

    """
    return _pcammls.TYISPHasFeature(handle, feature_id)


def TYISPGetFeatureInfoList(handle, info_buffer, buffer_size):
    r"""
    TYISPGetFeatureInfoList(handle, info_buffer, buffer_size) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE
    info_buffer: TY_ISP_FEATURE_INFO *
    buffer_size: int

    """
    return _pcammls.TYISPGetFeatureInfoList(handle, info_buffer, buffer_size)


def TYISPGetFeatureInfoListSize(handle, buffer_size):
    r"""
    TYISPGetFeatureInfoListSize(handle, buffer_size) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE
    buffer_size: int32_t *

    """
    return _pcammls.TYISPGetFeatureInfoListSize(handle, buffer_size)


def TYISPProcessImage(handle, image_bayer, image_out):
    r"""
    TYISPProcessImage(handle, image_bayer, image_out) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE
    image_bayer: TY_IMAGE_DATA const *
    image_out: TY_IMAGE_DATA *

    """
    return _pcammls.TYISPProcessImage(handle, image_bayer, image_out)


def TYISPSetFeature(*args):
    r"""
    TYISPSetFeature(handle, feature_id, data, size) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE
    feature_id: enum TY_ISP_FEATURE_ID
    data: uint8_t const *
    size: int32_t

    TYISPSetFeature(handle, feature_id, value) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE
    feature_id: enum TY_ISP_FEATURE_ID
    value: int

    TYISPSetFeature(handle, feature_id, value) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE
    feature_id: enum TY_ISP_FEATURE_ID
    value: float

    """
    return _pcammls.TYISPSetFeature(*args)


def TYISPGetFeature(*args):
    r"""
    TYISPGetFeature(handle, feature_id, data_buff, buff_size) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE
    feature_id: enum TY_ISP_FEATURE_ID
    data_buff: uint8_t *
    buff_size: int32_t

    TYISPGetFeature(handle, feature_id, value) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE
    feature_id: enum TY_ISP_FEATURE_ID
    value: int *

    TYISPGetFeature(handle, feature_id) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE
    feature_id: enum TY_ISP_FEATURE_ID

    """
    return _pcammls.TYISPGetFeature(*args)


PERCIPIO_STREAM_COLOR = _pcammls.PERCIPIO_STREAM_COLOR

PERCIPIO_STREAM_DEPTH = _pcammls.PERCIPIO_STREAM_DEPTH

PERCIPIO_STREAM_IR_LEFT = _pcammls.PERCIPIO_STREAM_IR_LEFT

PERCIPIO_STREAM_IR_RIGHT = _pcammls.PERCIPIO_STREAM_IR_RIGHT


class PercipioRectifyIntrData(object):
    r"""Proxy of C++ PercipioRectifyIntrData class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    __swig_destroy__ = _pcammls.delete_PercipioRectifyIntrData

    def __init__(self, *args):
        r"""
        __init__(self) -> PercipioRectifyIntrData
        __init__(self, intr) -> PercipioRectifyIntrData

        Parameters
        ----------
        intr: TY_CAMERA_INTRINSIC const

        """
        _pcammls.PercipioRectifyIntrData_swiginit(
            self, _pcammls.new_PercipioRectifyIntrData(*args)
        )

    def Data(self):
        r"""Data(self) -> ty_float_vector"""
        return _pcammls.PercipioRectifyIntrData_Data(self)


# Register PercipioRectifyIntrData in _pcammls:
_pcammls.PercipioRectifyIntrData_swigregister(PercipioRectifyIntrData)


class PercipioRectifyRotaData(object):
    r"""Proxy of C++ PercipioRectifyRotaData class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    __swig_destroy__ = _pcammls.delete_PercipioRectifyRotaData

    def __init__(self, *args):
        r"""
        __init__(self) -> PercipioRectifyRotaData
        __init__(self, intr) -> PercipioRectifyRotaData

        Parameters
        ----------
        intr: TY_CAMERA_ROTATION const

        """
        _pcammls.PercipioRectifyRotaData_swiginit(
            self, _pcammls.new_PercipioRectifyRotaData(*args)
        )

    def Data(self):
        r"""Data(self) -> ty_float_vector"""
        return _pcammls.PercipioRectifyRotaData_Data(self)


# Register PercipioRectifyRotaData in _pcammls:
_pcammls.PercipioRectifyRotaData_swigregister(PercipioRectifyRotaData)


class PercipioCalibData(object):
    r"""Proxy of C++ PercipioCalibData class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    __swig_destroy__ = _pcammls.delete_PercipioCalibData

    def __init__(self, *args):
        r"""
        __init__(self) -> PercipioCalibData
        __init__(self, calib) -> PercipioCalibData

        Parameters
        ----------
        calib: TY_CAMERA_CALIB_INFO const

        """
        _pcammls.PercipioCalibData_swiginit(self, _pcammls.new_PercipioCalibData(*args))

    def Width(self):
        r"""Width(self) -> int"""
        return _pcammls.PercipioCalibData_Width(self)

    def Height(self):
        r"""Height(self) -> int"""
        return _pcammls.PercipioCalibData_Height(self)

    def Intrinsic(self):
        r"""Intrinsic(self) -> ty_float_vector"""
        return _pcammls.PercipioCalibData_Intrinsic(self)

    def Extrinsic(self):
        r"""Extrinsic(self) -> ty_float_vector"""
        return _pcammls.PercipioCalibData_Extrinsic(self)

    def Distortion(self):
        r"""Distortion(self) -> ty_float_vector"""
        return _pcammls.PercipioCalibData_Distortion(self)

    def data(self):
        r"""data(self) -> TY_CAMERA_CALIB_INFO"""
        return _pcammls.PercipioCalibData_data(self)


# Register PercipioCalibData in _pcammls:
_pcammls.PercipioCalibData_swigregister(PercipioCalibData)


class image_data(object):
    r"""Proxy of C++ image_data class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    streamID = property(
        _pcammls.image_data_streamID_get,
        _pcammls.image_data_streamID_set,
        doc=r"""streamID""",
    )
    timestamp = property(
        _pcammls.image_data_timestamp_get,
        _pcammls.image_data_timestamp_set,
        doc=r"""timestamp""",
    )
    imageIndex = property(
        _pcammls.image_data_imageIndex_get,
        _pcammls.image_data_imageIndex_set,
        doc=r"""imageIndex""",
    )
    status = property(
        _pcammls.image_data_status_get,
        _pcammls.image_data_status_set,
        doc=r"""status""",
    )
    size = property(
        _pcammls.image_data_size_get, _pcammls.image_data_size_set, doc=r"""size"""
    )
    buffer = property(
        _pcammls.image_data_buffer_get,
        _pcammls.image_data_buffer_set,
        doc=r"""buffer""",
    )
    width = property(
        _pcammls.image_data_width_get, _pcammls.image_data_width_set, doc=r"""width"""
    )
    height = property(
        _pcammls.image_data_height_get,
        _pcammls.image_data_height_set,
        doc=r"""height""",
    )
    pixelFormat = property(
        _pcammls.image_data_pixelFormat_get,
        _pcammls.image_data_pixelFormat_set,
        doc=r"""pixelFormat""",
    )

    def __init__(self, *args):
        r"""
        __init__(self) -> image_data
        __init__(self, image) -> image_data

        Parameters
        ----------
        image: TY_IMAGE_DATA const &

        __init__(self, d) -> image_data

        Parameters
        ----------
        d: image_data const &

        """
        _pcammls.image_data_swiginit(self, _pcammls.new_image_data(*args))

    def resize(self, sz):
        r"""
        resize(self, sz) -> bool

        Parameters
        ----------
        sz: int const

        """
        return _pcammls.image_data_resize(self, sz)

    __swig_destroy__ = _pcammls.delete_image_data

    def Ptr(self):
        r"""Ptr(self) -> void *"""
        return _pcammls.image_data_Ptr(self)

    __U8CX = [TY_PIXEL_FORMAT_MJPG, TY_PIXEL_FORMAT_JPEG]
    __U8C1 = [TY_PIXEL_FORMAT_MONO, TY_PIXEL_FORMAT_BAYER8GB, TY_PIXEL_FORMAT_BAYER8BG]
    __U8C2 = [TY_PIXEL_FORMAT_YVYU, TY_PIXEL_FORMAT_YUYV]
    __U8C3 = [TY_PIXEL_FORMAT_RGB, TY_PIXEL_FORMAT_BGR]
    __U16C1 = [TY_PIXEL_FORMAT_DEPTH16, TY_PIXEL_FORMAT_MONO16]

    def as_nparray(self):
        """
        convert image to numpy array
        *** YOU SHOULD COPY DATA TO YOUR OWN ARRAY BEFORE invoking Enqueuebuffer  ***
        """
        if self.buffer == None or self.width <= 0 or self.height <= 0:
            return None
        pformat = self.pixelFormat
        if pformat in self.__U8CX:
            sz = self.size
            return uint8_t_ARRAY.ptr_as_nparray1d(self.buffer, self.size)
        if pformat in self.__U8C1:
            sz = self.height * self.width
            return uint8_t_ARRAY.ptr_as_nparray2d(self.buffer, self.height, self.width)
        elif pformat in self.__U8C2:
            return uint8_t_ARRAY.ptr_as_nparray3d(
                self.buffer, self.height, self.width, 2
            )
        elif pformat in self.__U8C3:
            sz = self.height * self.width * 3
            return uint8_t_ARRAY.ptr_as_nparray3d(
                self.buffer, self.height, self.width, 3
            )
        elif pformat in self.__U16C1:
            sz = self.height * self.width
            return uint16_t_ARRAY.ptr_as_nparray3d(
                self.buffer, self.height, self.width, 1
            )
        else:
            raise Exception("not supported format {}".format(pformat))
        return None


# Register image_data in _pcammls:
_pcammls.image_data_swigregister(image_data)


class pointcloud_data(object):
    r"""Proxy of C++ pointcloud_data class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    p3d = property(
        _pcammls.pointcloud_data_p3d_get,
        _pcammls.pointcloud_data_p3d_set,
        doc=r"""p3d""",
    )

    def __init__(self, *args):
        r"""
        __init__(self) -> pointcloud_data
        __init__(self, v) -> pointcloud_data

        Parameters
        ----------
        v: TY_VECT_3F const &

        """
        _pcammls.pointcloud_data_swiginit(self, _pcammls.new_pointcloud_data(*args))

    def getX(self):
        r"""getX(self) -> float"""
        return _pcammls.pointcloud_data_getX(self)

    def getY(self):
        r"""getY(self) -> float"""
        return _pcammls.pointcloud_data_getY(self)

    def getZ(self):
        r"""getZ(self) -> float"""
        return _pcammls.pointcloud_data_getZ(self)

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.pointcloud_data_CSize(self)

    __swig_destroy__ = _pcammls.delete_pointcloud_data


# Register pointcloud_data in _pcammls:
_pcammls.pointcloud_data_swigregister(pointcloud_data)


class pointcloud_data_list(object):
    r"""Proxy of C++ pointcloud_data_list class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self, *args):
        r"""
        __init__(self) -> pointcloud_data_list
        __init__(self, list) -> pointcloud_data_list

        Parameters
        ----------
        list: pointcloud_data_list const &

        """
        _pcammls.pointcloud_data_list_swiginit(
            self, _pcammls.new_pointcloud_data_list(*args)
        )

    __swig_destroy__ = _pcammls.delete_pointcloud_data_list

    def resize(self, w, h):
        r"""
        resize(self, w, h)

        Parameters
        ----------
        w: int
        h: int

        """
        return _pcammls.pointcloud_data_list_resize(self, w, h)

    def size(self):
        r"""size(self) -> int"""
        return _pcammls.pointcloud_data_list_size(self)

    def width(self):
        r"""width(self) -> int"""
        return _pcammls.pointcloud_data_list_width(self)

    def height(self):
        r"""height(self) -> int"""
        return _pcammls.pointcloud_data_list_height(self)

    def get_value(self, idx):
        r"""
        get_value(self, idx) -> pointcloud_data

        Parameters
        ----------
        idx: int

        """
        return _pcammls.pointcloud_data_list_get_value(self, idx)

    def getPtr(self):
        r"""getPtr(self) -> void *"""
        return _pcammls.pointcloud_data_list_getPtr(self)

    def as_nparray(self):
        """
        convert point cloud data to numpy array
        """
        if self.size() <= 0 or self.getPtr() == None:
            return None
        return float_ARRAY.ptr_as_nparray3d(
            self.getPtr(), self.height(), self.width(), 3
        )

    def CSize(self):
        r"""CSize(self) -> size_t"""
        return _pcammls.pointcloud_data_list_CSize(self)


# Register pointcloud_data_list in _pcammls:
_pcammls.pointcloud_data_list_swigregister(pointcloud_data_list)


class DeviceEvent(object):
    r"""Proxy of C++ DeviceEvent class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def run(self, handle, event_id):
        r"""
        run(self, handle, event_id) -> int

        Parameters
        ----------
        handle: void *
        event_id: TY_EVENT

        """
        return _pcammls.DeviceEvent_run(self, handle, event_id)

    __swig_destroy__ = _pcammls.delete_DeviceEvent

    def __init__(self):
        r"""
        __init__(self) -> DeviceEvent

        Parameters
        ----------
        self: PyObject *

        """
        if self.__class__ == DeviceEvent:
            _self = None
        else:
            _self = self
        _pcammls.DeviceEvent_swiginit(
            self,
            _pcammls.new_DeviceEvent(
                _self,
            ),
        )

    def __disown__(self):
        self.this.disown()
        _pcammls.disown_DeviceEvent(self)
        return weakref.proxy(self)


# Register DeviceEvent in _pcammls:
_pcammls.DeviceEvent_swigregister(DeviceEvent)


def handler_execute(handle, event_id):
    r"""
    handler_execute(handle, event_id) -> int

    Parameters
    ----------
    handle: TY_DEV_HANDLE
    event_id: TY_EVENT

    """
    return _pcammls.handler_execute(handle, event_id)


def user_device_event_callback(handle, evt):
    r"""
    user_device_event_callback(handle, evt)

    Parameters
    ----------
    handle: TY_DEV_HANDLE
    evt: TY_EVENT_INFO

    """
    return _pcammls.user_device_event_callback(handle, evt)


def percipio_device_callback(event_info, userdata):
    r"""
    percipio_device_callback(event_info, userdata)

    Parameters
    ----------
    event_info: TY_EVENT_INFO *
    userdata: void *

    """
    return _pcammls.percipio_device_callback(event_info, userdata)


class PercipioAecROI(object):
    r"""Proxy of C++ PercipioAecROI class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    _x = property(
        _pcammls.PercipioAecROI__x_get, _pcammls.PercipioAecROI__x_set, doc=r"""_x"""
    )
    _y = property(
        _pcammls.PercipioAecROI__y_get, _pcammls.PercipioAecROI__y_set, doc=r"""_y"""
    )
    _w = property(
        _pcammls.PercipioAecROI__w_get, _pcammls.PercipioAecROI__w_set, doc=r"""_w"""
    )
    _h = property(
        _pcammls.PercipioAecROI__h_get, _pcammls.PercipioAecROI__h_set, doc=r"""_h"""
    )

    def __init__(self, x, y, w, h):
        r"""
        __init__(self, x, y, w, h) -> PercipioAecROI

        Parameters
        ----------
        x: int32_t
        y: int32_t
        w: int32_t
        h: int32_t

        """
        _pcammls.PercipioAecROI_swiginit(self, _pcammls.new_PercipioAecROI(x, y, w, h))

    __swig_destroy__ = _pcammls.delete_PercipioAecROI


# Register PercipioAecROI in _pcammls:
_pcammls.PercipioAecROI_swigregister(PercipioAecROI)
cvar = _pcammls.cvar


class DevParamByteArray(object):
    r"""Proxy of C++ DevParamByteArray class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    m_data = property(
        _pcammls.DevParamByteArray_m_data_get,
        _pcammls.DevParamByteArray_m_data_set,
        doc=r"""m_data""",
    )
    real_size = property(
        _pcammls.DevParamByteArray_real_size_get,
        _pcammls.DevParamByteArray_real_size_set,
        doc=r"""real_size""",
    )

    def __init__(self):
        r"""__init__(self) -> DevParamByteArray"""
        _pcammls.DevParamByteArray_swiginit(self, _pcammls.new_DevParamByteArray())

    __swig_destroy__ = _pcammls.delete_DevParamByteArray


# Register DevParamByteArray in _pcammls:
_pcammls.DevParamByteArray_swigregister(DevParamByteArray)


class DevParamStruct(object):
    r"""Proxy of C++ DevParamStruct class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    m_data = property(
        _pcammls.DevParamStruct_m_data_get,
        _pcammls.DevParamStruct_m_data_set,
        doc=r"""m_data""",
    )
    real_size = property(
        _pcammls.DevParamStruct_real_size_get,
        _pcammls.DevParamStruct_real_size_set,
        doc=r"""real_size""",
    )

    def __init__(self):
        r"""__init__(self) -> DevParamStruct"""
        _pcammls.DevParamStruct_swiginit(self, _pcammls.new_DevParamStruct())

    __swig_destroy__ = _pcammls.delete_DevParamStruct


# Register DevParamStruct in _pcammls:
_pcammls.DevParamStruct_swigregister(DevParamStruct)


class DevParamDataInt(object):
    r"""Proxy of C++ DevParamDataInt class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    value = property(
        _pcammls.DevParamDataInt_value_get,
        _pcammls.DevParamDataInt_value_set,
        doc=r"""value""",
    )
    range = property(
        _pcammls.DevParamDataInt_range_get,
        _pcammls.DevParamDataInt_range_set,
        doc=r"""range""",
    )

    def __init__(self):
        r"""__init__(self) -> DevParamDataInt"""
        _pcammls.DevParamDataInt_swiginit(self, _pcammls.new_DevParamDataInt())

    __swig_destroy__ = _pcammls.delete_DevParamDataInt


# Register DevParamDataInt in _pcammls:
_pcammls.DevParamDataInt_swigregister(DevParamDataInt)


class DevParamDataEnum(object):
    r"""Proxy of C++ DevParamDataEnum class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    value = property(
        _pcammls.DevParamDataEnum_value_get,
        _pcammls.DevParamDataEnum_value_set,
        doc=r"""value""",
    )
    entryCount = property(
        _pcammls.DevParamDataEnum_entryCount_get,
        _pcammls.DevParamDataEnum_entryCount_set,
        doc=r"""entryCount""",
    )
    list = property(
        _pcammls.DevParamDataEnum_list_get,
        _pcammls.DevParamDataEnum_list_set,
        doc=r"""list""",
    )

    def __init__(self):
        r"""__init__(self) -> DevParamDataEnum"""
        _pcammls.DevParamDataEnum_swiginit(self, _pcammls.new_DevParamDataEnum())

    __swig_destroy__ = _pcammls.delete_DevParamDataEnum


# Register DevParamDataEnum in _pcammls:
_pcammls.DevParamDataEnum_swigregister(DevParamDataEnum)


class DevParamDataFloat(object):
    r"""Proxy of C++ DevParamDataFloat class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    value = property(
        _pcammls.DevParamDataFloat_value_get,
        _pcammls.DevParamDataFloat_value_set,
        doc=r"""value""",
    )
    range = property(
        _pcammls.DevParamDataFloat_range_get,
        _pcammls.DevParamDataFloat_range_set,
        doc=r"""range""",
    )

    def __init__(self):
        r"""__init__(self) -> DevParamDataFloat"""
        _pcammls.DevParamDataFloat_swiginit(self, _pcammls.new_DevParamDataFloat())

    __swig_destroy__ = _pcammls.delete_DevParamDataFloat


# Register DevParamDataFloat in _pcammls:
_pcammls.DevParamDataFloat_swigregister(DevParamDataFloat)


class DevParamDataBool(object):
    r"""Proxy of C++ DevParamDataBool class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    value = property(
        _pcammls.DevParamDataBool_value_get,
        _pcammls.DevParamDataBool_value_set,
        doc=r"""value""",
    )

    def __init__(self):
        r"""__init__(self) -> DevParamDataBool"""
        _pcammls.DevParamDataBool_swiginit(self, _pcammls.new_DevParamDataBool())

    __swig_destroy__ = _pcammls.delete_DevParamDataBool


# Register DevParamDataBool in _pcammls:
_pcammls.DevParamDataBool_swigregister(DevParamDataBool)


class DevParamData(object):
    r"""Proxy of C++ DevParamData class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    b_param = property(
        _pcammls.DevParamData_b_param_get,
        _pcammls.DevParamData_b_param_set,
        doc=r"""b_param""",
    )
    m_param = property(
        _pcammls.DevParamData_m_param_get,
        _pcammls.DevParamData_m_param_set,
        doc=r"""m_param""",
    )
    u32_param = property(
        _pcammls.DevParamData_u32_param_get,
        _pcammls.DevParamData_u32_param_set,
        doc=r"""u32_param""",
    )
    f_param = property(
        _pcammls.DevParamData_f_param_get,
        _pcammls.DevParamData_f_param_set,
        doc=r"""f_param""",
    )
    byteArray = property(
        _pcammls.DevParamData_byteArray_get,
        _pcammls.DevParamData_byteArray_set,
        doc=r"""byteArray""",
    )
    st_param = property(
        _pcammls.DevParamData_st_param_get,
        _pcammls.DevParamData_st_param_set,
        doc=r"""st_param""",
    )

    def __init__(self):
        r"""__init__(self) -> DevParamData"""
        _pcammls.DevParamData_swiginit(self, _pcammls.new_DevParamData())

    __swig_destroy__ = _pcammls.delete_DevParamData


# Register DevParamData in _pcammls:
_pcammls.DevParamData_swigregister(DevParamData)


class DevParam(object):
    r"""Proxy of C++ DevParam class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr
    type = property(
        _pcammls.DevParam_type_get, _pcammls.DevParam_type_set, doc=r"""type"""
    )
    data = property(
        _pcammls.DevParam_data_get, _pcammls.DevParam_data_set, doc=r"""data"""
    )

    def isEmpty(self):
        r"""isEmpty(self) -> bool"""
        return _pcammls.DevParam_isEmpty(self)

    def toBool(self):
        r"""toBool(self) -> bool"""
        return _pcammls.DevParam_toBool(self)

    def toInt(self):
        r"""toInt(self) -> int"""
        return _pcammls.DevParam_toInt(self)

    def toEnum(self):
        r"""toEnum(self) -> uint32_t"""
        return _pcammls.DevParam_toEnum(self)

    def toFloat(self):
        r"""toFloat(self) -> float"""
        return _pcammls.DevParam_toFloat(self)

    def mMin(self):
        r"""mMin(self) -> int"""
        return _pcammls.DevParam_mMin(self)

    def mMax(self):
        r"""mMax(self) -> int"""
        return _pcammls.DevParam_mMax(self)

    def mInc(self):
        r"""mInc(self) -> int"""
        return _pcammls.DevParam_mInc(self)

    def fMin(self):
        r"""fMin(self) -> int"""
        return _pcammls.DevParam_fMin(self)

    def fMax(self):
        r"""fMax(self) -> int"""
        return _pcammls.DevParam_fMax(self)

    def fInc(self):
        r"""fInc(self) -> int"""
        return _pcammls.DevParam_fInc(self)

    def eList(self):
        r"""eList(self) -> ty_enum_vector"""
        return _pcammls.DevParam_eList(self)

    def toByteArray(self):
        r"""toByteArray(self) -> ty_uint8_vector"""
        return _pcammls.DevParam_toByteArray(self)

    def toArray(self):
        r"""toArray(self) -> ty_int32_vector"""
        return _pcammls.DevParam_toArray(self)

    def __init__(self):
        r"""__init__(self) -> DevParam"""
        _pcammls.DevParam_swiginit(self, _pcammls.new_DevParam())

    __swig_destroy__ = _pcammls.delete_DevParam


# Register DevParam in _pcammls:
_pcammls.DevParam_swigregister(DevParam)


class PercipioSDK(object):
    r"""Proxy of C++ PercipioSDK class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self):
        r"""__init__(self) -> PercipioSDK"""
        _pcammls.PercipioSDK_swiginit(self, _pcammls.new_PercipioSDK())

    __swig_destroy__ = _pcammls.delete_PercipioSDK

    def ListDevice(self):
        r"""ListDevice(self) -> ty_dev_info_vector"""
        return _pcammls.PercipioSDK_ListDevice(self)

    def IPv4StringToInt(self, ip):
        r"""
        IPv4StringToInt(self, ip) -> int

        Parameters
        ----------
        ip: char *

        """
        return _pcammls.PercipioSDK_IPv4StringToInt(self, ip)

    def DevParamFromInt(self, val):
        r"""
        DevParamFromInt(self, val) -> DevParam

        Parameters
        ----------
        val: int

        """
        return _pcammls.PercipioSDK_DevParamFromInt(self, val)

    def DevParamFromEnum(self, val):
        r"""
        DevParamFromEnum(self, val) -> DevParam

        Parameters
        ----------
        val: uint32_t

        """
        return _pcammls.PercipioSDK_DevParamFromEnum(self, val)

    def DevParamFromFloat(self, val):
        r"""
        DevParamFromFloat(self, val) -> DevParam

        Parameters
        ----------
        val: float

        """
        return _pcammls.PercipioSDK_DevParamFromFloat(self, val)

    def DevParamFromBool(self, val):
        r"""
        DevParamFromBool(self, val) -> DevParam

        Parameters
        ----------
        val: bool

        """
        return _pcammls.PercipioSDK_DevParamFromBool(self, val)

    def DevParamFromByteArray(self, val):
        r"""
        DevParamFromByteArray(self, val) -> DevParam

        Parameters
        ----------
        val: std::vector< unsigned char,std::allocator< unsigned char > >

        """
        return _pcammls.PercipioSDK_DevParamFromByteArray(self, val)

    def DevParamFromPercipioAecROI(self, roi):
        r"""
        DevParamFromPercipioAecROI(self, roi) -> DevParam

        Parameters
        ----------
        roi: PercipioAecROI

        """
        return _pcammls.PercipioSDK_DevParamFromPercipioAecROI(self, roi)

    def TYGetLastErrorCode(self):
        r"""TYGetLastErrorCode(self) -> int"""
        return _pcammls.PercipioSDK_TYGetLastErrorCode(self)

    def TYGetLastErrorCodedescription(self):
        r"""TYGetLastErrorCodedescription(self) -> char const *"""
        return _pcammls.PercipioSDK_TYGetLastErrorCodedescription(self)

    def Open(self, *args):
        r"""
        Open(self) -> TY_DEV_HANDLE
        Open(self, sn) -> TY_DEV_HANDLE

        Parameters
        ----------
        sn: char const *

        """
        return _pcammls.PercipioSDK_Open(self, *args)

    def OpenDeviceByIP(self, ip):
        r"""
        OpenDeviceByIP(self, ip) -> TY_DEV_HANDLE

        Parameters
        ----------
        ip: char const *

        """
        return _pcammls.PercipioSDK_OpenDeviceByIP(self, ip)

    def isValidHandle(self, handle):
        r"""
        isValidHandle(self, handle) -> bool

        Parameters
        ----------
        handle: TY_DEV_HANDLE const

        """
        return _pcammls.PercipioSDK_isValidHandle(self, handle)

    def Close(self, handle):
        r"""
        Close(self, handle)

        Parameters
        ----------
        handle: TY_DEV_HANDLE const

        """
        return _pcammls.PercipioSDK_Close(self, handle)

    def DeviceWriteDefaultParametersFromJSFile(self, handle, file):
        r"""
        DeviceWriteDefaultParametersFromJSFile(self, handle, file) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        file: char const *

        """
        return _pcammls.PercipioSDK_DeviceWriteDefaultParametersFromJSFile(
            self, handle, file
        )

    def DeviceLoadDefaultParameters(self, handle):
        r"""
        DeviceLoadDefaultParameters(self, handle) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const

        """
        return _pcammls.PercipioSDK_DeviceLoadDefaultParameters(self, handle)

    def DeviceClearDefaultParameters(self, handle):
        r"""
        DeviceClearDefaultParameters(self, handle) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const

        """
        return _pcammls.PercipioSDK_DeviceClearDefaultParameters(self, handle)

    def DeviceRegiststerCallBackEvent(self, handler):
        r"""
        DeviceRegiststerCallBackEvent(self, handler) -> bool

        Parameters
        ----------
        handler: DeviceEventHandle

        """
        return _pcammls.PercipioSDK_DeviceRegiststerCallBackEvent(self, handler)

    def DeviceSetParameter(self, *args):
        r"""
        DeviceSetParameter(self, handle, comp, feat, param) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        comp: int32_t const
        feat: TY_FEATURE_ID const
        param: DevParam

        DeviceSetParameter(self, handle, comp, feat, param) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        comp: uint32_t const
        feat: TY_FEATURE_ID const
        param: DevParam

        """
        return _pcammls.PercipioSDK_DeviceSetParameter(self, *args)

    def DeviceGetParameter(self, *args):
        r"""
        DeviceGetParameter(self, handle, comp, feat) -> DevParam

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        comp: int32_t const
        feat: TY_FEATURE_ID const

        DeviceGetParameter(self, handle, comp, feat) -> DevParam

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        comp: uint32_t const
        feat: TY_FEATURE_ID const

        """
        return _pcammls.PercipioSDK_DeviceGetParameter(self, *args)

    def DeviceColorStreamIspEnable(self, handle, enable):
        r"""
        DeviceColorStreamIspEnable(self, handle, enable) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        enable: bool

        """
        return _pcammls.PercipioSDK_DeviceColorStreamIspEnable(self, handle, enable)

    def DeviceHasStream(self, handle, stream):
        r"""
        DeviceHasStream(self, handle, stream) -> bool

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        stream: PERCIPIO_STREAM_ID const

        """
        return _pcammls.PercipioSDK_DeviceHasStream(self, handle, stream)

    def DeviceStreamEnable(self, handle, stream):
        r"""
        DeviceStreamEnable(self, handle, stream) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        stream: PERCIPIO_STREAM_ID const

        """
        return _pcammls.PercipioSDK_DeviceStreamEnable(self, handle, stream)

    def DeviceStreamDisable(self, handle, stream):
        r"""
        DeviceStreamDisable(self, handle, stream) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        stream: PERCIPIO_STREAM_ID const

        """
        return _pcammls.PercipioSDK_DeviceStreamDisable(self, handle, stream)

    def DeviceStreamFormatDump(self, handle, stream):
        r"""
        DeviceStreamFormatDump(self, handle, stream) -> ty_enum_vector

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        stream: PERCIPIO_STREAM_ID const

        """
        return _pcammls.PercipioSDK_DeviceStreamFormatDump(self, handle, stream)

    def DeviceStreamFormatConfig(self, handle, stream, fmt):
        r"""
        DeviceStreamFormatConfig(self, handle, stream, fmt) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        stream: PERCIPIO_STREAM_ID const
        fmt: TY_ENUM_ENTRY const

        """
        return _pcammls.PercipioSDK_DeviceStreamFormatConfig(self, handle, stream, fmt)

    def Value(self, fmt):
        r"""
        Value(self, fmt) -> uint32_t

        Parameters
        ----------
        fmt: TY_ENUM_ENTRY const &

        """
        return _pcammls.PercipioSDK_Value(self, fmt)

    def Width(self, fmt):
        r"""
        Width(self, fmt) -> int

        Parameters
        ----------
        fmt: TY_ENUM_ENTRY const &

        """
        return _pcammls.PercipioSDK_Width(self, fmt)

    def Height(self, fmt):
        r"""
        Height(self, fmt) -> int

        Parameters
        ----------
        fmt: TY_ENUM_ENTRY const &

        """
        return _pcammls.PercipioSDK_Height(self, fmt)

    def Description(self, fmt):
        r"""
        Description(self, fmt) -> char const *

        Parameters
        ----------
        fmt: TY_ENUM_ENTRY const &

        """
        return _pcammls.PercipioSDK_Description(self, fmt)

    def DeviceStreamOn(self, handle):
        r"""
        DeviceStreamOn(self, handle) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const

        """
        return _pcammls.PercipioSDK_DeviceStreamOn(self, handle)

    def DeviceStreamRead(self, handle, timeout):
        r"""
        DeviceStreamRead(self, handle, timeout) -> ty_image_data_vector

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        timeout: int

        """
        return _pcammls.PercipioSDK_DeviceStreamRead(self, handle, timeout)

    def DeviceStreamOff(self, handle):
        r"""
        DeviceStreamOff(self, handle) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const

        """
        return _pcammls.PercipioSDK_DeviceStreamOff(self, handle)

    def DeviceReadCurrentEnumData(self, handle, stream, enum_desc):
        r"""
        DeviceReadCurrentEnumData(self, handle, stream, enum_desc) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        stream: PERCIPIO_STREAM_ID const
        enum_desc: TY_ENUM_ENTRY &

        """
        return _pcammls.PercipioSDK_DeviceReadCurrentEnumData(
            self, handle, stream, enum_desc
        )

    def DeviceReadCalibData(self, handle, stream):
        r"""
        DeviceReadCalibData(self, handle, stream) -> PercipioCalibData

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        stream: PERCIPIO_STREAM_ID const

        """
        return _pcammls.PercipioSDK_DeviceReadCalibData(self, handle, stream)

    def DeviceReadRectifiedIntrData(self, handle, stream):
        r"""
        DeviceReadRectifiedIntrData(self, handle, stream) -> PercipioRectifyIntrData

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        stream: PERCIPIO_STREAM_ID const

        """
        return _pcammls.PercipioSDK_DeviceReadRectifiedIntrData(self, handle, stream)

    def DeviceReadRectifiedRotationData(self, handle, stream):
        r"""
        DeviceReadRectifiedRotationData(self, handle, stream) -> PercipioRectifyRotaData

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        stream: PERCIPIO_STREAM_ID const

        """
        return _pcammls.PercipioSDK_DeviceReadRectifiedRotationData(
            self, handle, stream
        )

    def DeviceReadCalibDepthScaleUnit(self, handle):
        r"""
        DeviceReadCalibDepthScaleUnit(self, handle) -> float const

        Parameters
        ----------
        handle: TY_DEV_HANDLE const

        """
        return _pcammls.PercipioSDK_DeviceReadCalibDepthScaleUnit(self, handle)

    def DeviceControlReadTemperature(self, handle, ID):
        r"""
        DeviceControlReadTemperature(self, handle, ID) -> float

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        ID: TY_TEMPERATURE_ID

        """
        return _pcammls.PercipioSDK_DeviceControlReadTemperature(self, handle, ID)

    def DeviceControlTriggerModeEnable(self, handle, enable):
        r"""
        DeviceControlTriggerModeEnable(self, handle, enable) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        enable: int const

        """
        return _pcammls.PercipioSDK_DeviceControlTriggerModeEnable(self, handle, enable)

    def DeviceControlTriggerModeSendTriggerSignal(self, handle):
        r"""
        DeviceControlTriggerModeSendTriggerSignal(self, handle) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const

        """
        return _pcammls.PercipioSDK_DeviceControlTriggerModeSendTriggerSignal(
            self, handle
        )

    def DeviceControlLaserPowerAutoControlEnable(self, handle, enable):
        r"""
        DeviceControlLaserPowerAutoControlEnable(self, handle, enable) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        enable: bool

        """
        return _pcammls.PercipioSDK_DeviceControlLaserPowerAutoControlEnable(
            self, handle, enable
        )

    def DeviceControlLaserPowerConfig(self, handle, laser):
        r"""
        DeviceControlLaserPowerConfig(self, handle, laser) -> int

        Parameters
        ----------
        handle: TY_DEV_HANDLE const
        laser: int

        """
        return _pcammls.PercipioSDK_DeviceControlLaserPowerConfig(self, handle, laser)

    def DeviceStreamDepthRender(self, src, dst):
        r"""
        DeviceStreamDepthRender(self, src, dst) -> int

        Parameters
        ----------
        src: image_data const &
        dst: image_data &

        """
        return _pcammls.PercipioSDK_DeviceStreamDepthRender(self, src, dst)

    def DeviceStreamIRRender(self, src, dst):
        r"""
        DeviceStreamIRRender(self, src, dst) -> int

        Parameters
        ----------
        src: image_data const &
        dst: image_data &

        """
        return _pcammls.PercipioSDK_DeviceStreamIRRender(self, src, dst)

    def DeviceStreamImageDecode(self, src, dst):
        r"""
        DeviceStreamImageDecode(self, src, dst) -> int

        Parameters
        ----------
        src: image_data const &
        dst: image_data &

        """
        return _pcammls.PercipioSDK_DeviceStreamImageDecode(self, src, dst)

    def DeviceStreamMapDepthImageToPoint3D(self, depth, calib_data, scale, p3d):
        r"""
        DeviceStreamMapDepthImageToPoint3D(self, depth, calib_data, scale, p3d) -> int

        Parameters
        ----------
        depth: image_data const &
        calib_data: PercipioCalibData const &
        scale: float
        p3d: pointcloud_data_list &

        """
        return _pcammls.PercipioSDK_DeviceStreamMapDepthImageToPoint3D(
            self, depth, calib_data, scale, p3d
        )

    def DeviceStreamDoUndistortion(self, calib_data, src, dst):
        r"""
        DeviceStreamDoUndistortion(self, calib_data, src, dst) -> int

        Parameters
        ----------
        calib_data: PercipioCalibData const &
        src: image_data const &
        dst: image_data &

        """
        return _pcammls.PercipioSDK_DeviceStreamDoUndistortion(
            self, calib_data, src, dst
        )

    def DeviceStreamMapDepthImageToColorCoordinate(
        self, depth_calib, srcDepth, scale, color_calib, targetW, targetH, dstDepth
    ):
        r"""
        DeviceStreamMapDepthImageToColorCoordinate(self, depth_calib, srcDepth, scale, color_calib, targetW, targetH, dstDepth) -> int

        Parameters
        ----------
        depth_calib: PercipioCalibData const &
        srcDepth: image_data const &
        scale: float const
        color_calib: PercipioCalibData const &
        targetW: int const
        targetH: int const
        dstDepth: image_data &

        """
        return _pcammls.PercipioSDK_DeviceStreamMapDepthImageToColorCoordinate(
            self, depth_calib, srcDepth, scale, color_calib, targetW, targetH, dstDepth
        )

    def DeviceStreamMapRGBImageToDepthCoordinate(
        self, depth_calib, srcDepth, scale, color_calib, srcColor, dstColor
    ):
        r"""
        DeviceStreamMapRGBImageToDepthCoordinate(self, depth_calib, srcDepth, scale, color_calib, srcColor, dstColor) -> int

        Parameters
        ----------
        depth_calib: PercipioCalibData const &
        srcDepth: image_data const &
        scale: float const
        color_calib: PercipioCalibData const &
        srcColor: image_data const &
        dstColor: image_data &

        """
        return _pcammls.PercipioSDK_DeviceStreamMapRGBImageToDepthCoordinate(
            self, depth_calib, srcDepth, scale, color_calib, srcColor, dstColor
        )

    def DeviceStreamDepthSpeckleFilter(self, max_spc_size, max_spc_diff, image):
        r"""
        DeviceStreamDepthSpeckleFilter(self, max_spc_size, max_spc_diff, image) -> int

        Parameters
        ----------
        max_spc_size: int
        max_spc_diff: int
        image: image_data &

        """
        return _pcammls.PercipioSDK_DeviceStreamDepthSpeckleFilter(
            self, max_spc_size, max_spc_diff, image
        )


# Register PercipioSDK in _pcammls:
_pcammls.PercipioSDK_swigregister(PercipioSDK)

HUFFMAN = _pcammls.HUFFMAN

MAX_STORAGE_SIZE = _pcammls.MAX_STORAGE_SIZE


def IsBayerColor(pixelFormat):
    r"""
    IsBayerColor(pixelFormat) -> bool

    Parameters
    ----------
    pixelFormat: int32_t

    """
    return _pcammls.IsBayerColor(pixelFormat)


def parseIRCsiRaw10(src, dst):
    r"""
    parseIRCsiRaw10(src, dst) -> int

    Parameters
    ----------
    src: image_data const &
    dst: image_data &

    """
    return _pcammls.parseIRCsiRaw10(src, dst)


def parseIRCsiRaw12(src, dst):
    r"""
    parseIRCsiRaw12(src, dst) -> int

    Parameters
    ----------
    src: image_data const &
    dst: image_data &

    """
    return _pcammls.parseIRCsiRaw12(src, dst)


def parseIrFrame(src, dst):
    r"""
    parseIrFrame(src, dst) -> int

    Parameters
    ----------
    src: image_data const &
    dst: image_data &

    """
    return _pcammls.parseIrFrame(src, dst)


def parseColorFrame(src, dst):
    r"""
    parseColorFrame(src, dst) -> int

    Parameters
    ----------
    src: image_data const &
    dst: image_data &

    """
    return _pcammls.parseColorFrame(src, dst)


def getLocalTime():
    r"""getLocalTime() -> char *"""
    return _pcammls.getLocalTime()


def getSystemTime():
    r"""getSystemTime() -> uint64_t"""
    return _pcammls.getSystemTime()


def MSleep(ms):
    r"""
    MSleep(ms)

    Parameters
    ----------
    ms: uint32_t

    """
    return _pcammls.MSleep(ms)


def colorFormatName(fmt):
    r"""
    colorFormatName(fmt) -> char const *

    Parameters
    ----------
    fmt: TY_PIXEL_FORMAT

    """
    return _pcammls.colorFormatName(fmt)


def TYImageInFrame(frame, comp):
    r"""
    TYImageInFrame(frame, comp) -> TY_IMAGE_DATA

    Parameters
    ----------
    frame: TY_FRAME_DATA const &
    comp: TY_COMPONENT_ID const

    """
    return _pcammls.TYImageInFrame(frame, comp)


def updateThreadFunc(userdata):
    r"""
    updateThreadFunc(userdata) -> void *

    Parameters
    ----------
    userdata: void *

    """
    return _pcammls.updateThreadFunc(userdata)


def updateDevicesParallel(ifaces, timeout=2000):
    r"""
    updateDevicesParallel(ifaces, timeout=2000) -> TY_STATUS

    Parameters
    ----------
    ifaces: std::vector< TY_INTERFACE_HANDLE,std::allocator< TY_INTERFACE_HANDLE > > &
    timeout: uint64_t

    """
    return _pcammls.updateDevicesParallel(ifaces, timeout)


def selectDevice(iface, ID, IP, deviceNum):
    r"""
    selectDevice(iface, ID, IP, deviceNum) -> TY_STATUS

    Parameters
    ----------
    iface: TY_INTERFACE_TYPE
    ID: std::string const &
    IP: std::string const &
    deviceNum: uint32_t

    """
    return _pcammls.selectDevice(iface, ID, IP, deviceNum)


def get_feature_enum_list(handle, compID, featID, feature_info):
    r"""
    get_feature_enum_list(handle, compID, featID, feature_info) -> TY_STATUS

    Parameters
    ----------
    handle: TY_DEV_HANDLE
    compID: TY_COMPONENT_ID
    featID: TY_FEATURE_ID
    feature_info: std::vector< TY_ENUM_ENTRY,std::allocator< TY_ENUM_ENTRY > > &

    """
    return _pcammls.get_feature_enum_list(handle, compID, featID, feature_info)


def get_image_mode(handle, compID, image_mode, idx):
    r"""
    get_image_mode(handle, compID, image_mode, idx) -> TY_STATUS

    Parameters
    ----------
    handle: TY_DEV_HANDLE
    compID: TY_COMPONENT_ID
    image_mode: TY_IMAGE_MODE &
    idx: int

    """
    return _pcammls.get_image_mode(handle, compID, image_mode, idx)


def get_default_image_mode(handle, compID, image_mode):
    r"""
    get_default_image_mode(handle, compID, image_mode) -> TY_STATUS

    Parameters
    ----------
    handle: TY_DEV_HANDLE
    compID: TY_COMPONENT_ID
    image_mode: TY_IMAGE_MODE &

    """
    return _pcammls.get_default_image_mode(handle, compID, image_mode)


class TYThread(object):
    r"""Proxy of C++ TYThread class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self):
        r"""__init__(self) -> TYThread"""
        _pcammls.TYThread_swiginit(self, _pcammls.new_TYThread())

    __swig_destroy__ = _pcammls.delete_TYThread

    def create(self, cb, arg):
        r"""
        create(self, cb, arg) -> int

        Parameters
        ----------
        cb: TYThread::Callback_t
        arg: void *

        """
        return _pcammls.TYThread_create(self, cb, arg)

    def destroy(self):
        r"""destroy(self) -> int"""
        return _pcammls.TYThread_destroy(self)


# Register TYThread in _pcammls:
_pcammls.TYThread_swigregister(TYThread)


class TYThreadImpl(object):
    r"""Proxy of C++ TYThreadImpl class."""

    thisown = property(
        lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag"
    )
    __repr__ = _swig_repr

    def __init__(self):
        r"""__init__(self) -> TYThreadImpl"""
        _pcammls.TYThreadImpl_swiginit(self, _pcammls.new_TYThreadImpl())

    def create(self, cb, arg):
        r"""
        create(self, cb, arg) -> int

        Parameters
        ----------
        cb: TYThread::Callback_t
        arg: void *

        """
        return _pcammls.TYThreadImpl_create(self, cb, arg)

    def destroy(self):
        r"""destroy(self) -> int"""
        return _pcammls.TYThreadImpl_destroy(self)

    __swig_destroy__ = _pcammls.delete_TYThreadImpl


# Register TYThreadImpl in _pcammls:
_pcammls.TYThreadImpl_swigregister(TYThreadImpl)


def __TYCompareFirmwareVersion(info, major, minor):
    r"""
    __TYCompareFirmwareVersion(info, major, minor) -> int

    Parameters
    ----------
    info: TY_DEVICE_BASE_INFO const &
    major: int
    minor: int

    """
    return _pcammls.__TYCompareFirmwareVersion(info, major, minor)


def __TYDetectOldVer21ColorCam(dev_handle, is_v21_color_device):
    r"""
    __TYDetectOldVer21ColorCam(dev_handle, is_v21_color_device) -> TY_STATUS

    Parameters
    ----------
    dev_handle: TY_DEV_HANDLE
    is_v21_color_device: bool *

    """
    return _pcammls.__TYDetectOldVer21ColorCam(dev_handle, is_v21_color_device)


def __TYParseSizeFromImageMode(mode, image_size):
    r"""
    __TYParseSizeFromImageMode(mode, image_size)

    Parameters
    ----------
    mode: TY_IMAGE_MODE
    image_size: int *

    """
    return _pcammls.__TYParseSizeFromImageMode(mode, image_size)


def ColorIspInitSetting(isp_handle, dev_handle):
    r"""
    ColorIspInitSetting(isp_handle, dev_handle) -> TY_STATUS

    Parameters
    ----------
    isp_handle: TY_ISP_HANDLE
    dev_handle: TY_DEV_HANDLE

    """
    return _pcammls.ColorIspInitSetting(isp_handle, dev_handle)


def ColorIspInitAutoExposure(isp_handle, dev_handle):
    r"""
    ColorIspInitAutoExposure(isp_handle, dev_handle) -> TY_STATUS

    Parameters
    ----------
    isp_handle: TY_ISP_HANDLE
    dev_handle: TY_DEV_HANDLE

    """
    return _pcammls.ColorIspInitAutoExposure(isp_handle, dev_handle)


def ColorIspShowSupportedFeatures(handle):
    r"""
    ColorIspShowSupportedFeatures(handle) -> TY_STATUS

    Parameters
    ----------
    handle: TY_ISP_HANDLE

    """
    return _pcammls.ColorIspShowSupportedFeatures(handle)
