import rtde_control
import rtde_receive
from robo_ctl.robotiq_gripper_control import RobotiqGripper

# --- Begin of left ---

# ROBOT_HOST = "*************" # UR - Right 
ROBOT_HOST = "*************" # UR - left 

rtde_c_left = rtde_control.RTDEControlInterface(ROBOT_HOST)
rtde_r_left = rtde_receive.RTDEReceiveInterface(ROBOT_HOST)

# --- Init Left Gripper ---
gripper_left = RobotiqGripper(rtde_c_left)
gripper_left.activate()
gripper_left.open()

velocity = 0.70
acceleration = 0.05

# Get current TCP pose and modify it
left_pose = rtde_r_left.getActualTCPPose()
left_pose[1] -= 0.007
left_pose[2] += 0.16
rtde_c_left.moveL(left_pose, velocity, acceleration)   # [x, y, z, Rx, Ry, Rz] 轴角

tcp_pose = rtde_r_left.getActualTCPPose()
print("--- LEFT TCP Pose ---", tcp_pose)
# --- Left gripping ---
gripper_left.close()
# --- End of left ---