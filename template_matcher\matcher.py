import cv2
import numpy as np

def feature_based_matching_example():
    """
    Demonstrates feature-based image matching.
    Uses ORB feature detector and BFMatcher for feature matching.
    The result image is saved directly to a file.
    """
    print("Loading images...")

    # --- Create more complex template and scene images for better keypoint detection ---

    # Template image: A smaller image with some distinct shapes/patterns
    # template = np.zeros((150, 150, 3), dtype=np.uint8)
    # # Draw a white circle
    # cv2.circle(template, (75, 75), 40, (255, 255, 255), -1)
    # # Draw a red triangle
    # pts = np.array([[50, 100], [100, 100], [75, 50]], np.int32)
    # pts = pts.reshape((-1, 1, 2))
    # cv2.fillPoly(template, [pts], (0, 0, 255))
    # # Draw a blue line
    # cv2.line(template, (20, 20), (130, 130), (255, 0, 0), 3)
    # cv2.imwrite("template.png", template)
    # template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
    # print("Complex template image created.")

    # # Scene image: A larger image containing the template and some additional elements
    # scene = np.zeros((300, 300, 3), dtype=np.uint8)
    # # Place the template in the scene (e.g., at top-left corner with some offset)
    # scene[50:50+template.shape[0], 50:50+template.shape[1]] = template
    # # Add some other noise or shapes to the scene
    # cv2.rectangle(scene, (200, 200), (280, 280), (0, 255, 0), -1) # Green square
    # cv2.circle(scene, (250, 80), 30, (0, 255, 255), -1) # Yellow circle
    # cv2.imwrite("scene.png", scene)
    # scene_gray = cv2.cvtColor(scene, cv2.COLOR_BGR2GRAY)
    # print("Complex scene image created.")

    # In a real application, you would load images like this:
    template = cv2.imread('data/2d_images/cropped-template_2.png')
    scene = cv2.imread('data/2d_images/Image_20250707113725896.bmp')
    if template is None or scene is None:
        print("Error: Could not load images. Check file paths.")
        return
    template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
    scene_gray = cv2.cvtColor(scene, cv2.COLOR_BGR2GRAY)

    # ====================================== BEGIN of ORB =============================================
    # # --- 1. Initialize ORB feature detector ---
    # # ORB (Oriented FAST and Rotated BRIEF) is an efficient feature detector,
    # # suitable for real-time applications and is patent-free.
    # orb = cv2.ORB_create()
    # print("ORB feature detector initialized.")

    # # --- 2. Find keypoints and descriptors ---
    # # kp is a list of keypoints, des is a list of descriptors.
    # # Keypoints are points in the image with unique features (e.g., corners, edges).
    # # Descriptors are vector representations of the area around the keypoint, used to compare keypoints in different images.
    # kp1, des1 = orb.detectAndCompute(template_gray, None)
    # kp2, des2 = orb.detectAndCompute(scene_gray, None)
    # print(f"Found {len(kp1)} keypoints in the template.")
    # print(f"Found {len(kp2)} keypoints in the scene.")

    # # Check if enough keypoints were found
    # if des1 is None or des2 is None or len(des1) < 2 or len(des2) < 2:
    #     print("Error: Not enough keypoints detected for matching. Ensure image content is rich enough.")
    #     return # Return without saving if matching cannot proceed

    # # --- 3. Use BFMatcher (Brute-Force Matcher) for matching ---
    # # BFMatcher attempts to match each descriptor in one set with all descriptors in another set.
    # # NORM_HAMMING is suitable for binary descriptors (like ORB).
    # bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
    # print("BFMatcher initialized.")
    # ====================================== END of ORB =============================================


    # ====================================== Begin of SIFT =============================================
    # --- 1. Initialize SIFT feature detector ---
    # SIFT (Scale-Invariant Feature Transform) is a more robust feature detector
    # compared to ORB, especially for scale and rotation changes.
    sift = cv2.SIFT_create()
    print("SIFT feature detector initialized.")

    # --- 2. Find keypoints and descriptors ---
    # kp is a list of keypoints, des is a list of descriptors.
    # Keypoints are points in the image with unique features (e.g., corners, edges).
    # Descriptors are vector representations of the area around the keypoint, used to compare keypoints in different images.
    kp1, des1 = sift.detectAndCompute(template_gray, None)
    kp2, des2 = sift.detectAndCompute(scene_gray, None)
    print(f"Found {len(kp1)} keypoints in the template.")
    print(f"Found {len(kp2)} keypoints in the scene.")

    # Check if enough keypoints were found
    if des1 is None or des2 is None or len(des1) < 2 or len(des2) < 2:
        print("Error: Not enough keypoints detected for matching. Ensure image content is rich enough.")
        return # Return without saving if matching cannot proceed

    # --- 3. Use BFMatcher (Brute-Force Matcher) for matching ---
    # BFMatcher attempts to match each descriptor in one set with all descriptors in another set.
    # NORM_L2 is suitable for float-based descriptors (like SIFT).
    bf = cv2.BFMatcher(cv2.NORM_L2, crossCheck=True)
    print("BFMatcher initialized.")

    # ====================================== END of SIFT =============================================

    # Perform feature matching
    # matches is a list of DMatch objects, each containing query descriptor index, train descriptor index, distance, etc.
    matches = bf.match(des1, des2)
    print(f"Found {len(matches)} initial matches.")

    # --- 4. Sort and filter matches (optional but recommended) ---
    # Sort matches by distance; smaller distance indicates a better match.
    matches = sorted(matches, key=lambda x: x.distance)

    # Filter out the best N matches (e.g., top 10%)
    # This helps remove bad matches and improve matching accuracy.
    num_good_matches = int(len(matches) * 0.1) # Take the top 10% of matches
    if num_good_matches == 0 and len(matches) > 0:
        num_good_matches = 1 # Keep at least one match (if available)
    good_matches = matches[:num_good_matches]
    print(f"Retained {len(good_matches)} good matches after filtering.")

    # --- 5. Draw matching results ---
    # drawMatches function visualizes keypoints and their matching lines.
    # flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS means not to draw unmatched keypoints.
    img_matches = cv2.drawMatches(template, kp1, scene, kp2, good_matches, None,
                                  flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS)
    print("Matching result image drawn.")


    # --- 6. Find the bounding box of the matched object ---
    # Need at least 4 good matches to find homography
    if len(good_matches) > 4:
        # Extract corresponding keypoints from good matches
        src_pts = np.float32([kp1[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
        dst_pts = np.float32([kp2[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)

        # Find the homography matrix (perspective transformation)
        # H: 3x3 transformation matrix
        # mask: indicates which points are inliers
        H, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)

        if H is not None:
            # Get the corners of the template image
            h, w, _ = template.shape
            pts = np.float32([[0, 0], [0, h - 1], [w - 1, h - 1], [w - 1, 0]]).reshape(-1, 1, 2)

            # Transform the template corners to the scene image using the homography
            dst = cv2.perspectiveTransform(pts, H)

            # Draw the bounding box on the img_matches (which contains the scene image on the right)
            # The points in dst are relative to the scene image.
            # When drawing on img_matches, we need to shift the x-coordinates by template.shape[1]
            # because the scene image is placed to the right of the template image in img_matches.
            img_matches = cv2.polylines(img_matches, [np.int32(dst) + (template.shape[1], 0)],
                                        True, (0, 255, 255), 3, cv2.LINE_AA)
            print("Bounding box drawn on the matched region.")

            # --- 7. Perform Hough Circle Detection within the bounded region ---
            # Get the bounding box coordinates in the scene image
            x_min = int(np.min(dst[:, :, 0]))
            y_min = int(np.min(dst[:, :, 1]))
            x_max = int(np.max(dst[:, :, 0]))
            y_max = int(np.max(dst[:, :, 1]))

            # Ensure coordinates are within image bounds
            x_min = max(0, x_min)
            y_min = max(0, y_min)
            x_max = min(scene.shape[1], x_max)
            y_max = min(scene.shape[0], y_max)

            # Extract ROI from the original scene image (grayscale for HoughCircles)
            roi_gray = scene_gray[y_min:y_max, x_min:x_max]

            if roi_gray.shape[0] > 0 and roi_gray.shape[1] > 0:
                # Apply Hough Circle Transform
                # Parameters might need tuning based on the size and clarity of circles
                # dp: Inverse ratio of the accumulator resolution to the image resolution.
                # minDist: Minimum distance between the centers of the detected circles.
                # param1: Higher threshold for the Canny edge detector (used internally).
                # param2: Accumulator threshold for the circle centers at the detection stage.
                # minRadius: Minimum circle radius.
                # maxRadius: Maximum circle radius.
                # circles = cv2.HoughCircles(roi_gray, cv2.HOUGH_GRADIENT, dp=1.5, minDist=100,
                #                            param1=80, param2=50, minRadius=180, maxRadius=200) # big circle
                circles = cv2.HoughCircles(roi_gray, cv2.HOUGH_GRADIENT, dp=1.5, minDist=100,
                                           param1=100, param2=50, minRadius=70, maxRadius=90) # small circle

                if circles is not None:
                    circles = np.uint16(np.around(circles))
                    for i in circles[0, :]:
                        # Draw outer circle
                        # Shift coordinates from ROI to the combined img_matches
                        # Add template.shape[1] to x-coordinate because scene is to the right
                        center_x = i[0] + x_min + template.shape[1]
                        center_y = i[1] + y_min
                        radius = i[2]
                        cv2.circle(img_matches, (center_x, center_y), radius, (0, 0, 255), 2) # Red circle
                        # Draw center of the circle
                        cv2.circle(img_matches, (center_x, center_y), 2, (0, 255, 0), 3) # Green center
                        print(f"Detected circle at ({center_x}, {center_y}) with radius {radius} within the bounded region.")
                else:
                    print("No circles detected within the matched region.")
            else:
                print("ROI is empty, cannot perform Hough Circle detection.")
        else:
            print("Warning: Homography could not be calculated. Not enough inliers or bad matches.")
    else:
        print("Warning: Not enough good matches (less than 4) to calculate homography and draw bounding box.")


    # --- 8. Save the final image to a file ---
    output_filename = "feature_matches_and_circles_result.png"
    cv2.imwrite(output_filename, img_matches)
    print(f"Final result image saved as '{output_filename}'.")

# Call the function to run the example
if __name__ == "__main__":
    feature_based_matching_example()
