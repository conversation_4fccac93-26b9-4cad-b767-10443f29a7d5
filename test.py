import numpy as np

M_ext = np.array([
    [-1.0007905486901512, 0.003223362525267898, 0.02306619124450609, 439.6890099766894 / 1000],
    [-0.02050342694205889, -0.05745323478897354, -0.9971714663932695, 1120.2799124557278 / 1000],
    [-0.0018872089779450479, -0.998472225963346, 0.05484329257280212, 116.14279785566329 / 1000],
    [0.0, 0.0, 0.0, 1.0]
], dtype=np.float32)
# 计算逆变换矩阵
M_world_to_cam = np.linalg.inv(M_ext)

# 定义世界坐标系下的点 (x, y, z)
point_world = np.array([0.565, 0.1656, 0.22658, 1.0])  # 转换为齐次坐标

# 变换到相机坐标系
point_cam_hom = np.dot(M_world_to_cam, point_world)

# 取前三个分量即为结果（第四个分量为1，无需处理）
point_camera = point_cam_hom[:3]

print(f"相机坐标系下的坐标: {point_camera}")