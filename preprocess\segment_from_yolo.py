# SPDX-FileCopyrightText: Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import PIL.Image
import matplotlib.pyplot as plt
import numpy as np
import argparse
import sys
import os
import cv2
import time

from sam_predictor import Predictor
from infer_wrap import YOLOv8TRTDetector


if __name__ == "__main__":

    parser = argparse.ArgumentParser()
    parser.add_argument("image", type=str, help="Path to the input image")
    parser.add_argument("--yolo_engine", type=str, default="weights/yolov8m_seatbelt_2025_08_27.engine", help="Path to YOLOv8 TensorRT engine file")
    parser.add_argument("--image_encoder", type=str, default="weights/resnet18_image_encoder_fp16.engine", help="Path to SAM image encoder engine")
    parser.add_argument("--mask_decoder", type=str, default="weights/mobile_sam_mask_decoder.engine", help="Path to SAM mask decoder engine")
    parser.add_argument("--conf_thresh", type=float, default=0.25, help="Confidence threshold for YOLO detections")
    parser.add_argument("--device", type=str, default="cuda:0", help="Device for inference")
    parser.add_argument("--class_filter", type=int, nargs='+', default=None, help="Filter detections by class IDs (optional)")
    parser.add_argument("--output", type=str, default="../data/template_v3/out_mask_resnet.png", help="Output image path")
    args = parser.parse_args()
        
    def bbox2points(bbox):
        """Convert bounding box to points for SAM"""
        points = np.array([
            [bbox[0], bbox[1]],  # top-left
            [bbox[2], bbox[3]]   # bottom-right
        ])

        point_labels = np.array([2, 3])  # 2=Bounding box top-left, 3=Bounding box bottom-right
        return points, point_labels

    def draw_bbox(bbox, color='g'):
        """Draw bounding box on the plot"""
        x = [bbox[0], bbox[2], bbox[2], bbox[0], bbox[0]]
        y = [bbox[1], bbox[1], bbox[3], bbox[3], bbox[1]]
        plt.plot(x, y, color + '-')

    # Initialize YOLOv8 detector
    detector = YOLOv8TRTDetector(args.yolo_engine, args.device)

    # Load image
    pil_image = PIL.Image.open(args.image)  #.convert("RGB")  # if image is 1 channel, need to convert;
    # Convert PIL image to OpenCV format for YOLOv8
    cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

    # Perform detection
    detections = detector.predict(cv_image)
    
    # Filter detections by confidence threshold
    filtered_detections = []
    for i, (bbox, score, label) in enumerate(zip(detections['bboxes'], detections['scores'], detections['labels'])):
        if score >= args.conf_thresh:
            if args.class_filter is None or int(label) in args.class_filter:
                filtered_detections.append({
                    'bbox': bbox,
                    'score': score,
                    'label': int(label)
                })
    
    # Initialize SAM predictor
    sam_predictor = Predictor(
        args.device,
        args.image_encoder,
        args.mask_decoder
    )

    # Set image for SAM
    sam_predictor.set_image(pil_image)
    
    # Number of detections
    N = len(filtered_detections)
    
    if N == 0:
        print("No objects detected with the current settings.")
        sys.exit(0)

    FILTER_LARGEST_MASK = False

    def subplot_notick(a, b, c):
        """Create subplot without ticks"""
        ax = plt.subplot(a, b, c)
        ax.set_xticklabels([])
        ax.set_yticklabels([])
        ax.axis('off')

    def draw_detection(index, apply_filter=FILTER_LARGEST_MASK):
        """Draw detection and segmentation for a specific object"""
        subplot_notick(2, N, index + 1)
        bbox = filtered_detections[index]['bbox']
        label = filtered_detections[index]['label']
        score = filtered_detections[index]['score']
        
        points, point_labels = bbox2points(bbox)
        
        mask, _, _ = sam_predictor.predict(points, point_labels)
        
        # Convert logits to a binary mask
        raw_binary = (mask[0, 0].detach().cpu().numpy() > 0).astype(np.uint8)

        if apply_filter:
            # Use connected components analysis to keep the largest mask
            num_labels, labels_im, stats, _ = cv2.connectedComponentsWithStats(raw_binary, connectivity=8)

            if num_labels > 1:
                areas = stats[1:, cv2.CC_STAT_AREA]
                largest_label = 1 + np.argmax(areas)
                filtered_mask = (labels_im == largest_label).astype(np.uint8) * 255
            else:
                filtered_mask = raw_binary * 255
        else:
            # Do not filter, use the original mask
            filtered_mask = raw_binary * 255

        mask_filename = f"../data/template_v3/mask_seatbelt_{index}_resnet.png"
        cv2.imwrite(mask_filename, filtered_mask)
        
        # Use the filtered mask for visualization as well
        bool_filtered_mask = filtered_mask.astype(bool)
        
        plt.imshow(pil_image)
        draw_bbox(bbox)
        plt.title(f"Class: {label}, Score: {score:.2f}")
        
        subplot_notick(2, N, N + index + 1)
        plt.imshow(pil_image)
        plt.imshow(bool_filtered_mask, alpha=0.5)
        plt.title("Segmentation Mask")

    # Create figure for visualization
    AR = pil_image.width / pil_image.height
    plt.figure(figsize=(25, 10))
    
    # Draw all detections and their segmentations
    for i in range(N):
        draw_detection(i)
       
    plt.subplots_adjust(wspace=0.05, hspace=0.2)
    plt.savefig(args.output, bbox_inches="tight")
    print(f"Results saved to {args.output}")
