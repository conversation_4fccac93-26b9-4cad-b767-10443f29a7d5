# Copyright (c) RVBUST, Inc - All rights reserved.
import os
import numpy as np
import cv2
from pcam import <PERSON>cipioCamera


def TryCreateDir(path):
    """Create directory if it doesn't exist."""
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"Created directory: {path}")


def App(index=0):
    """PCAM calibration capture application."""
    try:
        with PercipioCamera() as camera:
            # Print camera information
            camera.print_camera_info()
            
            print(f"Capturing calibration data for index {index}...")
            
            # Capture data with aligned depth-to-color and aligned point cloud
            capture_data = camera.capture(show=False, save=False, align_depth_to_color=True)
            
            if capture_data.color_image is None:
                print("Failed to capture color image!")
                return -1
            
            if capture_data.aligned_point_cloud is None:
                print("Failed to capture aligned point cloud!")
                return -1
            
            # Data Process - create save directory structure
            save_address = "Data"
            TryCreateDir(save_address)
            save_address += "/pcam"
            TryCreateDir(save_address)
            save_address += "/calibrate"
            TryCreateDir(save_address)
            save_address += "/2025-08-26"
            TryCreateDir(save_address)
            
            # Save color image
            color_filename = f"{save_address}/image_{index}.png"
            cv2.imwrite(color_filename, capture_data.color_image)
            print(f"Saved color image: {color_filename}")
            
            # Save aligned point cloud
            if capture_data.aligned_point_cloud is not None:
                import open3d as o3d
                
                # Separate coordinates and colors
                points = capture_data.aligned_point_cloud[:, :3]  # XYZ coordinates
                colors = capture_data.aligned_point_cloud[:, 3:6]  # RGB colors (0-1 range)
                colors = colors.astype(np.float32) / 255.0
                # Create Open3D point cloud object
                pcd = o3d.geometry.PointCloud()
                pcd.points = o3d.utility.Vector3dVector(points)
                pcd.colors = o3d.utility.Vector3dVector(colors)
                
                # Save as PLY format (with color)
                pc_filename = f"{save_address}/pointmap_color_{index}.ply"
                o3d.io.write_point_cloud(pc_filename, pcd, write_ascii=True)
                print(f"Saved aligned color point cloud: {pc_filename}")
            
            print("PCAM capture completed successfully!")
            
    except Exception as e:
        print(f"Error during PCAM capture: {e}")
        return -1
    
    # =========  AUBO ============
    try:
        import pyaubo_sdk

        robot_ip = "*************" 
        robot_port = 30004  
        robot_rpc_client = pyaubo_sdk.RpcClient()

        robot_rpc_client.connect(robot_ip, robot_port)
        robot_rpc_client.login("aubo", "123")
        robot_name = robot_rpc_client.getRobotNames()[0]
        tcp_pose = robot_rpc_client.getRobotInterface(robot_name).getRobotState().getTcpPose()

        print("--- TCP Pose ---", tcp_pose)
        pose_filename = "Data/pcam/calibrate/2025-08-26/pose.txt"
        with open(pose_filename, 'a+') as f:
            f.write(str(tcp_pose)+'\n')
        
        print(f"Saved TCP pose to: {pose_filename}")
        
    except Exception as e:
        print(f"Error during robot pose capture: {e}")
        return -1

    return 0


if __name__ == "__main__":
    App(index=14)