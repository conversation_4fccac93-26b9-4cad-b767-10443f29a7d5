import cv2
import numpy as np
from typing import <PERSON><PERSON>, List
import os

from utils_matcher import extract_and_match_features, detect_best_circle_in_roi
from template_match import template_matching_registration

def feature_based_matching(
        template_path: str, 
        scene_path: str, 
        method: str = "SIFT", 
        save_dir: str = "output",
        flag: str = "small",
        ) -> List[Tuple[int, int]]:
    """
    Demonstrates feature-based image matching with circle detection.
    Uses SIFT feature detector and BFMatcher for feature matching,
    then performs Hough circle detection in the matched region.
    
    Args:
        template_path: str
            Path to the template image.
        scene_path: str
            Path to the scene image.
        method: str
            Feature extraction method ("ORB" or "SIFT").
        save_dir: str
            Directory to save the result image.
        flag: str
            Flag to select the circle size ("small" or "large").
    Returns:
        List[Tuple[int, int]]
            List of all detected circle center coordinates (x, y) in scene image coordinate system.
            Returns empty list if no circles are detected or matching fails.
        
    Raises:
        ValueError: If the feature extraction method is not supported.
    """
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    print("Loading images...")

    # In a real application, you would load images like this:
    template = cv2.imread(template_path)
    scene = cv2.imread(scene_path)
    if template is None or scene is None:
        print("Error: Could not load images. Check file paths.")
        return []
    template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
    scene_gray = cv2.cvtColor(scene, cv2.COLOR_BGR2GRAY)

    # ---- Feature extraction & matching ----
    matches, kp1, kp2 = extract_and_match_features(template_gray, scene_gray, method=method)
    # matches already sorted by distance
    if matches is None or len(matches) == 0:
        return []

    # Filter out the best N matches (e.g., top 10%)
    # This helps remove bad matches and improve matching accuracy.
    num_good_matches = int(len(matches) * 0.1) # Take the top 10% of matches
    if num_good_matches == 0 and len(matches) > 0:
        num_good_matches = 1 # Keep at least one match (if available)
    good_matches = matches[:num_good_matches]
    print(f"Retained {len(good_matches)} good matches after filtering.")

    # --- 5. Draw matching results ---
    # drawMatches function visualizes keypoints and their matching lines.
    # flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS means not to draw unmatched keypoints.
    img_matches = cv2.drawMatches(template, kp1, scene, kp2, good_matches, None,
                                  flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS)
    print("Matching result image drawn.")

    # --- 6. Find the bounding box of the matched object ---
    # Need at least 4 good matches to find homography
    if len(good_matches) > 4:
        # Extract corresponding keypoints from good matches
        src_pts = np.float32([kp1[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
        dst_pts = np.float32([kp2[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)

        # Find the homography matrix (perspective transformation)
        # H: 3x3 transformation matrix
        # mask: indicates which points are inliers
        H, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)

        if H is not None:
            # Get the corners of the template image
            h, w, _ = template.shape
            pts = np.float32([[0, 0], [0, h - 1], [w - 1, h - 1], [w - 1, 0]]).reshape(-1, 1, 2)

            # Transform the template corners to the scene image using the homography
            dst = cv2.perspectiveTransform(pts, H)

            # Draw the bounding box on the img_matches (which contains the scene image on the right)
            # The points in dst are relative to the scene image.
            # When drawing on img_matches, we need to shift the x-coordinates by template.shape[1]
            # because the scene image is placed to the right of the template image in img_matches.
            img_matches = cv2.polylines(img_matches, [np.int32(dst) + (template.shape[1], 0)],
                                        True, (0, 255, 255), 3, cv2.LINE_AA)
            print("Bounding box drawn on the matched region.")

            # --- 7. Perform Hough Circle Detection within the bounded region ---
            # Get the bounding box coordinates in the scene image
            x_min = int(np.min(dst[:, :, 0]))
            y_min = int(np.min(dst[:, :, 1]))
            x_max = int(np.max(dst[:, :, 0]))
            y_max = int(np.max(dst[:, :, 1]))

            # Ensure coordinates are within image bounds
            x_min = max(0, x_min)
            y_min = max(0, y_min)
            x_max = min(scene.shape[1], x_max)
            y_max = min(scene.shape[0], y_max)

            # Extract ROI from the original scene image (grayscale for HoughCircles)
            roi_gray = scene_gray[y_min:y_max, x_min:x_max]

            # Detect all circles in the ROI
            detected_circles = detect_best_circle_in_roi(roi_gray, x_min, y_min, flag)
            
            if detected_circles:
                print(f"Detected {len(detected_circles)} circles in the matched region.")
                
                # Draw all detected circles on the visualization image
                circle_centers = []
                for i, (center_x, center_y, radius) in enumerate(detected_circles):
                    # Shift coordinates for drawing on img_matches (scene is on the right)
                    draw_center_x = center_x + template.shape[1]
                    draw_center_y = center_y
                    
                    # Draw each circle with a different color shade
                    color_intensity = 255 - (i * 30) % 200  # Vary color intensity
                    cv2.circle(img_matches, (draw_center_x, draw_center_y), radius, (0, 0, color_intensity), 2)
                    cv2.circle(img_matches, (draw_center_x, draw_center_y), 2, (0, 255, 0), 3)  # Green center
                    
                    # Store circle center in scene coordinates
                    circle_centers.append((center_x, center_y))
                    print(f"Circle {i+1}: center=({center_x}, {center_y}), radius={radius}")
                
                # Save the final image
                output_filename = os.path.join(save_dir, f"feature_matches_and_circles_result_{flag}.png")
                cv2.imwrite(output_filename, img_matches)
                print(f"Final result image saved as '{output_filename}'.")
                return circle_centers
            else:
                print("Warning: No circles detected in the matched region.")
                # Save image even if no circle detected
                output_filename = os.path.join(save_dir, f"feature_matches_and_circles_result_{flag}.png")
                cv2.imwrite(output_filename, img_matches)
                print(f"Final result image saved as '{output_filename}'.")
                return []
        else:
            print("Warning: Homography could not be calculated. Not enough inliers or bad matches.")
            return []
    else:
        print("Warning: Not enough good matches (less than 4) to calculate homography and draw bounding box.")
        return []


def main():
    scene_path = "../data/2d_images/0710_T4/Image_20250715102440965.bmp"
    template_path_1 = "template/temp_small.jpg"
    template_path_2 = "template/temp_large.jpg"

    # large circle
    
    result_large_1 = template_matching_registration(template_path=template_path_2, image_path=scene_path, flag="large")
    result_small_2 = template_matching_registration(template_path=template_path_1, image_path=scene_path, flag="small")
    
    

    # result_large_1 = feature_based_matching(
    #         template_path=template_path_1,
    #         scene_path=scene_path,
    #         method="SIFT",
    #         save_dir="output/0710_T5",
    #         flag="large",
    #     )
    # # small circle
    # result_small_2 = feature_based_matching(
    #         template_path=template_path_2,
    #         scene_path=scene_path,
    #         method="SIFT",
    #         save_dir="output/0710_T5",
    #         flag="small",
    #     )

    x1, y1 = result_large_1[0]
    x2, y2 = result_small_2[0]
    print(f"Circle 1: ({x1}, {y1})")
    print(f"Circle 2: ({x2}, {y2})")

    pt1 = [x1, y1, 1]
    pt2 = [x2, y2, 1] 
    H = np.array([
        [ 1.74529789e-05, -9.68114669e-06,  6.66878279e+02],
        [-4.25076605e-03,  6.57644182e-02, 8.05730062e+02],
        [-6.51356975e-02, -4.38722264e-03,  1.00000000e+00]
    ])

    pt1_base = np.dot(H, np.array(pt1).reshape(3, 1))
    pt2_base = np.dot(H, np.array(pt2).reshape(3, 1))
    dx = pt2_base[0] - pt1_base[0]
    dy = pt2_base[1] - pt1_base[1]
    dz = pt2_base[2] - pt1_base[2]

    print(f"dx: {dx}, dy: {dy}, dz: {dz}")


# Call the function to run the example
if __name__ == "__main__":
    main()    
