import PIL.Image
import numpy as np
import matplotlib.pyplot as plt
import cv2
import open3d as o3d
import torch
import copy
import time
import sys

from pathlib import Path
# Add preprocess directory to Python path so that `import models` resolves correctly
sys.path.append(str(Path(__file__).resolve().parent / 'preprocess'))
# Add camera directory to Python path for PercipioCamera import  
camera_path = str(Path(__file__).resolve().parent / 'camera' / 'Pcammls')
if camera_path not in sys.path:
    sys.path.append(camera_path)
print(f"Added camera path: {camera_path}")

from preprocess.sam_predictor import Predictor
from preprocess.infer_wrap import YOLOv8TRTDetector
from preprocess.mask2cloud import mask_to_point_cloud
from utils import bbox2points, draw_bbox, subplot_notick, pose_6d_to_matrix, matrix_to_pose_6d, matrix_to_axis_6d

from pose_estimator import PoseEstimator
from camera.Pcammls.pcam import PercipioCamera
from robo_ctl.aubo_controller import <PERSON>bo<PERSON><PERSON><PERSON><PERSON>


def main():
    # Path to template file
    template = 'data/template_v3/filtered_point_cloud_remove_redius_outlier_500_002.ply' 
    
    # Initialize camera and capture live data
    print("Initializing Percipio camera...")
    try:
        with PercipioCamera(exposure_time=60000) as camera:
            camera.print_camera_info()
            
            print("Capturing color and depth images...")
            # Capture only color and aligned depth (capture_3d=False for efficiency)
            capture_data = camera.capture(capture_3d=False, show=False)
            
            if capture_data.color_image is None or capture_data.aligned_depth_image is None:
                raise RuntimeError("Failed to capture required images from camera")
            
            print(f"Successfully captured:")
            print(f"  - Color image: {capture_data.color_image.shape}")
            print(f"  - Aligned depth: {capture_data.aligned_depth_image.shape}")
            
            # Convert captured data to required format
            cv_image = capture_data.color_image  # Already in BGR format from camera
            pil_image = PIL.Image.fromarray(cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB))
            depth = capture_data.aligned_depth_image.astype(np.float32) / 1000.0  # Convert mm to m
            
            # Get camera intrinsics from calibration data
            color_intrinsic_matrix = np.array(camera.color_info.intrinsic).reshape(3, 3)
            print(f"Using camera intrinsic matrix:\n{color_intrinsic_matrix}")
            
    except Exception as e:
        print(f"Camera initialization failed: {e}")
        print("Falling back to hardcoded file paths for testing...")
        # Fallback to original hardcoded paths
        color_path = '/home/<USER>/workspace/yang.wang/seatbelt/camera/Pcammls/Data/capture-2025-08-27_01/color_1756272035.png'
        depth_path = '/home/<USER>/workspace/yang.wang/seatbelt/camera/Pcammls/Data/capture-2025-08-27_01/aligned_depth_1756272035.png'
        
        pil_image = PIL.Image.open(color_path)
        cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        depth = cv2.imread(depth_path, cv2.IMREAD_ANYDEPTH)
        depth = depth.astype(np.float32) / 1000.0
        
        # Use hardcoded intrinsic matrix as fallback
        color_intrinsic_matrix = np.array([
            [1857.92529296875, 0.0, 1267.4947509765625],
            [0.0, 1857.943359375, 943.3340454101562],
            [0, 0, 1] 
        ])

    # config infer mask
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    yolo_engine = 'preprocess/weights/yolov8m_seatbelt_2025_08_27.engine'
    image_encoder = 'preprocess/weights/resnet18_image_encoder_fp16.engine'
    mask_decoder = 'preprocess/weights/mobile_sam_mask_decoder.engine'

    # Initialize detector and sam predictor
    detector = YOLOv8TRTDetector(yolo_engine, device)
    sam_predictor = Predictor(device, image_encoder, mask_decoder)
    
    # yolo infer
    # pil_image and cv_image are already defined above from camera capture or fallback
    detections = detector.predict(cv_image)
    
    N = len(detections['bboxes'])
    
    if N == 0:
        print("No objects detected with the current settings.")
        sys.exit(0)

    bbox = detections['bboxes'][0]
    
    # sam infer
    points, point_labels = bbox2points(bbox)
    sam_predictor.set_image(pil_image)
    mask, _, _ = sam_predictor.predict(points, point_labels)
    mask = (mask[0, 0] > 0).detach().cpu().numpy()
    mask = mask * 255
    
    if True:
        subplot_notick(2, N, 1)
        plt.imshow(pil_image)
        draw_bbox(bbox)
        plt.title("Detection")

        subplot_notick(2, N, 2)
        plt.imshow(pil_image)
        plt.imshow(mask, alpha=0.5)
        plt.title("Segmentation Mask")
        plt.subplots_adjust(wspace=0.05, hspace=0.2)
        plt.savefig('out/out.png', bbox_inches="tight")
       
    # estimate pose
    # depth is already processed above from camera capture or fallback (in meters)
    # generate point cloud from rgb and depth dependant on mask
    rgb = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)

    # Use camera intrinsic matrix (already defined above from camera or fallback)
    camera_intrinsic = color_intrinsic_matrix

    pcd_scene = mask_to_point_cloud(rgb, depth, mask, camera_intrinsic)
    
    # estimation
    source_pcd = o3d.io.read_point_cloud(template)
    target_pcd = pcd_scene

    T_camera_to_world = np.array([
        [-0.9986409869543417, -0.01948014307366654, -0.017593604848226647, 0.5416675868284545],
        [-0.019972183434040086, 0.9932659701203223, 0.08607051708729817, 0.38195964595246085],
        [0.01300407988110321, 0.09365319903319758, -0.9905491402097637, 0.9956482519748999],
        [0.0, 0.0, 0.0, 1.0]
    ])  

    print("Transforming point cloud from camera to world coordinates...")
    source_pcd_world = copy.deepcopy(source_pcd)
    source_pcd_world.transform(T_camera_to_world)

    target_pcd_world = copy.deepcopy(target_pcd)
    target_pcd_world.transform(T_camera_to_world)


    # Initialize pose estimator
    pose_estimator = PoseEstimator(source_pcd_world, target_pcd_world, voxel_size=0.002)
    T_source_to_target = pose_estimator.run()
    pose_estimator.visualize_registration(save_dir="out/2025_08_27_T0", visualize_merged_clouds=False)
    
    # gripper pose in robot base frame
   # gripper_pose_in_source = np.array([207.96, 269.18, 195.29, 128.88, -1, 87.52]) # x, y, z, rx, ry, rz TCP_1
    gripper_pose_in_source = np.array([678.51, 360.56, 96.79, 2.235, 0.021, 1.598]) # x, y, z, rx, ry, rz TCP_q rad

    gripper_pose_in_source = pose_6d_to_matrix(gripper_pose_in_source, unit='mm', degrees=False)

    # gripper pose in target frame
    gripper_pose_in_target_matrix = T_source_to_target @ gripper_pose_in_source 
    gripper_pose_in_target = matrix_to_pose_6d(gripper_pose_in_target_matrix, unit='m', degrees=False) # rad
    np.set_printoptions(precision=5, suppress=True, linewidth=100)
    print(f"gripper_pose_in_target: {gripper_pose_in_target}")

    # move robot
    # gripper_pose_in_target_axis = matrix_to_axis_6d(gripper_pose_in_target_matrix, unit='m')
    # print(f"gripper_pose_in_target_axis: {gripper_pose_in_target_axis}") # rot-vector
    
    user_input = input("move the robotic? (y/n)").lower()
    if user_input == 'n':
        print("Down!")
        exit()
    elif user_input == 'y':
        print("robotic move!")
    else:
        print("no valiad!")
        exit()

    from pyrobotiqgripper import RobotiqGripper
    gripper = RobotiqGripper(portname="/dev/ttyUSB0")
    gripper.reset()
    gripper.activate()
    gripper.goTo(100)

    ctrl = AuboController(ip="*************", port=30004)
    if not ctrl.connect(login=True):
        print("连接或登录失败")
        raise SystemExit(1)
    print("连接并登录成功，机器人:", ctrl.robot_name)
    ctrl.set_tool_speed_acc(0.8, 0.5)
    ctrl.set_joint_speed_acc(0.1, 0.1)

    waypoint_0 = np.array([0.4635, 0.3544, 0.3726, 2.376, -0.083, 1.689])
    waypoint_1 = gripper_pose_in_target.copy()
    waypoint_1[0] -= 0.1
    waypoint_1[2] += 0.2
    ret = ctrl.moveLine(target_pose=waypoint_1, start_pose=waypoint_0, managed_runtime=True)
    print(f"moveLine to waypoint_1, ret: {ret}")

    print(f"gripper_pose_in_target: {gripper_pose_in_target}")
    ret = ctrl.moveLine(target_pose=gripper_pose_in_target, managed_runtime=True)
    print(f"moveLine to gripper_pose_in_target, ret: {ret}")

    gripper.goTo(255)

    waypoint_2 = gripper_pose_in_target.copy()
    waypoint_2[2] += 0.2
    ret = ctrl.moveLine(target_pose=waypoint_2, managed_runtime=True)
    print(f"moveLine to waypoint_2, ret: {ret}")

    take_pic_position = np.array([0.565, 0.1656, 0.2565, 3.141, 0.0, 3.141])
    ret = ctrl.moveLine(target_pose=take_pic_position, managed_runtime=True)
    print(f"moveLine to take_pic_position, ret: {ret}")


if __name__ == "__main__":
    main()