#!/usr/bin/env python3
"""
Real-time color image capture and display using PercipioCamera.

This script provides a real-time interface for capturing and saving color images
from Percipio cameras with sequential numbering.
"""

import cv2
import time
from pathlib import Path
from pcam import PercipioCamera


class ColorImageCapture:
    """Real-time color image capture and display."""
    
    def __init__(self, exposure_time=None, save_dir="captured_colors"):
        """
        Initialize color image capture.
        
        Args:
            exposure_time: Manual exposure time (None for auto exposure)
            save_dir: Directory to save captured images
        """
        self.exposure_time = exposure_time
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(exist_ok=True)
        self.image_index = 3
        

    def run(self):
        """Run real-time capture and display loop."""
        print("=== Real-time Color Image Capture ===")
        print("Controls:")
        print("  SPACE: Capture and save image")
        print("  'q': Quit")
        print(f"Images will be saved to: {self.save_dir}")
        
        try:
            with PercipioCamera(exposure_time=self.exposure_time) as camera:
                camera.print_camera_info()
                
                print("\nStarting real-time display...")
                
                while True:
                    # Capture frame for display
                    data = camera.capture(show=False)
                    
                    if data.color_image is not None:
                        # Display the image
                        cv2.namedWindow("Color Image - Press SPACE to capture, 'q' to quit", cv2.WINDOW_NORMAL)
                        cv2.imshow("Color Image - Press SPACE to capture, 'q' to quit", 
                                 data.color_image)
                        
                        # Handle key presses
                        key = cv2.waitKey(1) & 0xFF
                        
                        if key == ord(' '):  # Space key - capture image
                            self._save_color_image(data.color_image)
                            
                        elif key == ord('q'):  # Q key - quit
                            break
                    
                    # Small delay to prevent excessive CPU usage
                    time.sleep(0.01)
                    
        except Exception as e:
            print(f"Error: {e}")
        finally:
            cv2.destroyAllWindows()
            print("Capture session ended")
            
    def _save_color_image(self, color_image):
        """Save color image with sequential naming."""
        filename = f"color_{self.image_index:04d}.png"
        filepath = self.save_dir / filename
        
        success = cv2.imwrite(str(filepath), color_image)
        if success:
            print(f"Captured image {self.image_index}: {filepath}")
            self.image_index += 1
        else:
            print(f"Failed to save image: {filepath}")


def main():
    """Main function."""
    # None = auto exposure, or specify value like 5000
    exposure_time = 60000
    
    capture = ColorImageCapture(exposure_time=exposure_time)
    capture.run()


if __name__ == "__main__":
    main()