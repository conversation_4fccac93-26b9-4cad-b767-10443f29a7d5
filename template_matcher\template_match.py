import cv2
import numpy as np
import matplotlib.pyplot as plt

def preprocess_image(image, is_template=False):
    """图像预处理：灰度化 + 高斯模糊 + 自适应阈值"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    thresh = cv2.adaptiveThreshold(
        blurred, 255, 
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
        cv2.THRESH_BINARY_INV, 21, 10
    )
    
    if is_template:
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
    
    return thresh

def template_matching_registration(template_path, image_path, display=True, flag="small"):
    """模板匹配 + 仿射变换配准（修复版）"""
    # 1. 读取图像
    template_bgr = cv2.imread(template_path)
    image_bgr = cv2.imread(image_path)
    
    # 2. 预处理
    template_gray = preprocess_image(template_bgr, is_template=True)
    image_gray = preprocess_image(image_bgr)
    
    # 3. 模板匹配
    result = cv2.matchTemplate(
        image_gray, template_gray, 
        cv2.TM_CCOEFF_NORMED
    )
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    
    # 4. 确定匹配区域
    h, w = template_gray.shape[:2]
    top_left = max_loc
    bottom_right = (top_left[0] + w, top_left[1] + h)
    
    # 5. 定义三个点对（左上角、右上角、左下角）
    # 模板图像中的三个点（浮点型）
    src_pts = np.float32([
        [0, 0],              # 左上角
        [w - 1, 0],          # 右上角
        [0, h - 1]           # 左下角
    ]).reshape(-1, 2)
    
    # 原图中对应的三个点（浮点型）
    dst_pts = np.float32([
        [top_left[0], top_left[1]],                 # 左上角
        [top_left[0] + w - 1, top_left[1]],         # 右上角
        [top_left[0], top_left[1] + h - 1]          # 左下角
    ]).reshape(-1, 2)
    cv2.rectangle(image_bgr, (top_left[0], top_left[1]), (top_left[0] + w - 1, top_left[1] + h - 1), (0 ,0 ,250 ), 3)

    image_gray = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2GRAY)
    # Extract ROI from the original scene image (grayscale for HoughCircles)
    roi_gray = image_gray[top_left[1]:top_left[1] + h - 1, top_left[0]:top_left[0] + w - 1]
    cv2.imwrite("roi.png", roi_gray)
    # circles = cv2.HoughCircles(roi_gray, cv2.HOUGH_GRADIENT, dp=1.5, minDist=100,
    #                                        param1=100, param2=50, minRadius=70, maxRadius=90) # small circle
    if flag == "small":
        circles = cv2.HoughCircles(roi_gray, cv2.HOUGH_GRADIENT, dp=1.5, minDist=100,
                                        param1=100, param2=50, minRadius=70, maxRadius=90) # small circle
    elif flag == "large":
        circles = cv2.HoughCircles(roi_gray, cv2.HOUGH_GRADIENT, dp=1.5, minDist=100,
                                        param1=80, param2=50, minRadius=170, maxRadius=182) # large circle
    else:
        raise ValueError(f"Unsupported flag '{flag}'. Use 'small' or 'large'.")

    circles_res = []
    if circles is not None:
        circles = np.uint16(np.around(circles))
        for i in circles[0, :]:
            # Draw outer circle
            # Shift coordinates from ROI to the combined img_matches
            # Add template.shape[1] to x-coordinate because scene is to the right
            center_x = i[0] + top_left[0]
            center_y = i[1] + top_left[1]
            radius = i[2]
            cv2.circle(image_bgr, (center_x, center_y), radius, (0, 0, 255), 2) # Red circle
            # Draw center of the circle
            cv2.circle(image_bgr, (center_x, center_y), 2, (0, 255, 0), 3) # Green center
            print(f"Detected circle at ({center_x}, {center_y}) with radius {radius} within the bounded region.")
            circles_res.append((center_x, center_y))

    # cv2.imshow("image", image_bgr)
    # cv2.waitKey()
    cv2.imwrite(f"reslut_{flag}.png", image_bgr)
    # # 6. 计算仿射变换矩阵（3x2）
    # M = cv2.getAffineTransform(src_pts, dst_pts)
    
    # # 7. 应用仿射变换
    # h_img, w_img = image_bgr.shape[:2]
    # registered_image = cv2.warpAffine(
    #     image_bgr, M, (w_img, h_img)
    # )
    
    # 8. 可视化
    # if display:
    #     image_with_box = image_bgr.copy()
    #     cv2.rectangle(image_with_box, top_left, bottom_right, (0, 255, 0), 3)
        
    #     template_rgb = cv2.cvtColor(template_bgr, cv2.COLOR_BGR2RGB)
    #     image_box_rgb = cv2.cvtColor(image_with_box, cv2.COLOR_BGR2RGB)
    #     # registered_rgb = cv2.cvtColor(registered_image, cv2.COLOR_BGR2RGB)
        
    #     fig, axes = plt.subplots(1, 2, figsize=(15, 5))
    #     axes[0].imshow(template_rgb)
    #     axes[0].set_title("Template (temp.png)")
    #     axes[1].imshow(image_box_rgb)
    #     axes[1].set_title("Matched Region")
    #     # axes[2].imshow(registered_rgb)
    #     # axes[2].set_title("Registered Image")
        
    #     # for ax in axes:
    #     #     ax.axis("off")
    #     plt.show()
    return circles_res
    # return registered_image, M


if __name__ == "__main__":
    # ========== 调用示例 ==========
    template_path = "temp_2.jpg"   # 替换为实际路径
    image_path = "data/2d_images/Image_20250707114315045.bmp"         # 替换为实际路径

    template_matching_registration(
        template_path, 
        image_path, 
        display=True
    )

    # # 保存配准结果（可选）
    # cv2.imwrite("registered_result.png", registered_result)
    # print("配准变换矩阵：\n", transform_matrix)
