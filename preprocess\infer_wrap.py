from models import TRTModule  # isort:skip
import torch
import cv2
from pathlib import Path
import numpy as np

from models.torch_utils import det_postprocess
from models.utils import blob, letterbox

class YOLOv8TRTDetector:
    def __init__(self, engine_path, device='cuda:0'):
        """
        Initialize the YOLOv8 TensorRT detector
        
        Args:
            engine_path (str): Path to the TensorRT engine file
            device (str): Device to run inference on ('cuda:0', 'cuda:1', etc.)
        """
        self.device = torch.device(device)
        self.engine = TRTModule(engine_path, self.device)
        self.H, self.W = self.engine.inp_info[0].shape[-2:]
        
        # Set desired output names order
        self.engine.set_desired(['num_dets', 'bboxes', 'scores', 'labels'])
        
        # --- WARMUP ---
        self.warmup_engine()
        # --------------
    
    def warmup_engine(self, num_warmup_iterations=10):
        """Runs dummy inferences to warmup the engine."""
        print(f"Starting engine warmup ({num_warmup_iterations} iterations)...")
        try:
            # Determine expected input shape and dtype (adjust if blob behaves differently)
            expected_shape = (1, 3, self.H, self.W) 
            expected_dtype = torch.float32 # Common dtype for models

            # Create dummy input tensor on the correct device
            dummy_tensor = torch.zeros(expected_shape, dtype=expected_dtype, device=self.device)

            # Warmup loop
            for _ in range(num_warmup_iterations):
                # Perform inference but ignore the output
                _ = self.engine(dummy_tensor) 

            print("Engine warmup complete!")
            torch.cuda.synchronize() # Ensure all GPU operations are finished (if using CUDA)

        except Exception as e:
            print(f"Warning: Engine warmup failed. Error: {e}")
            print("Continuing without warmup...")
    
    def predict(self, image):
        """
        Perform object detection on a single image
        
        Args:
            image: Can be either a string (path to image file) or a numpy array (BGR image)
            draw (bool): Whether to draw bounding boxes on the image (default: True)
            
        Returns:
            dict: A dictionary containing detection results:
                - 'bboxes': List of bounding boxes [x1, y1, x2, y2]
                - 'scores': List of confidence scores
                - 'labels': List of class labels (indices)
        """
        # Load image if path is provided
        if isinstance(image, str) or isinstance(image, Path):
            bgr = cv2.imread(str(image))
            if bgr is None:
                raise FileNotFoundError(f"Could not load image: {image}")
        else:
            bgr = image.copy()
        
        # Prepare image for inference
        draw_img = bgr.copy()
        bgr, ratio, dwdh = letterbox(bgr, (self.W, self.H))
        rgb = cv2.cvtColor(bgr, cv2.COLOR_BGR2RGB)
        tensor = blob(rgb, return_seg=False)
        dwdh = torch.asarray(dwdh * 2, dtype=torch.float32, device=self.device)
        tensor = torch.asarray(tensor, device=self.device)
        
        # Inference
        data = self.engine(tensor)
        
        # Post-processing
        bboxes, scores, labels = det_postprocess(data)
        
        # Prepare result dictionary
        result = {
            'bboxes': [],
            'scores': [],
            'labels': [],
            'label_names': [],
            'processed_image': draw_img
        }
        
        if bboxes.numel() == 0:
            # No objects detected
            return result
        
        # Adjust bounding boxes to original image size
        bboxes -= dwdh
        bboxes /= ratio
        
        # Convert tensors to lists for the result dictionary
        bboxes_list = bboxes.cpu().numpy().tolist()
        scores_list = scores.cpu().numpy().tolist()
        labels_list = labels.cpu().numpy().tolist()
        
        # Update result dictionary
        result['bboxes'] = bboxes_list
        result['scores'] = scores_list
        result['labels'] = labels_list

        return result


# Example usage
if __name__ == '__main__':
    import argparse
    
    def parse_args():
        parser = argparse.ArgumentParser()
        parser.add_argument('--engine', type=str, required=True, help='Engine file')
        parser.add_argument('--img', type=str, required=True, help='Image file')
        parser.add_argument('--device', type=str, default='cuda:0', help='TensorRT infer device')
        return parser.parse_args()
    
    args = parse_args()
    
    # Initialize detector
    detector = YOLOv8TRTDetector(args.engine, args.device)
    
    # Perform detection
    result = detector.predict(args.img)
    
    # Print results
    if len(result['bboxes']) == 0:
        print(f'No objects detected in {args.img}')
    else:
        print(f'Detected {len(result["bboxes"])} objects:')
        for i, (bbox, score, label) in enumerate(zip(result['bboxes'], result['scores'], result['labels'])):
            print(f'  {i+1}. {label}: {score:.3f}, bbox: {bbox}')