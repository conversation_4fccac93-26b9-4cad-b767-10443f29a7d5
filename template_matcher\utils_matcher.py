import cv2
import numpy as np
from typing import List, <PERSON><PERSON>


def extract_and_match_features(
        template_gray: np.n<PERSON><PERSON>, 
        scene_gray: np.ndar<PERSON>, 
        method: str = "SIFT"
        ) -> Tuple[List[cv2.DMatch], List[cv2.KeyPoint], List[cv2.KeyPoint]]:
    """
    Extract keypoints from two grayscale images, compute descriptors and
    perform brute-force matching using the specified feature extraction method.

    Args:
        template_gray: np.ndarray
            Grayscale template image.
        scene_gray: np.ndarray
            Grayscale scene image.
        method: str
            Feature extraction method ("ORB" or "SIFT").

    Returns:
        matches: list[cv2.DMatch] | None
            List of matched features sorted by distance. Returns ``None`` if
            insufficient keypoints/descriptors are detected.
        kp1: list[cv2.KeyPoint]
            Keypoints detected in the template image.
        kp2: list[cv2.KeyPoint]
            Keypoints detected in the scene image.

    Raises:
        ValueError: If the feature extraction method is not supported.
    """
    method = method.upper()

    if method == "ORB":
        detector = cv2.ORB_create()
        norm_type = cv2.NORM_HAMMING
    elif method == "SIFT":
        detector = cv2.SIFT_create()
        norm_type = cv2.NORM_L2
    else:
        raise ValueError(f"Unsupported feature extraction method '{method}'. Use 'ORB' or 'SIFT'.")

    print(f"{method} feature detector initialized.")
    

    # Detect keypoints and compute descriptors
    kp1, des1 = detector.detectAndCompute(template_gray, None)
    kp2, des2 = detector.detectAndCompute(scene_gray, None)
    print(f"Found {len(kp1)} keypoints in the template.")
    print(f"Found {len(kp2)} keypoints in the scene.")

    # Validate descriptors
    if des1 is None or des2 is None or len(des1) < 2 or len(des2) < 2:
        print("Error: Not enough keypoints detected for matching. Ensure image content is rich enough.")
        return None, kp1, kp2

    # Perform brute-force matching
    bf = cv2.BFMatcher(norm_type, crossCheck=True)
    print("BFMatcher initialized.")
    matches = bf.match(des1, des2)
    print(f"Found {len(matches)} initial matches.")

    # Sort matches by distance (best matches first)
    matches = sorted(matches, key=lambda x: x.distance)

    return matches, kp1, kp2


def detect_circle_in_roi(
        roi_gray: np.ndarray, 
        x_offset: int, 
        y_offset: int, 
        flag: str = "small"
        ) -> List[Tuple[int, int, int]]:
    """
    Detect circles in ROI and return all detected circles with confidence metrics.
    
    Args:
        roi_gray: np.ndarray
            Grayscale ROI image for circle detection
        x_offset : int
            X offset to convert ROI coordinates to scene coordinates
        y_offset : int
            Y offset to convert ROI coordinates to scene coordinates
        flag: str
            Flag to select the circle size ("small" or "large").
    Returns:
        List[Tuple[int, int, int]]
            List of detected circles as (center_x, center_y, radius) in scene coordinates.
            Returns empty list if no circles detected or invalid ROI.
    """
    if roi_gray.shape[0] <= 0 or roi_gray.shape[1] <= 0:
        print("ROI is empty, cannot perform Hough Circle detection.")
        return []
        
    # Apply Hough Circle Transform
    if flag == "small":
        circles = cv2.HoughCircles(roi_gray, cv2.HOUGH_GRADIENT, dp=1.5, minDist=100,
                                        param1=80, param2=60, minRadius=60, maxRadius=80) # small circle param1=80 param2=50
    elif flag == "large":
        circles = cv2.HoughCircles(roi_gray, cv2.HOUGH_GRADIENT, dp=1.5, minDist=100,
                                        param1=60, param2=60, minRadius=140, maxRadius=170) # large circle param1=60 param2=50
    else:
        raise ValueError(f"Unsupported flag '{flag}'. Use 'small' or 'large'.")
    
    if circles is None:
        print("No circles detected within the matched region.")
        return []
        
    circles = np.uint16(np.around(circles))
    detected_circles = []
    
    # Convert circles to scene coordinates
    for circle in circles[0, :]:
        center_x = int(circle[0]) + x_offset
        center_y = int(circle[1]) + y_offset  
        radius = int(circle[2])

        detected_circles.append((center_x, center_y, radius))
        
    return detected_circles
