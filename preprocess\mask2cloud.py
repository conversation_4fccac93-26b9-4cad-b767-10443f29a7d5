import open3d as o3d
import cv2
import numpy as np
import copy

def mask_to_point_cloud(rgb_image, depth_image, mask, intrinsic):
    """
    Convert masked RGB and depth images to a colored point cloud.
    
    Args:
        rgb_image (np.ndarray): RGB image
        depth_image (np.ndarray): Depth image
        mask (np.ndarray): Binary mask of the object
        
    Returns:
        o3d.geometry.PointCloud: Colored point cloud of the object
    """
    # set o3d intrinsics
    o3d_intrinsic = o3d.camera.PinholeCameraIntrinsic()
    o3d_intrinsic.intrinsic_matrix = intrinsic

    # Apply mask to the RGB and depth images
    masked_rgb = rgb_image.copy()
    masked_depth = depth_image.copy()
    
    # Set background to black in RGB and 0 in depth
    for i in range(3):  # For each RGB channel
        masked_rgb[:, :, i] = np.where(mask, masked_rgb[:, :, i], 0)
    
    masked_depth = np.where(mask, masked_depth, 0)
    
    # Convert to Open3D format
    rgb_o3d = o3d.geometry.Image(masked_rgb)
    depth_o3d = o3d.geometry.Image(masked_depth)
    
    # Create RGBD image
    rgbd_image = o3d.geometry.RGBDImage.create_from_color_and_depth(
        rgb_o3d, depth_o3d, 
        depth_scale=1,  # Adjust based on the depth scale (mm or m) mm: 1000, m: 1
        depth_trunc=5.0,     # Maximum depth in meters
        convert_rgb_to_intensity=False
    )
    
    # Create point cloud from RGBD image
    pcd = o3d.geometry.PointCloud.create_from_rgbd_image(
        rgbd_image, o3d_intrinsic
    )
    
    # Remove zero points (where depth was 0)
    points = np.asarray(pcd.points)
    colors = np.asarray(pcd.colors)
    
    # Find points with non-zero coordinates (sum of absolute values > 0)
    valid_indices = np.where(np.abs(points).sum(axis=1) > 0)[0]
    
    # Create a new point cloud with only valid points
    object_pcd = o3d.geometry.PointCloud()
    object_pcd.points = o3d.utility.Vector3dVector(points[valid_indices])
    object_pcd.colors = o3d.utility.Vector3dVector(colors[valid_indices])

    # save object pcd
    #o3d.io.write_point_cloud("../data/template_v3/masked_point_cloud.ply", object_pcd)

    # --- filter Preprocessing ---
    pcd_filtered = copy.deepcopy(object_pcd)
    # Consider applying preprocessing before further processing if needed
   # pcd, ind = pcd_filtered.remove_statistical_outlier(nb_neighbors=50, std_ratio=1.5)
    # pcd, ind = pcd_filtered.remove_radius_outlier(nb_points=100, radius=0.02)
    import time 
    st = time.time()
    # from collections import Counter
    # eps         = 0.02   # 半径阈值，单位与点云坐标一致
    # min_points  = 100     # 半径内最少点数才算同簇
    # labels = np.array(pcd.cluster_dbscan(eps=eps, min_points=min_points, print_progress=False))
    # mask        = labels >= 0
    # major_label = Counter(labels[mask]).most_common(1)[0][0]
    # pcd_major = pcd.select_by_index(np.where(labels == major_label)[0])
    pcd, ind = pcd_filtered.remove_radius_outlier(nb_points=500, radius=0.02)
    dt = time.time() - st
    print(f"removing radius outliers takes time: {dt}")

    #o3d.io.write_point_cloud("../data/template_v3/filtered_point_cloud_remove_redius_outlier_500_002.ply", pcd)

    return pcd

if __name__ == "__main__":
    image_path = "../data/template_v3/color.png"
    depth_path = "../data/template_v3/depth.png"
    mask_path = "../data/template_v3/mask.png"
    # read rgb image
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

    # read depth image
    depth = cv2.imread(depth_path, cv2.IMREAD_UNCHANGED)
    depth = depth.astype(np.float32) / 1000   # already np.float32
    # depth = depth  # scale is meter, don't / 1000

    # read mask image
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    # mask = np.ones(image.shape[:2], dtype=np.uint8) * 255

    # build camera intrinsic matrix - color
    # intrinsic = np.array([
    #     [1577.85595703, 0, 717.44384765],
    #     [0, 1578.40002441, 565.813415527],
    #     [0, 0, 1]   
    # ]) # RVC left instrinsic
    intrinsic = np.array([
        [1857.92529296875, 0.0, 1267.4947509765625],
        [0.0, 1857.943359375, 943.3340454101562],
        [0, 0, 1] 
    ]) # pcam color instrinsic
    # mask to point cloud
    object_pcd = mask_to_point_cloud(image, depth, mask, intrinsic)
    print("done!")