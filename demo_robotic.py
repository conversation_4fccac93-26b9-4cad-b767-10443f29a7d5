"""
Refactored demo using URRobotController class.

This demo shows how to use the new URRobotController class for
basic robot operations including gripper control and movement.
"""

from robo_ctl.ur_robot_controller import URRobotController
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)

def main():
    """Main demo function using the refactored robot controller."""
    
    # Robot configuration
    ROBOT_HOST = "*************"  # UR - left arm
    
    # Create robot controller using context manager for automatic cleanup
    try:
        with URRobotController(ROBOT_HOST) as robot:
            print("=== Robot Controller Demo ===")
            
            # Get current TCP pose
            current_pose = robot.get_tcp_pose()
            if current_pose:
                print(f"Current TCP Pose: {current_pose}")
            
            # Activate and open gripper
            print("Activating gripper...")
            if robot.activate_gripper():
                print("✓ Gripper activated successfully")
            else:
                print("✗ Gripper activation failed")
                return
            
            print("Opening gripper...")
            if robot.open_gripper():
                print("✓ Gripper opened successfully")
            else:
                print("✗ Gripper open failed")
            
            # Configure gripper settings
            robot.set_gripper_speed(50)  # 50% speed
            robot.set_gripper_force(50)  # 50% force
            
            # Example movement - modify the current pose
            if current_pose:
                # Create a target pose by modifying current position
                target_pose = current_pose.copy()
                # target_pose[1] -= 0.007  # Move slightly in Y direction
                # target_pose[2] -= 0.001   # Move up in Z direction
                target_pose[3] = 3.142 # rot vector + 弧度
                target_pose[4] = 0.0
                target_pose[5] = -1.571
                
                print(f"Moving to target pose: {target_pose}")
                
                # Movement parameters
                velocity = 0.05      # m/s
                acceleration = 0.01  # m/s²
                
                # Execute linear movement
                if robot.move_linear(target_pose, velocity, acceleration):
                    print("✓ Movement completed successfully")
                else:
                    print("✗ Movement failed")
                
                # Get new TCP pose after movement
                new_pose = robot.get_tcp_pose()
                if new_pose:
                    print(f"New TCP Pose: {new_pose}")
            
            # Close gripper
            print("Closing gripper...")
            if robot.close_gripper():
                print("✓ Gripper closed successfully")
            else:
                print("✗ Gripper close failed")
            
            print("=== Demo completed ===")
            
    except ConnectionError as e:
        print(f"Failed to connect to robot: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

def advanced_demo():
    """
    Advanced demo showing additional features.
    """
    ROBOT_HOST = "*************"
    
    try:
        robot = URRobotController(ROBOT_HOST)
        
        print("=== Advanced Robot Operations ===")
        
        # Get joint positions
        joints = robot.get_joint_positions()
        if joints:
            print(f"Current joint positions (rad): {joints}")
        
        # Check connection status
        print(f"Robot connected: {robot.is_connected()}")
        
        # Example of error handling - invalid pose
        try:
            invalid_pose = [1, 2, 3]  # Invalid pose with only 3 elements
            robot.move_linear(invalid_pose, 0.01, 0.01)
        except ValueError as e:
            print(f"Caught expected error: {e}")
        
        # Cleanup
        robot.disconnect()
        print("Robot disconnected manually")
        
    except Exception as e:
        print(f"Error in advanced demo: {e}")

if __name__ == "__main__":
    # Run basic demo
    main()
    
    print("\n" + "="*50 + "\n")
    
    # Run advanced demo
    advanced_demo() 