import numpy as np
from robo_ctl.ur_robot_controller import URRobotController

def to_init_position():
    with URRobotController("192.168.25.15") as robot:
        # Go to init position
        cur_pose = robot.get_tcp_pose()
        cur_pose[0] -= 0.1
        robot.move_linear(cur_pose, 3, 0.1)
        
        init_position_axis = np.array([0.35969, -0.08322, 0.55705, 2.315, -2.401, 0.159])
        robot.move_linear(init_position_axis, 3, 0.1)
        print("GO TO INIT POSITION completed")
        robot.disconnect()


if __name__ == "__main__":
   to_init_position()