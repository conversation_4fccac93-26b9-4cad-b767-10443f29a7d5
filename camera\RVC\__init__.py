"""
RVC Camera Package

A comprehensive Python package for RVC X1/X2 cameras that provides:
- Simplified camera initialization and configuration
- Easy data capture and processing
- Automatic resource management
- Comprehensive error handling

Author: Jack
Date: 2025-07-23
"""

from .rvc_camera_wrapper import (
    RVCCameraWrapper,
    CameraType,
    CameraID,
    quick_capture_x2,
    quick_capture_x1
)

__version__ = "1.0.0"
__author__ = "Jack"
__email__ = ""
__description__ = "RVC Camera Wrapper for simplified camera operations"

__all__ = [
    "RVCCameraWrapper",
    "CameraType", 
    "CameraID",
    "quick_capture_x2",
    "quick_capture_x1"
]
