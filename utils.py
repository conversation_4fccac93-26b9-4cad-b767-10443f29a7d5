import numpy as np
from scipy.spatial.transform import Rotation as R

import matplotlib.pyplot as plt
import PIL.Image

def pose_6d_to_matrix(pose_6d, unit='m', degrees=True):
    """
    Convert a 6D pose vector (x, y, z, rx, ry, rz) to a 4x4 homogeneous transformation matrix.

    Args:
        pose_6d (list or np.ndarray): 6D pose vector [x, y, z, rx, ry, rz].
            - x, y, z: Translation components (unit: meters or millimeters, specified by `unit`).
            - rx, ry, rz: Rotation components (XYZ Euler angles).
        unit (str): Unit of translation components ('m' for meters, 'mm' for millimeters).
        degrees (bool): Whether rotation components are in degrees (True) or radians (False).

    Returns:
        np.ndarray: 4x4 homogeneous transformation matrix.

    Raises:
        ValueError: If input is invalid (e.g., not a 6D vector or invalid unit).

    Example:
        >>> pose_6d = [1.0, 2.0, 3.0, 30, 45, 60]
        >>> pose_6d_to_matrix(pose_6d, unit='m', degrees=True)
    """
    if len(pose_6d) != 6:
        raise ValueError("Input pose_6d must be a 6D vector [x, y, z, rx, ry, rz].")

    x, y, z, rx, ry, rz = pose_6d

    # Convert translation units if necessary
    if unit == 'mm':
        x, y, z = x / 1000.0, y / 1000.0, z / 1000.0
    elif unit != 'm':
        raise ValueError("Invalid unit. Use 'm' for meters or 'mm' for millimeters.")

    # Convert Euler angles to rotation matrix
    rotation = R.from_euler('xyz', [rx, ry, rz], degrees=degrees).as_matrix()

    # Construct 4x4 transformation matrix
    transform = np.eye(4)
    transform[:3, :3] = rotation
    transform[:3, 3] = [x, y, z]

    return transform

def matrix_to_pose_6d(matrix, unit='m', degrees=True):
    """
    Convert a 4x4 homogeneous transformation matrix to a 6D pose vector (x, y, z, rx, ry, rz).

    Args:
        matrix (np.ndarray): 4x4 homogeneous transformation matrix.
        unit (str): Unit of translation components ('m' for meters, 'mm' for millimeters).
        degrees (bool): Whether rotation components should be in degrees (True) or radians (False).

    Returns:
        np.ndarray: 6D pose vector [x, y, z, rx, ry, rz].
            - x, y, z: Translation components (unit: meters or millimeters, specified by `unit`).
            - rx, ry, rz: Rotation components (XYZ Euler angles).

    Raises:
        ValueError: If input is not a valid 4x4 matrix.

    Example:
        >>> matrix = np.array([[1, 0, 0, 1.0],
                              [0, 1, 0, 2.0],
                              [0, 0, 1, 3.0],
                              [0, 0, 0, 1]])
        >>> matrix_to_pose_6d(matrix, unit='m', degrees=True)
    """
    if matrix.shape != (4, 4):
        raise ValueError("Input must be a 4x4 matrix.")

    # Extract translation components
    x, y, z = matrix[:3, 3]

    # Convert translation units if necessary
    if unit == 'mm':
        x, y, z = x * 1000.0, y * 1000.0, z * 1000.0
    elif unit != 'm':
        raise ValueError("Invalid unit. Use 'm' for meters or 'mm' for millimeters.")

    # Extract rotation matrix and convert to Euler angles
    rotation = matrix[:3, :3]
    rx, ry, rz = R.from_matrix(rotation).as_euler('xyz', degrees=degrees)

    return np.array([x, y, z, rx, ry, rz])


def matrix_to_axis_6d(matrix, unit='m'):
    """
    Convert 4x4 transformation matrix to 6D pose representation.
    
    Args:
        matrix: 4x4 numpy array representing the transformation matrix.
        unit: Unit of translation components ('m' for meters, 'mm' for millimeters).
    Returns:
        axis_6d: 6-element array [x, y, z, rx*θ, ry*θ, rz*θ].
    """
    if matrix.shape != (4, 4):
        raise ValueError("Input must be a 4x4 transformation matrix.")
    
    # Extract translation components
    x, y, z = matrix[:3, 3]

    # Convert translation units if necessary
    if unit == 'mm':
        x, y, z = x * 1000.0, y * 1000.0, z * 1000.0
    elif unit != 'm':
        raise ValueError("Invalid unit. Use 'm' for meters or 'mm' for millimeters.")
    
    
    rotation_vector = R.from_matrix(matrix[:3, :3]).as_rotvec()
    
    return np.array([x, y, z, rotation_vector[0], rotation_vector[1], rotation_vector[2]])


def subplot_notick(a, b, c):
    """Create subplot without ticks"""
    ax = plt.subplot(a, b, c)
    ax.set_xticklabels([])
    ax.set_yticklabels([])
    ax.axis('off')


def bbox2points(bbox):
    """Convert bounding box to points for SAM"""
    points = np.array([
        [bbox[0], bbox[1]],  # top-left
        [bbox[2], bbox[3]]   # bottom-right
    ])

    point_labels = np.array([2, 3])  # 2=Bounding box top-left, 3=Bounding box bottom-right
    return points, point_labels


def draw_bbox(bbox, color='g'):
    """Draw bounding box on the plot"""
    x = [bbox[0], bbox[2], bbox[2], bbox[0], bbox[0]]
    y = [bbox[1], bbox[1], bbox[3], bbox[3], bbox[1]]
    plt.plot(x, y, color + '-')


if __name__ == "__main__":
    pose_6d = [1.0, 2.0, 3.0, 30, 45, 60]
    np.set_printoptions(precision=3, suppress=True, linewidth=100)
    matrix = pose_6d_to_matrix(pose_6d, unit='m', degrees=True)
    print(matrix)
    pose_6d_re = matrix_to_pose_6d(matrix, unit='m', degrees=True)
    print(pose_6d_re)

   #  position_take_pic_euler = np.array([0.76996, -0.25386, 0.36307, -180.0, -0.0, -90.0])
    position_take_pic_euler = np.array([769.96, -253.86, 363.07, 180.0, -0.0, -90.0])
    position_take_pic_matrix = pose_6d_to_matrix(position_take_pic_euler, unit='mm', degrees=True)
    position_take_pic_axis = matrix_to_axis_6d(position_take_pic_matrix, unit='mm')
    print(f"axis: {position_take_pic_axis}")