#! /usr/bin/env python
# coding=utf-8
"""
AuboController

一个对 Aubo Python SDK(pyaubo_sdk) 的轻量封装，提供更易用的直线/圆弧运动、
速度管理与位置查询接口，便于二次开发。

功能:
- 连接/登录 RPC
- 速度配置缓存（工具端/关节速度和加速度）与设置接口
- 直线运动 moveLine（支持从当前位姿到目标，或先到起点再到终点）
- 获取当前关节角与 TCP 位姿
- 可选阻塞等待运动完成（基于 execId 轮询）

注意:
- SDK 并未提供“全局速度设置”接口。速度/加速度通常作为运动指令参数传入。
  本类在内部缓存“默认速度参数”，并在每次下发运动时使用或被方法参数覆盖。
- 速度单位请遵循示例。示例中将角度单位统一转换为弧度，如 250*(pi/180)。
- 使用前请确保机械臂已上电、解锁，且处于可运动状态（可参考 example_startup.py）。
"""
from __future__ import annotations

import time
import math
from typing import List, Optional, Tuple

import pyaubo_sdk


M_PI = 3.14159265358979323846


class AuboController:
    def __init__(self, ip: str = "127.0.0.1", port: int = 30004,
                 username: str = "aubo", password: str = "123") -> None:
        self.ip = ip
        self.port = port
        self.username = username
        self.password = password

        self._client = pyaubo_sdk.RpcClient()
        self.robot_name: Optional[str] = None

        # 速度参数缓存（默认从 RobotConfig 读取，可被 set_* 覆盖）
        self.tool_speed: Optional[float] = None   # TCP 速度（示例以弧度制传参）
        self.tool_acc: Optional[float] = None     # TCP 加速度
        self.joint_speed: Optional[float] = None  # 关节速度（rad/s）
        self.joint_acc: Optional[float] = None    # 关节加速度（rad/s^2）

    # ---------- 连接/登录 ----------
    def connect(self, login: bool = True) -> bool:
        """连接 RPC（可选自动登录）。成功返回 True。"""
        self._client.connect(self.ip, self.port)
        if not self._client.hasConnected():
            return False
        if login:
            return self.login(self.username, self.password)
        return True

    def login(self, username: str, password: str) -> bool:
        self._client.login(username, password)
        if not self._client.hasLogined():
            return False
        # 选择第一台机器人
        names = self._client.getRobotNames()
        if not names:
            return False
        self.robot_name = names[0]
        # 初始化速度缓存
        self._init_default_speeds()
        return True

    def _assert_ready(self) -> None:
        if not self._client.hasConnected():
            raise RuntimeError("RPC 未连接")
        if not self._client.hasLogined():
            raise RuntimeError("RPC 未登录")
        if not self.robot_name:
            raise RuntimeError("未获取到机器人名称")

    # ---------- 速度相关 ----------
    def _init_default_speeds(self) -> None:
        """从 RobotConfig 读取默认速度参数，作为本类的初始速度。"""
        try:
            iface = self._client.getRobotInterface(self.robot_name)
            cfg = iface.getRobotConfig()
            # 注意: 示例中线速度/加速度与关节速度/加速度单位均以 rad/s、rad/s^2 传参示范
            self.tool_speed = cfg.getDefaultToolSpeed()
            self.tool_acc = cfg.getDefaultToolAcc()
            self.joint_speed = cfg.getDefaultJointSpeed()
            self.joint_acc = cfg.getDefaultJointAcc()
            print(f"tool_speed: {self.tool_speed}")
            print(f"tool_acc: {self.tool_acc}")
        except Exception:
            # 出错时保持为 None，调用运动接口时需提供或回退到示例默认值
            pass

    def set_tool_speed_acc(self, speed: float, acc: float) -> None:
        self.tool_speed = float(speed)
        self.tool_acc = float(acc)

    def set_joint_speed_acc(self, speed: float, acc: float) -> None:
        self.joint_speed = float(speed)
        self.joint_acc = float(acc)

    # ---------- 状态获取 ----------
    def get_joint_positions(self) -> List[float]:
        self._assert_ready()
        return self._client.getRobotInterface(self.robot_name).getRobotState().getJointPositions()

    def get_tcp_pose(self) -> List[float]:
        self._assert_ready()
        return self._client.getRobotInterface(self.robot_name).getRobotState().getTcpPose()

    # ---------- 运行期控制 ----------
    def start_runtime(self) -> None:
        """开始规划期（示例在发运动前调用）。"""
        self._client.getRuntimeMachine().start()

    def stop_runtime(self) -> None:
        self._client.getRuntimeMachine().stop()

    # ---------- 同步等待 ----------
    def wait_for_motion(self, robot_interface) -> int:
        """阻塞等待当前运动完成，基于 execId 轮询。失败返回 -1，成功返回 0。"""
        cnt = 0
        while robot_interface.getMotionControl().getExecId() == -1:
            cnt += 1
            if cnt > 5:
                print("Motion fail!")
                return -1
            time.sleep(0.05)
            print("getExecId: ", robot_interface.getMotionControl().getExecId())
        id = robot_interface.getMotionControl().getExecId()
        while True:
            id1 = robot_interface.getMotionControl().getExecId()
            if id != id1:
                break
            time.sleep(0.05)
        return 0

    # ---------- 运动指令 ----------
    def execute_motion_sequence(self, motion_func, *args, **kwargs) -> int:
        """执行运动序列，管理runtime machine的生命周期。
        根据示例模式，在整个运动序列开始时start()，结束时stop()。
        """
        self.start_runtime()
        try:
            return motion_func(*args, **kwargs)
        finally:
            self.stop_runtime()
            
    def moveJoint(self, q: List[float],
                   speed: Optional[float] = None,
                   acc: Optional[float] = None,
                   blend_radius: float = 0.0,
                   ref: int = 0,
                   managed_runtime: bool = True) -> int:
        """关节运动封装。
        参数含义遵循示例：speed/acc 以 rad/s、rad/s^2 传入；blend 与参考系使用默认值。
        managed_runtime: 是否自动管理runtime machine的start/stop。
        运动总是阻塞等待完成。
        返回 SDK 返回码，0 为成功。
        """
        if managed_runtime:
            return self.execute_motion_sequence(self._moveJoint_internal, q, speed, acc, blend_radius, ref)
        else:
            return self._moveJoint_internal(q, speed, acc, blend_radius, ref)
            
    def _moveJoint_internal(self, q: List[float], speed: Optional[float], acc: Optional[float], 
                           blend_radius: float, ref: int) -> int:
        """内部关节运动实现，不管理runtime machine。"""
        self._assert_ready()
        spd = float(speed if speed is not None else (self.joint_speed if self.joint_speed is not None else 180 * (M_PI / 180)))
        ac = float(acc if acc is not None else (self.joint_acc if self.joint_acc is not None else 1000000 * (M_PI / 180)))

        robot_interface = self._client.getRobotInterface(self.robot_name)
        ret = robot_interface.getMotionControl().moveJoint(q, spd, ac, blend_radius, ref)
        
        wait_result = self.wait_for_motion(robot_interface)
        if wait_result == -1:
            return -1
        return ret

    def _validate_pose_with_inverse_kinematics(self, robot_interface, pose: List[float]) -> bool:
        """验证位姿的逆运动学解，根据example_movel.py的逻辑实现。"""
        cnt = 0
        while True:
            cur = robot_interface.getRobotState().getJointPositions()
            inverse_result = robot_interface.getRobotAlgorithm().inverseKinematics(cur, pose)
            if inverse_result[1] >= 0:
                return True
            cnt += 1
            if cnt > 10:
                return False
            time.sleep(0.01)

    def moveLine(self,
                  target_pose: List[float],
                  start_pose: Optional[List[float]] = None,
                  speed: Optional[float] = None,
                  acc: Optional[float] = None,
                  blend_radius: float = 0.0,
                  ref: int = 0,
                  managed_runtime: bool = True) -> int:
        """直线运动：
        - 若提供 start_pose，则先直线到 start_pose，再直线到 target_pose；
        - 否则从当前位置直线到 target_pose。
        严格按照 example_movel.py 的逻辑实现逆运动学验证。
        managed_runtime: 是否自动管理runtime machine的start/stop。
        运动总是阻塞等待完成。
        返回 SDK 返回码，0 为成功。
        """
        if managed_runtime:
            return self.execute_motion_sequence(self._moveLine_internal, target_pose, start_pose, 
                                                speed, acc, blend_radius, ref)
        else:
            return self._moveLine_internal(target_pose, start_pose, speed, acc, blend_radius, ref)
            
    def _moveLine_internal(self, target_pose: List[float], start_pose: Optional[List[float]], 
                          speed: Optional[float], acc: Optional[float], blend_radius: float, 
                          ref: int) -> int:
        """内部直线运动实现，不管理runtime machine。"""
        self._assert_ready()
        spd = float(speed if speed is not None else (self.tool_speed if self.tool_speed is not None else 250 * (M_PI / 180)))
        ac = float(acc if acc is not None else (self.tool_acc if self.tool_acc is not None else 1000 * (M_PI / 180)))

        robot_interface = self._client.getRobotInterface(self.robot_name)
        mc = robot_interface.getMotionControl()
        
        poses_to_move = []
        if start_pose is not None:
            poses_to_move.append(start_pose)
        poses_to_move.append(target_pose)
        
        for i, pose in enumerate(poses_to_move):
            print(f"Moving to {'start' if i == 0 and start_pose is not None else 'target'} pose...")
            
            ret = mc.moveLine(pose, spd, ac, blend_radius, ref)
            if ret != 0:
                return ret
                
            if not self._validate_pose_with_inverse_kinematics(robot_interface, pose):
                print(f"Inverse kinematics validation failed for pose {i}")
                return -1
                
            wait_result = self.wait_for_motion(robot_interface)
            if wait_result == -1:
                return -1
                    
        return 0


# ---------------------- 使用示例 ----------------------
if __name__ == '__main__':
    ctrl = AuboController(ip="*************", port=30004)
    if not ctrl.connect(login=True):
        print("连接或登录失败")
        raise SystemExit(1)
    print("连接并登录成功，机器人:", ctrl.robot_name)

    # 可选：调整速度
    ctrl.set_tool_speed_acc(0.5, 0.5)
    ctrl.set_joint_speed_acc(0.1, 0.1)

    # 读取当前状态
    try:
        print("当前关节角:", ctrl.get_joint_positions())
        print("当前TCP位姿:", ctrl.get_tcp_pose())
    except Exception as e:
        print("读取状态失败:", e)

    # ========== 详细使用示例 ==========
    
    # 示例1: 单次关节运动 (managed_runtime=True, 自动管理运行时)
    print("\n=== 示例1: 单次关节运动 ===")
    q_target = [-2.05177, -0.400292, 1.19625, 0.0285152, 1.57033, -2.28774]
    ret = ctrl.moveJoint(q_target, managed_runtime=True)
    print(f"moveJoint to {q_target}, ret: {ret}")
    
    # 示例2: 单次直线运动 (managed_runtime=True, 自动管理运行时)
    print("\n=== 示例2: 单次直线运动 ===")
    p1 = ctrl.get_tcp_pose()
    p1[2] += 0.05  # Z轴上移5cm
    print(f"Moving to pose: {p1}")
    ret = ctrl.moveLine(target_pose=p1, managed_runtime=True)
    print(f"moveLine ret: {ret}")
    
    # 示例3: 多点连续运动序列 (手动管理运行时, 效率更高)
    print("\n=== 示例3: 多点连续运动序列 ===")
    waypoints = [
        [-0.155944, -0.727344, 0.439066, 3.05165, 0.0324355, 1.80417],
        [-0.581143, -0.357548, 0.439066, 3.05165, 0.0324355, 1.80417], 
        [0.503502, -0.420646, 0.439066, 3.05165, 0.0324355, 1.80417]
    ]
    
    # 手动管理运行时生命周期
    ctrl.start_runtime()
    try:
        for i, waypoint in enumerate(waypoints):
            print(f"Moving to waypoint {i+1}: {waypoint}")
            ret = ctrl.moveLine(waypoint, managed_runtime=False)
            if ret != 0:
                print(f"运动失败，返回码: {ret}")
                break
            print(f"到达waypoint {i+1}")
    finally:
        ctrl.stop_runtime()
    
    # 示例4: 关节运动序列 (类似 example_movej.py)
    print("\n=== 示例4: 关节运动序列 ===")
    joint_waypoints = [
        [-2.05177, -0.400292, 1.19625, 0.0285152, 1.57033, -2.28774],
        [-0.236948, -0.465633, 1.1002, -0.00600456, 1.56824, -0.472864],
        [1.38167, -0.0115091, 1.66958, 0.107726, 1.572, 1.14576]
    ]
    
    ctrl.start_runtime()
    try:
        for i, q in enumerate(joint_waypoints):
            print(f"Moving to joint position {i+1}...")
            ret = ctrl.moveJoint(q, managed_runtime=False)
            if ret != 0:
                print(f"关节运动失败，返回码: {ret}")
                break
    finally:
        ctrl.stop_runtime()
    
    # 示例5: 带起点和终点的直线运动
    print("\n=== 示例5: 带起点和终点的直线运动 ===")
    start_pose = [-0.155944, -0.727344, 0.439066, 3.05165, 0.0324355, 1.80417]
    end_pose = [0.503502, -0.420646, 0.439066, 3.05165, 0.0324355, 1.80417]
    ret = ctrl.moveLine(target_pose=end_pose, start_pose=start_pose, 
                       managed_runtime=True)
    print(f"先到起点再到终点的直线运动, ret: {ret}")
    
    # 示例6: 自定义速度和加速度
    print("\n=== 示例6: 自定义速度运动 ===")
    current_pose = ctrl.get_tcp_pose()
    current_pose[0] += 0.1  # X轴移动10cm
    ret = ctrl.moveLine(current_pose, 
                       speed=100 * (M_PI / 180),  # 自定义线速度
                       acc=500 * (M_PI / 180),    # 自定义线加速度
                       managed_runtime=True)
    print(f"自定义速度运动, ret: {ret}")
    
    # 示例7: 循环运动 (类似 example_movel.py 的循环)
    print("\n=== 示例7: 循环运动 ===")
    # 先到起始关节位置
    q0 = [-2.40194, 0.103747, 1.7804, 0.108636, 1.57129, -2.6379]
    ret = ctrl.moveJoint(q0, managed_runtime=True)
    print(f"移动到起始关节位置, ret: {ret}")
    
    # 循环3次走3个路点
    loop_waypoints = [
        [-0.155944, -0.727344, 0.439066, 3.05165, 0.0324355, 1.80417],
        [-0.581143, -0.357548, 0.439066, 3.05165, 0.0324355, 1.80417],
        [0.503502, -0.420646, 0.439066, 3.05165, 0.0324355, 1.80417]
    ]
    
    for loop in range(3):
        print(f"\n--- 第 {loop+1} 轮循环 ---")
        ctrl.start_runtime()
        try:
            for i, waypoint in enumerate(loop_waypoints):
                print(f"Moving to p{i}...")
                ret = ctrl.moveLine(waypoint, managed_runtime=False)
                if ret != 0:
                    print(f"运动失败，退出循环")
                    break
        finally:
            ctrl.stop_runtime()
    
    print("\n=== 所有示例完成 ===")
    print("最终位置:")
    try:
        print("当前关节角:", ctrl.get_joint_positions())
        print("当前TCP位姿:", ctrl.get_tcp_pose())
    except Exception as e:
        print("读取最终状态失败:", e)

